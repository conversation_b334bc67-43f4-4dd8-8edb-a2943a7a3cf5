#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ella Robot Text 调试和问题排查示例

当遇到"所有robot_text元素都没有有效文本"的警告时，使用此脚本进行调试
"""

from pages.apps.ella.ella_response_handler import EllaResponseHandler
from core.logger import log


def debug_robot_text_issue(handler: EllaResponseHandler):
    """
    调试robot_text获取问题的完整流程
    
    Args:
        handler: EllaResponseHandler实例
    """
    print("=" * 60)
    print("🔍 开始调试robot_text获取问题")
    print("=" * 60)
    
    # 1. 获取详细的调试信息
    print("\n1️⃣ 获取页面中所有robot_text元素的详细信息...")
    debug_info = handler.debug_robot_text_elements()
    
    print(f"\n📊 调试结果摘要:")
    print(f"   总元素数: {debug_info['total_elements']}")
    print(f"   有文本的元素数: {debug_info['elements_with_text']}")
    print(f"   通过AI验证的文本数: {len(debug_info['ai_validated_texts'])}")
    
    # 2. 显示所有元素的详细信息
    print(f"\n📝 所有元素详细信息:")
    for element_info in debug_info['elements_details']:
        print(f"   元素{element_info['index']}:")
        print(f"     文本: '{element_info.get('text', 'N/A')}'")
        print(f"     长度: {element_info.get('text_length', 0)}")
        print(f"     是否为空: {element_info.get('is_empty', True)}")
        print(f"     AI验证: {element_info.get('is_ai_response', False)}")
        print(f"     有意义: {element_info.get('is_meaningful', False)}")
        if 'error' in element_info:
            print(f"     错误: {element_info['error']}")
        print()
    
    # 3. 尝试不同的获取方法
    print("\n2️⃣ 尝试不同的获取方法...")
    
    # 方法1: 标准方法（严格模式）
    print("\n🔸 方法1: 标准方法（严格AI验证）")
    response1 = handler.get_response_from_robot_text()
    print(f"   结果: '{response1}'")
    
    # 方法2: 宽松模式
    print("\n🔸 方法2: 宽松模式（不验证AI格式）")
    response2 = handler.get_response_from_robot_text_relaxed()
    print(f"   结果: '{response2}'")
    
    # 方法3: 获取所有响应
    print("\n🔸 方法3: 获取所有响应")
    all_responses = handler.get_all_robot_text_responses()
    print(f"   结果: {all_responses}")
    
    # 方法4: 增强的备用策略
    print("\n🔸 方法4: 增强的备用策略")
    response4 = handler.get_robot_text_with_fallback()
    print(f"   结果: '{response4}'")
    
    # 4. 分析问题并给出建议
    print("\n3️⃣ 问题分析和建议...")
    analyze_and_suggest(debug_info, response1, response2, all_responses, response4)


def analyze_and_suggest(debug_info, response1, response2, all_responses, response4):
    """
    分析调试结果并给出建议
    """
    print("\n📋 问题分析:")
    
    if debug_info['total_elements'] == 0:
        print("❌ 页面中没有找到robot_text元素")
        print("💡 建议:")
        print("   - 检查页面是否正确加载")
        print("   - 确认resource_id是否正确")
        print("   - 检查页面结构是否发生变化")
        
    elif debug_info['elements_with_text'] == 0:
        print("❌ 所有robot_text元素都没有文本内容")
        print("💡 建议:")
        print("   - 等待页面完全加载后再获取")
        print("   - 检查AI是否已经响应")
        print("   - 尝试触发AI响应后再获取")
        
    elif len(debug_info['ai_validated_texts']) == 0:
        print("❌ 有文本内容但都没有通过AI响应验证")
        print("💡 建议:")
        print("   - 使用宽松模式获取: get_response_from_robot_text_relaxed()")
        print("   - 检查AI响应验证规则是否过于严格")
        print("   - 查看实际文本内容，可能需要调整验证规则")
        
        print(f"\n📄 实际文本内容:")
        for i, text in enumerate(debug_info['all_texts'], 1):
            print(f"   {i}. '{text}'")
            
    else:
        print("✅ 找到了有效的AI响应文本")
        print(f"   通过验证的文本: {debug_info['ai_validated_texts']}")
    
    print(f"\n🎯 推荐的获取方法:")
    
    if response1:
        print("✅ 使用标准方法即可: get_response_from_robot_text()")
    elif response2:
        print("✅ 使用宽松模式: get_response_from_robot_text_relaxed()")
    elif response4:
        print("✅ 使用增强备用策略: get_robot_text_with_fallback()")
    elif all_responses:
        print("✅ 使用获取所有响应: get_all_robot_text_responses()")
        print(f"   然后从中选择: {all_responses}")
    else:
        print("❌ 所有方法都无法获取到有效内容")
        print("💡 建议:")
        print("   - 检查页面状态和AI响应状态")
        print("   - 尝试重新触发AI响应")
        print("   - 检查页面结构是否发生变化")


def main():
    """主函数示例"""
    print("Ella Robot Text 调试工具")
    print("注意: 这是一个示例脚本，需要实际的driver实例才能运行")
    
    # 示例用法（需要实际的driver实例）
    # from core.base_driver import driver_manager
    # handler = EllaResponseHandler(driver_manager.driver)
    # debug_robot_text_issue(handler)


if __name__ == "__main__":
    main()
