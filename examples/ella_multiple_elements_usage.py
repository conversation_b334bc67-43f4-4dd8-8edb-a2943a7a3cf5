#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ella多元素处理使用示例

演示如何使用优化后的方法处理页面中存在多个相同resource_id的元素
"""

from pages.apps.ella.ella_response_handler import EllaResponseHandler
from core.logger import log


def demo_multiple_robot_text_handling():
    """演示处理多个robot_text元素的不同方法"""
    
    # 假设已经初始化了handler
    handler = EllaResponseHandler(driver=None, status_checker=None)
    
    print("=== Ella多元素处理示例 ===\n")
    
    # 方法1: 使用优化后的单一响应获取（推荐）
    print("1. 使用优化后的get_response_from_robot_text方法:")
    print("   - 自动处理多个元素")
    print("   - 返回最新的有效响应")
    print("   - 代码示例:")
    print("     response = handler.get_response_from_robot_text()")
    print("     # 现在会自动处理多个robot_text元素\n")
    
    # 方法2: 获取所有元素的文本列表
    print("2. 获取所有robot_text元素的文本列表:")
    print("   - 返回所有匹配元素的文本")
    print("   - 可以自行选择需要的响应")
    print("   - 代码示例:")
    print("     all_responses = handler.get_all_robot_text_responses()")
    print("     for i, response in enumerate(all_responses):")
    print("         print(f'响应{i+1}: {response}')\n")
    
    # 方法3: 使用增强的备用策略方法
    print("3. 使用增强的备用策略方法:")
    print("   - 包含多种获取策略")
    print("   - 自动选择最佳响应")
    print("   - 代码示例:")
    print("     response = handler.get_robot_text_with_fallback()")
    print("     # 使用多种策略确保获取到响应\n")
    
    # 方法4: 通用的多元素处理方法
    print("4. 通用的多元素处理方法:")
    print("   - 可用于任何resource_id")
    print("   - 灵活的配置选项")
    print("   - 代码示例:")
    print("     all_texts = handler.get_all_elements_text(")
    print("         resource_id='com.transsion.aivoiceassistant:id/robot_text',")
    print("         element_name='robot_text',")
    print("         validate_ai_response=True")
    print("     )\n")


def demo_optimization_benefits():
    """演示优化的好处"""
    
    print("=== 优化带来的好处 ===\n")
    
    benefits = [
        "1. 自动检测多个元素: 当页面存在多个相同resource_id的元素时，自动处理",
        "2. 智能选择响应: 从多个元素中选择最新、最有效的响应文本",
        "3. 向后兼容: 原有的单元素处理逻辑保持不变",
        "4. 增强的错误处理: 更好的日志记录和异常处理",
        "5. 多种备用策略: 提供多种获取方法，提高成功率",
        "6. 灵活的配置: 可以选择是否启用多元素处理",
        "7. 性能优化: 避免重复获取，提高效率"
    ]
    
    for benefit in benefits:
        print(benefit)
    
    print("\n=== 使用建议 ===\n")
    
    suggestions = [
        "• 对于robot_text元素，推荐使用 get_response_from_robot_text() 方法",
        "• 如需获取所有响应，使用 get_all_robot_text_responses() 方法",
        "• 如需最大兼容性，使用 get_robot_text_with_fallback() 方法",
        "• 对于其他元素，可以在调用时设置 get_all_elements=True 参数",
        "• 在测试环境中，可以使用 get_all_elements_text() 查看所有元素内容"
    ]
    
    for suggestion in suggestions:
        print(suggestion)


def demo_error_scenarios():
    """演示错误场景的处理"""
    
    print("\n=== 错误场景处理 ===\n")
    
    scenarios = [
        "1. 元素不存在: 返回空字符串，记录警告日志",
        "2. 元素文本为空: 重试机制，最终返回空字符串",
        "3. 多个元素都无效: 尝试所有元素，返回空字符串",
        "4. 网络异常: 重试机制，记录详细错误信息",
        "5. 页面结构变化: 使用XML dump作为备用方案"
    ]
    
    for scenario in scenarios:
        print(scenario)


if __name__ == "__main__":
    demo_multiple_robot_text_handling()
    demo_optimization_benefits()
    demo_error_scenarios()
