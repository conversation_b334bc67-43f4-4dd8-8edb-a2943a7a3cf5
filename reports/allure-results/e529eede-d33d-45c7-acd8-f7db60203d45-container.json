{"uuid": "fd2d457a-efc2-49f0-b3d7-3c8bb784ecac", "children": ["d9938633-0ea5-4ea5-a07e-1ab42f682fc0"], "befores": [{"name": "ella_app", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>>(timeout=15)\n +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>.wait_for_page_load\n", "trace": "  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 38, in run_old_style_hookwrapper\n    res = yield\n          ^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    teardown.throw(exception)\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 53, in run_old_style_hookwrapper\n    return result.get_result()\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_result.py\", line 103, in get_result\n    raise exc.with_traceback(tb)\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 38, in run_old_style_hookwrapper\n    res = yield\n          ^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 139, in _multicall\n    teardown.throw(exception)\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 53, in run_old_style_hookwrapper\n    return result.get_result()\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_result.py\", line 103, in get_result\n    raise exc.with_traceback(tb)\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 38, in run_old_style_hookwrapper\n    res = yield\n          ^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 121, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 338, in ella_app\n    pytest.fail(f\"Ella应用启动异常: {e}\")\n  File \"D:\\PythonProject\\app_test\\.venv\\Lib\\site-packages\\_pytest\\outcomes.py\", line 198, in fail\n    raise Failed(msg=reason, pytrace=pytrace)\n"}, "start": 1755698692921, "stop": 1755698726883}], "start": 1755698692921, "stop": 1755698726913}