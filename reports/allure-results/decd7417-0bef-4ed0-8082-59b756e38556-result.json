{"name": "测试close aivana能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>>(timeout=15)\n +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>.wait_for_page_load", "trace": "self = <testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana object at 0x00000249E54CACD0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        # 在第一个测试用例开始前设置屏幕\n        self.setup_batch_test_screen()\n    \n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n>           assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\nE           AssertionError: Ella页面加载失败\nE           assert False\nE            +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>>(timeout=15)\nE            +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:331: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana object at 0x00000249E54CACD0>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        # 在第一个测试用例开始前设置屏幕\n        self.setup_batch_test_screen()\n    \n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella页面加载失败\nE           assert False\nE            +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>>(timeout=15)\nE            +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E5D5A2D0>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:338: Failed"}, "description": "close aivana", "attachments": [{"name": "stdout", "source": "503c0ab7-0307-48ca-a9d0-3f590ea61251-attachment.txt", "type": "text/plain"}], "start": 1755698658659, "stop": 1755698658659, "uuid": "7b7d10e2-3c68-41bd-862e-7482827d5cb2", "historyId": "d9f01ef1af79559082ce9e9b2e40295f", "testCaseId": "d9f01ef1af79559082ce9e9b2e40295f", "fullName": "testcases.test_ella.component_coupling.test_close_aivana.TestEllaCloseAivana#test_close_aivana", "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_aivana"}, {"name": "subSuite", "value": "TestEllaCloseAivana"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "43576-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_aivana"}]}