2025-08-20 22:04:52 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-20 22:04:53 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-20 22:04:53 | INFO | tools.adb_process_monitor:clear_all_running_processes:1825 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-20 22:04:53 | INFO | tools.adb_process_monitor:clear_all_running_processes:1841 | ⚡ 优先使用命令直接清理...
2025-08-20 22:04:55 | INFO | tools.adb_process_monitor:clear_all_running_processes:1847 | 💪 强制停止顽固应用...
2025-08-20 22:04:59 | INFO | tools.adb_process_monitor:clear_all_running_processes:1857 | 🎉 应用进程清理完成，共清理 30 个应用
2025-08-20 22:05:01 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-20 22:05:01 | INFO | pages.apps.ella.dialogue_page:start_app:214 | 启动Ella应用
2025-08-20 22:05:02 | INFO | pages.apps.ella.dialogue_page:start_app:222 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-20 22:05:05 | INFO | pages.apps.ella.dialogue_page:_check_app_started:280 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-20 22:05:05 | INFO | pages.apps.ella.dialogue_page:start_app:227 | ✅ Ella应用启动成功（指定Activity）
2025-08-20 22:05:05 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:301 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-20 22:05:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-08-20 22:05:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-08-20 22:05:20 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [输入框]
2025-08-20 22:05:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [文本输入框(备选)], 超时时间: 5秒
2025-08-20 22:05:25 | INFO | core.base_element:wait_for_element:65 | 元素列表 [False]
2025-08-20 22:05:25 | WARNING | core.base_element:wait_for_element:69 | 元素等待超时 [文本输入框(备选)]
2025-08-20 22:05:25 | ERROR | pages.apps.ella.dialogue_page:wait_for_page_load:311 | ❌ 页面加载超时，未找到输入框
2025-08-20 22:05:25 | ERROR | testcases.test_ella.base_ella_test:ella_app:337 | ❌ Ella应用启动异常: Ella页面加载失败
assert False
 +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>>(timeout=15)
 +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>.wait_for_page_load
2025-08-20 22:05:25 | INFO | pages.apps.ella.dialogue_page:stop_app:321 | 停止Ella应用
2025-08-20 22:05:26 | INFO | pages.apps.ella.dialogue_page:stop_app:332 | ✅ Ella应用已成功停止
