{"name": "测试close ella能正常执行", "status": "failed", "statusDetails": {"message": "Failed: Ella应用启动异常: Ella页面加载失败\nassert False\n +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>>(timeout=15)\n +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>.wait_for_page_load", "trace": "self = <testcases.test_ella.component_coupling.test_close_ella.TestEllaCloseElla object at 0x00000249E5D5A010>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        # 在第一个测试用例开始前设置屏幕\n        self.setup_batch_test_screen()\n    \n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n>           assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\nE           AssertionError: Ella页面加载失败\nE           assert False\nE            +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>>(timeout=15)\nE            +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:331: AssertionError\n\nDuring handling of the above exception, another exception occurred:\n\nself = <testcases.test_ella.component_coupling.test_close_ella.TestEllaCloseElla object at 0x00000249E5D5A010>\n\n    @pytest.fixture(scope=\"function\")\n    def ella_app(self):\n        \"\"\"简化的Ella应用fixture\"\"\"\n        # 在第一个测试用例开始前设置屏幕\n        self.setup_batch_test_screen()\n    \n        ella_page = EllaDialoguePage()\n    \n        try:\n            # 在启动应用前清除所有运行中的进程\n            self.clear_all_running_processes()\n    \n            # 启动应用\n            assert ella_page.start_app(), \"Ella应用启动失败\"\n            assert ella_page.wait_for_page_load(timeout=15), \"Ella页面加载失败\"\n    \n            log.info(\"✅ Ella应用启动成功\")\n            yield ella_page\n    \n        except Exception as e:\n            log.error(f\"❌ Ella应用启动异常: {e}\")\n>           pytest.fail(f\"Ella应用启动异常: {e}\")\nE           Failed: Ella应用启动异常: Ella页面加载失败\nE           assert False\nE            +  where False = <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>>(timeout=15)\nE            +    where <bound method EllaDialoguePage.wait_for_page_load of <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>> = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x00000249E8205210>.wait_for_page_load\n\ntestcases\\test_ella\\base_ella_test.py:338: Failed"}, "description": "close ella", "attachments": [{"name": "stdout", "source": "b4f39d20-4e39-4735-84ba-899c69a5d9f7-attachment.txt", "type": "text/plain"}], "start": 1755698692921, "stop": 1755698692921, "uuid": "d9938633-0ea5-4ea5-a07e-1ab42f682fc0", "historyId": "54b47105d42d2a9f18eec071fba40c73", "testCaseId": "54b47105d42d2a9f18eec071fba40c73", "fullName": "testcases.test_ella.component_coupling.test_close_ella.TestEllaCloseElla#test_close_ella", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "第三方集成"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.component_coupling"}, {"name": "suite", "value": "test_close_ella"}, {"name": "subSuite", "value": "TestEllaClose<PERSON>lla"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "43576-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.component_coupling.test_close_ella"}]}