{"timestamp": "2025-08-12 14:59:36", "current_app": "com.google.android.documentsui", "page_source": "<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>\r\n<hierarchy rotation=\"0\">\r\n  <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.transsion.smartpanel\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[1047,297][1080,657]\" drawing-order=\"0\" hint=\"\" display-id=\"0\">\r\n    <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.transsion.smartpanel\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[1047,297][1080,657]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n      <node index=\"0\" text=\"\" resource-id=\"com.transsion.smartpanel:id/floating_view\" class=\"android.widget.RelativeLayout\" package=\"com.transsion.smartpanel\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[1047,297][1080,657]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n        <node index=\"0\" text=\"\" resource-id=\"com.transsion.smartpanel:id/img_floating_view\" class=\"android.widget.ImageView\" package=\"com.transsion.smartpanel\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[1059,345][1080,609]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n      </node>\r\n    </node>\r\n  </node>\r\n  <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/navigation_bar_frame\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,2290][1080,2400]\" drawing-order=\"0\" hint=\"\" display-id=\"0\">\r\n    <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/navigation_bar_view\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,2290][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n      <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/navigation_inflater\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,2290][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n        <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/horizontal\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,2290][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n          <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/nav_buttons\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[22,2290][1058,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n            <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/ends_group\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[22,2290][1058,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n              <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.RelativeLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[22,2290][125,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n              <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.RelativeLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[125,2290][332,2400]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/recent_apps\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Overview\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"true\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[125,2290][332,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n              </node>\r\n              <node index=\"2\" text=\"\" resource-id=\"\" class=\"android.widget.RelativeLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[746,2290][954,2400]\" drawing-order=\"4\" hint=\"\" display-id=\"0\">\r\n                <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/back\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Back\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[746,2290][954,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n              </node>\r\n              <node index=\"3\" text=\"\" resource-id=\"\" class=\"android.widget.RelativeLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[954,2290][1058,2400]\" drawing-order=\"5\" hint=\"\" display-id=\"0\" />\r\n            </node>\r\n            <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/center_group\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[430,2290][650,2400]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n              <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/home\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Home\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"true\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[430,2290][650,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n            </node>\r\n          </node>\r\n        </node>\r\n      </node>\r\n    </node>\r\n  </node>\r\n  <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,2400]\" drawing-order=\"0\" hint=\"\" display-id=\"0\">\r\n    <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n      <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,2400]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n        <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/action_bar_root\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n          <node index=\"0\" text=\"\" resource-id=\"android:id/content\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n            <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/coordinator_layout\" class=\"android.view.ViewGroup\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n              <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/drawer_layout\" class=\"androidx.drawerlayout.widget.DrawerLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.ScrollView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"true\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                  <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/app_bar\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,552]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                    <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/collapsing_toolbar\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,552]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                      <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.ScrollView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,552]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                        <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/directory_header\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,255][1080,552]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                          <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.HorizontalScrollView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,255][1080,387]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                            <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,255][822,387]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                              <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/search_chip_group\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[55,255][767,387]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                                <node index=\"0\" text=\"Large files\" resource-id=\"\" class=\"android.widget.Button\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"true\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[66,255][402,387]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                                <node index=\"1\" text=\"This week\" resource-id=\"\" class=\"android.widget.Button\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"true\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[424,255][756,387]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                              </node>\r\n                            </node>\r\n                          </node>\r\n                          <node index=\"2\" text=\"\" resource-id=\"com.google.android.documentsui:id/header_container\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[66,387][1014,552]\" drawing-order=\"5\" hint=\"\" display-id=\"0\">\r\n                            <node index=\"0\" text=\"Recent files\" resource-id=\"com.google.android.documentsui:id/header_title\" class=\"android.widget.TextView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[66,387][882,552]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                            <node index=\"1\" text=\"\" resource-id=\"com.google.android.documentsui:id/sub_menu\" class=\"androidx.appcompat.widget.LinearLayoutCompat\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[882,403][1014,535]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                              <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/sub_menu_grid\" class=\"android.widget.Button\" package=\"com.google.android.documentsui\" content-desc=\"Grid view\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[882,403][1014,535]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                            </node>\r\n                          </node>\r\n                        </node>\r\n                      </node>\r\n                      <node index=\"1\" text=\"\" resource-id=\"com.google.android.documentsui:id/toolbar\" class=\"android.view.ViewGroup\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,255]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                        <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.ImageButton\" package=\"com.google.android.documentsui\" content-desc=\"Show roots\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][154,255]\" drawing-order=\"3\" hint=\"\" display-id=\"0\" />\r\n                        <node index=\"1\" text=\"Recent\" resource-id=\"\" class=\"android.widget.TextView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,154][388,223]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                        <node index=\"2\" text=\"\" resource-id=\"\" class=\"androidx.appcompat.widget.LinearLayoutCompat\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[816,123][1080,255]\" drawing-order=\"4\" hint=\"\" display-id=\"0\">\r\n                          <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/option_menu_search\" class=\"android.widget.Button\" package=\"com.google.android.documentsui\" content-desc=\"Search\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[816,123][948,255]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                          <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.google.android.documentsui\" content-desc=\"More options\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"true\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[948,123][1080,255]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                        </node>\r\n                      </node>\r\n                    </node>\r\n                  </node>\r\n                  <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                    <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/container_search_fragment\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                    <node index=\"1\" text=\"\" resource-id=\"com.google.android.documentsui:id/container_directory\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                      <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                        <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/refresh_layout\" class=\"android.view.ViewGroup\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                          <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/dir_list\" class=\"android.widget.GridView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][1080,2400]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                            <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/item_root\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,552][1080,753]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                              <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,552][1080,750]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                                <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/icon\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,552][198,750]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                                  <node index=\"0\" text=\"\" resource-id=\"\" class=\"androidx.cardview.widget.CardView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[44,596][154,706]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                                    <node index=\"0\" text=\"\" resource-id=\"com.google.android.documentsui:id/icon_thumb\" class=\"android.widget.ImageView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[44,596][154,706]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                                    <node index=\"2\" text=\"\" resource-id=\"com.google.android.documentsui:id/icon_mime\" class=\"android.widget.ImageView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[66,618][132,684]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                                  </node>\r\n                                </node>\r\n                                <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,596][838,705]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                                  <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,596][451,652]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                                    <node index=\"0\" text=\"bcy_doc.txt\" resource-id=\"android:id/title\" class=\"android.widget.TextView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,596][451,652]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                                  </node>\r\n                                  <node index=\"1\" text=\"\" resource-id=\"com.google.android.documentsui:id/line2\" class=\"android.widget.LinearLayout\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,663][838,705]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                                    <node index=\"0\" text=\"Aug 11, 2.58 kB, TXT document\" resource-id=\"com.google.android.documentsui:id/metadata\" class=\"android.widget.TextView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,663][692,705]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                                  </node>\r\n                                </node>\r\n                                <node index=\"2\" text=\"\" resource-id=\"com.google.android.documentsui:id/preview_icon\" class=\"android.widget.FrameLayout\" package=\"com.google.android.documentsui\" content-desc=\"Preview the file bcy_doc.txt\" checkable=\"false\" checked=\"false\" clickable=\"true\" enabled=\"true\" focusable=\"true\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[882,552][1080,750]\" drawing-order=\"3\" hint=\"\" display-id=\"0\">\r\n                                  <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[939,609][1022,692]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                                </node>\r\n                              </node>\r\n                              <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.view.View\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[198,750][1058,753]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                            </node>\r\n                          </node>\r\n                        </node>\r\n                      </node>\r\n                    </node>\r\n                    <node index=\"2\" text=\"\" resource-id=\"com.google.android.documentsui:id/drawer_edge\" class=\"android.view.View\" package=\"com.google.android.documentsui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,123][33,2400]\" drawing-order=\"3\" hint=\"\" display-id=\"0\" />\r\n                  </node>\r\n                </node>\r\n              </node>\r\n            </node>\r\n          </node>\r\n        </node>\r\n      </node>\r\n    </node>\r\n  </node>\r\n  <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,123]\" drawing-order=\"0\" hint=\"\" display-id=\"0\">\r\n    <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_launch_animation_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,123]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n    <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,123]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n      <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,123]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n        <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_contents\" class=\"android.widget.RelativeLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[36,21][1044,123]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n          <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_start_side_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[45,21][503,123]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n            <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_start_side_content\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[45,21][328,123]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n              <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_start_side_except_heads_up\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[45,21][328,123]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                <node index=\"0\" text=\"2:59\" resource-id=\"com.android.systemui:id/clock\" class=\"android.widget.TextView\" package=\"com.android.systemui\" content-desc=\"2:59 PM\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[45,21][135,123]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/notification_icon_area_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[135,21][328,123]\" drawing-order=\"4\" hint=\"\" display-id=\"0\">\r\n                  <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/notification_icon_area\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[135,21][328,123]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                    <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/notificationIcons\" class=\"android.view.ViewGroup\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[135,21][328,123]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                      <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Android Setup notification: \" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[144,53][181,90]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                      <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Android System notification: \" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[190,53][227,90]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                      <node index=\"2\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"My Health notification: \" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[236,53][273,90]\" drawing-order=\"3\" hint=\"\" display-id=\"0\" />\r\n                      <node index=\"3\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Clock notification: \" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[282,53][319,90]\" drawing-order=\"4\" hint=\"\" display-id=\"0\" />\r\n                    </node>\r\n                  </node>\r\n                </node>\r\n              </node>\r\n            </node>\r\n          </node>\r\n          <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/cutout_space_view\" class=\"android.view.View\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[503,21][576,123]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n          <node index=\"2\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_end_side_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[576,21][1035,123]\" drawing-order=\"3\" hint=\"\" display-id=\"0\">\r\n            <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_bar_end_side_content\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[741,21][1035,123]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n              <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/system_icons\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[741,39][1035,105]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/status_icons_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[749,39][929,105]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                  <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/airplane_container\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[749,39][914,105]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                  <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/statusIcons\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[749,39][914,105]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                    <node index=\"2\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Bluetooth on.\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[773,41][796,102]\" drawing-order=\"12\" hint=\"\" display-id=\"0\" />\r\n                    <node index=\"3\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"Do Not Disturb\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[811,41][843,102]\" drawing-order=\"18\" hint=\"\" display-id=\"0\" />\r\n                    <node index=\"4\" text=\"\" resource-id=\"com.android.systemui:id/mobile_combo\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"Phone four bars.\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[858,41][914,102]\" drawing-order=\"20\" hint=\"\" display-id=\"0\">\r\n                      <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/mobile_group\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[858,41][914,102]\" drawing-order=\"1\" hint=\"\" display-id=\"0\">\r\n                        <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/sim_container\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[858,49][914,93]\" drawing-order=\"4\" hint=\"\" display-id=\"0\">\r\n                          <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/mobile_type_container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[858,49][914,93]\" drawing-order=\"3\" hint=\"\" display-id=\"0\">\r\n                            <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[858,49][914,93]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                              <node index=\"0\" text=\"\" resource-id=\"com.android.systemui:id/mobile_in\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[858,49][869,93]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                              <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/mobile_signal\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[872,49][914,93]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                            </node>\r\n                            <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/mobile_type\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"5G\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[870,49][914,93]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                          </node>\r\n                        </node>\r\n                      </node>\r\n                    </node>\r\n                  </node>\r\n                </node>\r\n                <node index=\"1\" text=\"\" resource-id=\"com.android.systemui:id/battery\" class=\"android.widget.LinearLayout\" package=\"com.android.systemui\" content-desc=\"Battery charging, 88 percent.\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[929,39][1020,105]\" drawing-order=\"2\" hint=\"\" display-id=\"0\">\r\n                  <node index=\"0\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[929,55][996,89]\" drawing-order=\"1\" hint=\"\" display-id=\"0\" />\r\n                  <node index=\"1\" text=\"\" resource-id=\"\" class=\"android.widget.ImageView\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[1001,51][1020,92]\" drawing-order=\"2\" hint=\"\" display-id=\"0\" />\r\n                </node>\r\n              </node>\r\n            </node>\r\n          </node>\r\n        </node>\r\n      </node>\r\n    </node>\r\n    <node index=\"2\" text=\"\" resource-id=\"com.android.systemui:id/container\" class=\"android.widget.FrameLayout\" package=\"com.android.systemui\" content-desc=\"\" checkable=\"false\" checked=\"false\" clickable=\"false\" enabled=\"true\" focusable=\"false\" focused=\"false\" scrollable=\"false\" long-clickable=\"false\" password=\"false\" selected=\"false\" visible-to-user=\"true\" bounds=\"[0,0][1080,123]\" drawing-order=\"3\" hint=\"\" display-id=\"0\" />\r\n  </node>\r\n</hierarchy>", "elements": {"text_views": [{"index": 0, "text": "Recent files", "resource_id": null, "bounds": {"bottom": 552, "left": 66, "right": 882, "top": 387}, "clickable": false, "enabled": true}, {"index": 1, "text": "Recent", "resource_id": null, "bounds": {"bottom": 223, "left": 198, "right": 388, "top": 154}, "clickable": false, "enabled": true}, {"index": 2, "text": "bcy_doc.txt", "resource_id": null, "bounds": {"bottom": 652, "left": 198, "right": 451, "top": 596}, "clickable": false, "enabled": true}, {"index": 3, "text": "Aug 11, 2.58 kB, TXT document", "resource_id": null, "bounds": {"bottom": 705, "left": 198, "right": 692, "top": 663}, "clickable": false, "enabled": true}], "image_views": [{"index": 0, "resource_id": null, "bounds": {"bottom": 255, "left": 948, "right": 1080, "top": 123}, "width": 132, "height": 132, "clickable": true, "content_desc": "More options"}, {"index": 1, "resource_id": null, "bounds": {"bottom": 706, "left": 44, "right": 154, "top": 596}, "width": 110, "height": 110, "clickable": false, "content_desc": ""}, {"index": 2, "resource_id": null, "bounds": {"bottom": 684, "left": 66, "right": 132, "top": 618}, "width": 66, "height": 66, "clickable": false, "content_desc": ""}, {"index": 3, "resource_id": null, "bounds": {"bottom": 692, "left": 939, "right": 1022, "top": 609}, "width": 83, "height": 83, "clickable": false, "content_desc": ""}], "buttons": [{"index": 0, "text": "Large files", "resource_id": null, "bounds": {"bottom": 387, "left": 66, "right": 402, "top": 255}, "enabled": true}, {"index": 1, "text": "This week", "resource_id": null, "bounds": {"bottom": 387, "left": 424, "right": 756, "top": 255}, "enabled": true}, {"index": 2, "text": "", "resource_id": null, "bounds": {"bottom": 535, "left": 882, "right": 1014, "top": 403}, "enabled": true}, {"index": 3, "text": "", "resource_id": null, "bounds": {"bottom": 255, "left": 816, "right": 948, "top": 123}, "enabled": true}], "recycler_views": [], "linear_layouts": [{"index": 0, "resource_id": null, "bounds": {"bottom": 2400, "left": 0, "right": 1080, "top": 0}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 1, "resource_id": null, "bounds": {"bottom": 552, "left": 0, "right": 1080, "top": 123}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 2, "resource_id": null, "bounds": {"bottom": 552, "left": 0, "right": 1080, "top": 255}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 3, "resource_id": null, "bounds": {"bottom": 387, "left": 55, "right": 767, "top": 255}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 4, "resource_id": null, "bounds": {"bottom": 552, "left": 66, "right": 1014, "top": 387}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 5, "resource_id": null, "bounds": {"bottom": 2400, "left": 0, "right": 1080, "top": 123}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 6, "resource_id": null, "bounds": {"bottom": 753, "left": 0, "right": 1080, "top": 552}, "clickable": true, "has_text_child": true, "has_image_child": true}, {"index": 7, "resource_id": null, "bounds": {"bottom": 750, "left": 0, "right": 1080, "top": 552}, "clickable": false, "has_text_child": true, "has_image_child": true}, {"index": 8, "resource_id": null, "bounds": {"bottom": 705, "left": 198, "right": 838, "top": 596}, "clickable": false, "has_text_child": true, "has_image_child": false}, {"index": 9, "resource_id": null, "bounds": {"bottom": 652, "left": 198, "right": 451, "top": 596}, "clickable": false, "has_text_child": true, "has_image_child": false}], "clickable_elements": [{"index": 0, "class_name": "android.widget.Button", "text": "Large files", "resource_id": null, "bounds": {"bottom": 387, "left": 66, "right": 402, "top": 255}, "content_desc": ""}, {"index": 1, "class_name": "android.widget.Button", "text": "This week", "resource_id": null, "bounds": {"bottom": 387, "left": 424, "right": 756, "top": 255}, "content_desc": ""}, {"index": 2, "class_name": "android.widget.Button", "text": "", "resource_id": null, "bounds": {"bottom": 535, "left": 882, "right": 1014, "top": 403}, "content_desc": "Grid view"}, {"index": 3, "class_name": "android.widget.ImageButton", "text": "", "resource_id": null, "bounds": {"bottom": 255, "left": 0, "right": 154, "top": 123}, "content_desc": "Show roots"}, {"index": 4, "class_name": "android.widget.Button", "text": "", "resource_id": null, "bounds": {"bottom": 255, "left": 816, "right": 948, "top": 123}, "content_desc": "Search"}, {"index": 5, "class_name": "android.widget.ImageView", "text": "", "resource_id": null, "bounds": {"bottom": 255, "left": 948, "right": 1080, "top": 123}, "content_desc": "More options"}, {"index": 6, "class_name": "android.widget.LinearLayout", "text": "", "resource_id": null, "bounds": {"bottom": 753, "left": 0, "right": 1080, "top": 552}, "content_desc": ""}, {"index": 7, "class_name": "android.widget.FrameLayout", "text": "", "resource_id": null, "bounds": {"bottom": 750, "left": 882, "right": 1080, "top": 552}, "content_desc": "Preview the file bcy_doc.txt"}]}}