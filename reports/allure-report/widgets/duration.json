[{"uid": "7547d8dd682ea094", "name": "continue  screen recording能正常执行", "time": {"start": 1755540011356, "stop": 1755540036420, "duration": 25064}, "status": "passed", "severity": "critical"}, {"uid": "472b2f5bc7ebd088", "name": "测试increase the volume to the maximun能正常执行", "time": {"start": 1755538650413, "stop": 1755538672876, "duration": 22463}, "status": "passed", "severity": "critical"}, {"uid": "e82690830b99a38a", "name": "stop  screen recording能正常执行", "time": {"start": 1755540050262, "stop": 1755540077630, "duration": 27368}, "status": "passed", "severity": "critical"}, {"uid": "d987956ec23c66d0", "name": "测试screen record能正常执行", "time": {"start": 1755539384585, "stop": 1755539411294, "duration": 26709}, "status": "passed", "severity": "critical"}, {"uid": "bde5993ab80214c5", "name": "测试a clear and pink crystal necklace in the water", "time": {"start": 1755542631858, "stop": 1755542657387, "duration": 25529}, "status": "passed", "severity": "critical"}, {"uid": "a8d288b13eea8d3e", "name": "测试summarize content on this page", "time": {"start": 1755550035242, "stop": 1755550057418, "duration": 22176}, "status": "passed", "severity": "critical"}, {"uid": "3b0a2a501433e61e", "name": "测试play music by boomplay", "time": {"start": 1755535308816, "stop": 1755535333056, "duration": 24240}, "status": "passed", "severity": "critical"}, {"uid": "d69e193e35f210e0", "name": "测试where is my car能正常执行", "time": {"start": 1755551222229, "stop": 1755551244233, "duration": 22004}, "status": "passed", "severity": "critical"}, {"uid": "3006e468cff1526", "name": "测试how's the weather today?返回正确的不支持响应", "time": {"start": 1755534586604, "stop": 1755534615590, "duration": 28986}, "status": "passed", "severity": "normal"}, {"uid": "1e00a46747da8d89", "name": "测试how to set screenshots返回正确的不支持响应", "time": {"start": 1755545914505, "stop": 1755545936269, "duration": 21764}, "status": "passed", "severity": "normal"}, {"uid": "ef1df60b7ba75692", "name": "测试listen to fm能正常执行", "time": {"start": 1755534974838, "stop": 1755534996912, "duration": 22074}, "status": "passed", "severity": "critical"}, {"uid": "14dee738f79101ae", "name": "测试Generate a picture in the night forest for me", "time": {"start": 1755545061038, "stop": 1755545082942, "duration": 21904}, "status": "passed", "severity": "critical"}, {"uid": "df184aa14f08e16a", "name": "测试clear junk files命令", "time": {"start": 1755538080665, "stop": 1755538130030, "duration": 49365}, "status": "passed", "severity": "critical"}, {"uid": "2bb3ce4693c21548", "name": "测试turn up the brightness to the max能正常执行", "time": {"start": 1755541823865, "stop": 1755541846352, "duration": 22487}, "status": "passed", "severity": "critical"}, {"uid": "ef7a15af380820c6", "name": "测试download whatsapp能正常执行", "time": {"start": 1755544348925, "stop": 1755544376897, "duration": 27972}, "status": "passed", "severity": "critical"}, {"uid": "4b4b33da4101679c", "name": "测试close performance mode返回正确的不支持响应", "time": {"start": 1755543642589, "stop": 1755543664873, "duration": 22284}, "status": "passed", "severity": "normal"}, {"uid": "e99caa00c608f900", "name": "测试driving mode返回正确的不支持响应", "time": {"start": 1755544390869, "stop": 1755544413086, "duration": 22217}, "status": "failed", "severity": "normal"}, {"uid": "bf798a6e3d70daa5", "name": "测试it wears a red leather collar", "time": {"start": 1755546238351, "stop": 1755546264045, "duration": 25694}, "status": "passed", "severity": "critical"}, {"uid": "320a4646a2dfbba3", "name": "测试set alarm for 10 o'clock", "time": {"start": 1755539510703, "stop": 1755539534441, "duration": 23738}, "status": "failed", "severity": "critical"}, {"uid": "15cdef69b8beed3e", "name": "测试disable zonetouch master返回正确的不支持响应", "time": {"start": 1755544194307, "stop": 1755544216422, "duration": 22115}, "status": "passed", "severity": "normal"}, {"uid": "344438313a655b16", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "time": {"start": 1755546317774, "stop": 1755546339813, "duration": 22039}, "status": "passed", "severity": "normal"}, {"uid": "dd5f2e8b9cbf2bfa", "name": "测试what time is it in London能正常执行", "time": {"start": 1755551185946, "stop": 1755551208146, "duration": 22200}, "status": "passed", "severity": "critical"}, {"uid": "8be6bece5fd1af0d", "name": "测试call mom", "time": {"start": 1755542980983, "stop": 1755543012019, "duration": 31036}, "status": "failed", "severity": "critical"}, {"uid": "8e014ba05d1d3d20", "name": "测试play jay chou's music by spotify", "time": {"start": 1755533307222, "stop": 1755533331544, "duration": 24322}, "status": "passed", "severity": "critical"}, {"uid": "f1d7293de10d9144", "name": "测试record audio for 5 seconds能正常执行", "time": {"start": 1755533540018, "stop": 1755533565891, "duration": 25873}, "status": "passed", "severity": "critical"}, {"uid": "23689521abf187ca", "name": "测试change your language能正常执行", "time": {"start": 1755538002305, "stop": 1755538029474, "duration": 27169}, "status": "passed", "severity": "critical"}, {"uid": "70ea41509f71ae68", "name": "测试there are many yellow sunflowers on the ground", "time": {"start": 1755550440995, "stop": 1755550466694, "duration": 25699}, "status": "passed", "severity": "critical"}, {"uid": "d83d0ba4b2e5fcc3", "name": "测试the battery of the mobile phone is too low能正常执行", "time": {"start": 1755540738840, "stop": 1755540766433, "duration": 27593}, "status": "passed", "severity": "critical"}, {"uid": "3c858841add323a3", "name": "测试what date is it能正常执行", "time": {"start": 1755550923588, "stop": 1755550945795, "duration": 22207}, "status": "passed", "severity": "critical"}, {"uid": "5cb654923a5394e", "name": "测试memory cleanup能正常执行", "time": {"start": 1755538947324, "stop": 1755538995563, "duration": 48239}, "status": "passed", "severity": "critical"}, {"uid": "2c81a4a04cdc1071", "name": "测试disable auto pickup返回正确的不支持响应", "time": {"start": 1755543859540, "stop": 1755543881464, "duration": 21924}, "status": "passed", "severity": "normal"}, {"uid": "a764398584dbd337", "name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "time": {"start": 1755545761411, "stop": 1755545783561, "duration": 22150}, "status": "passed", "severity": "critical"}, {"uid": "ecc71f2f493e1262", "name": "测试check system update", "time": {"start": 1755543571254, "stop": 1755543593013, "duration": 21759}, "status": "failed", "severity": "critical"}, {"uid": "e68af3605869f6ab", "name": "测试why my charging is so slow能正常执行", "time": {"start": 1755536967108, "stop": 1755536988272, "duration": 21164}, "status": "failed", "severity": "critical"}, {"uid": "b81fc5f7f6d96890", "name": "测试i wanna be rich能正常执行", "time": {"start": 1755534741316, "stop": 1755534765733, "duration": 24417}, "status": "passed", "severity": "critical"}, {"uid": "937a6a1b80540fdf", "name": "测试turn on wifi能正常执行", "time": {"start": 1755541676941, "stop": 1755541700497, "duration": 23556}, "status": "passed", "severity": "critical"}, {"uid": "7f62336971efe5f8", "name": "测试open camera", "time": {"start": 1755547197681, "stop": 1755547238974, "duration": 41293}, "status": "passed", "severity": "critical"}, {"uid": "df6140718ba46854", "name": "测试set nfc tag", "time": {"start": 1755549241914, "stop": 1755549271257, "duration": 29343}, "status": "passed", "severity": "critical"}, {"uid": "9890369f3896e4c", "name": "测试make a call by whatsapp能正常执行", "time": {"start": 1755546772083, "stop": 1755546803639, "duration": 31556}, "status": "passed", "severity": "critical"}, {"uid": "7d126021ffb45811", "name": "测试search the address in the image能正常执行", "time": {"start": 1755548336283, "stop": 1755548362375, "duration": 26092}, "status": "passed", "severity": "critical"}, {"uid": "858c22d7630a2a7d", "name": "测试check mobile data balance of sim2返回正确的不支持响应", "time": {"start": 1755543354491, "stop": 1755543376726, "duration": 22235}, "status": "passed", "severity": "normal"}, {"uid": "e6acbfc61014ddb0", "name": "测试play news", "time": {"start": 1755535530009, "stop": 1755535557931, "duration": 27922}, "status": "passed", "severity": "critical"}, {"uid": "3177d325c06c0ced", "name": "测试help me generate a picture of a bamboo forest stream", "time": {"start": 1755545509327, "stop": 1755545531209, "duration": 21882}, "status": "passed", "severity": "critical"}, {"uid": "2b5c5da56c75ff4", "name": "测试set screen relay返回正确的不支持响应", "time": {"start": 1755549546096, "stop": 1755549568423, "duration": 22327}, "status": "passed", "severity": "normal"}, {"uid": "4997c4a01fffa2e1", "name": "测试minimum volume能正常执行", "time": {"start": 1755539155868, "stop": 1755539178585, "duration": 22717}, "status": "passed", "severity": "critical"}, {"uid": "3ff29417a9ae6010", "name": "测试i want to hear a joke能正常执行", "time": {"start": 1755546120102, "stop": 1755546145733, "duration": 25631}, "status": "passed", "severity": "critical"}, {"uid": "16fa33ac9f4fc28a", "name": "测试set smart hub返回正确的不支持响应", "time": {"start": 1755549693192, "stop": 1755549715674, "duration": 22482}, "status": "passed", "severity": "normal"}, {"uid": "89372ef1b45d48b4", "name": "测试can you give me a coin能正常执行", "time": {"start": 1755533999433, "stop": 1755534024121, "duration": 24688}, "status": "passed", "severity": "critical"}, {"uid": "abd00a5f90b2dab6", "name": "测试Enable Call Rejection返回正确的不支持响应", "time": {"start": 1755544616887, "stop": 1755544647919, "duration": 31032}, "status": "passed", "severity": "normal"}, {"uid": "25841d99901eab3", "name": "测试what is apec?能正常执行", "time": {"start": 1755536487501, "stop": 1755536513261, "duration": 25760}, "status": "passed", "severity": "critical"}, {"uid": "74799b443815fe47", "name": "测试maximum volume能正常执行", "time": {"start": 1755538910308, "stop": 1755538933258, "duration": 22950}, "status": "passed", "severity": "critical"}, {"uid": "acf343c73d15018b", "name": "测试turn up the volume to the max能正常执行", "time": {"start": 1755541859998, "stop": 1755541882503, "duration": 22505}, "status": "passed", "severity": "critical"}, {"uid": "15de6c1c1a1bde89", "name": "测试disable all ai magic box features返回正确的不支持响应", "time": {"start": 1755543823441, "stop": 1755543845293, "duration": 21852}, "status": "passed", "severity": "normal"}, {"uid": "6933b6fb12ae346d", "name": "测试i am your voice assistant", "time": {"start": 1755545950033, "stop": 1755545975296, "duration": 25263}, "status": "failed", "severity": "critical"}, {"uid": "fe991698e5267e29", "name": "测试continue music能正常执行", "time": {"start": 1755532440416, "stop": 1755532462076, "duration": 21660}, "status": "passed", "severity": "critical"}, {"uid": "859cd1ac4096dca4", "name": "测试close power saving mode返回正确的不支持响应", "time": {"start": 1755543678926, "stop": 1755543701149, "duration": 22223}, "status": "passed", "severity": "normal"}, {"uid": "878b77da97742d88", "name": "测试download basketball返回正确的不支持响应", "time": {"start": 1755544230502, "stop": 1755544252918, "duration": 22416}, "status": "failed", "severity": "normal"}, {"uid": "7d0c5927796b6392", "name": "测试turn on light theme能正常执行", "time": {"start": 1755541333638, "stop": 1755541355918, "duration": 22280}, "status": "passed", "severity": "critical"}, {"uid": "4f78c40977d29fd5", "name": "测试why is my phone not ringing on incoming calls能正常执行", "time": {"start": 1755536922602, "stop": 1755536953417, "duration": 30815}, "status": "passed", "severity": "critical"}, {"uid": "faf45958c476966a", "name": "测试Switch to Low-Temp Charge能正常执行", "time": {"start": 1755540479232, "stop": 1755540501365, "duration": 22133}, "status": "passed", "severity": "critical"}, {"uid": "a0520ca561c270a8", "name": "测试switch to power saving mode能正常执行", "time": {"start": 1755540515305, "stop": 1755540537560, "duration": 22255}, "status": "passed", "severity": "critical"}, {"uid": "93132b8d78ed6db", "name": "测试Scan the QR code in the image 能正常执行", "time": {"start": 1755537516454, "stop": 1755537633655, "duration": 117201}, "status": "passed", "severity": "critical"}, {"uid": "79a84d7137b558e3", "name": "测试delete the 8 o'clock alarm", "time": {"start": 1755532516620, "stop": 1755532538737, "duration": 22117}, "status": "passed", "severity": "critical"}, {"uid": "939cebe22906f8ea", "name": "测试cannot login in google email box能正常执行", "time": {"start": 1755534037938, "stop": 1755534060173, "duration": 22235}, "status": "passed", "severity": "critical"}, {"uid": "a216829f25a48ac0", "name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "time": {"start": 1755544915793, "stop": 1755544937936, "duration": 22143}, "status": "passed", "severity": "critical"}, {"uid": "d98e6b072f4e3cec", "name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "time": {"start": 1755545436695, "stop": 1755545458640, "duration": 21945}, "status": "passed", "severity": "critical"}, {"uid": "96d8d1a506ac9b9e", "name": "测试min alarm clock volume", "time": {"start": 1755539009642, "stop": 1755539032100, "duration": 22458}, "status": "passed", "severity": "critical"}, {"uid": "f8f62e26362f4acd", "name": "测试pause music能正常执行", "time": {"start": 1755533089991, "stop": 1755533112024, "duration": 22033}, "status": "passed", "severity": "critical"}, {"uid": "486198e7ca2f5671", "name": "测试view in notebook", "time": {"start": 1755550829705, "stop": 1755550864436, "duration": 34731}, "status": "passed", "severity": "critical"}, {"uid": "fd60a3ff320b3f36", "name": "测试pause fm能正常执行", "time": {"start": 1755533053460, "stop": 1755533075835, "duration": 22375}, "status": "passed", "severity": "critical"}, {"uid": "d93759f5df6d71b2", "name": "测试disable touch optimization返回正确的不支持响应", "time": {"start": 1755544122605, "stop": 1755544144741, "duration": 22136}, "status": "passed", "severity": "normal"}, {"uid": "f252f9db319d4be8", "name": "测试navigation to the first address in the image能正常执行", "time": {"start": 1755547073652, "stop": 1755547105068, "duration": 31416}, "status": "broken", "severity": "critical"}, {"uid": "245590744fd1760c", "name": "测试where is the carlcare service outlet能正常执行", "time": {"start": 1755541932633, "stop": 1755541964641, "duration": 32008}, "status": "passed", "severity": "critical"}, {"uid": "817201f101a3fce8", "name": "测试next song能正常执行", "time": {"start": 1755535162990, "stop": 1755535185150, "duration": 22160}, "status": "passed", "severity": "critical"}, {"uid": "3c7c9f887c88cd32", "name": "测试turn on light theme能正常执行", "time": {"start": 1755541369911, "stop": 1755541391953, "duration": 22042}, "status": "passed", "severity": "critical"}, {"uid": "f3c3b82db6e5f74a", "name": "测试check status updates on whatsapp能正常执行", "time": {"start": 1755534074311, "stop": 1755534097644, "duration": 23333}, "status": "passed", "severity": "critical"}, {"uid": "6eb43be3ca854b83", "name": "测试set screen to minimum brightness返回正确的不支持响应", "time": {"start": 1755549619480, "stop": 1755549641974, "duration": 22494}, "status": "passed", "severity": "normal"}, {"uid": "dde17385f415640f", "name": "测试set ultra power saving返回正确的不支持响应", "time": {"start": 1755549918832, "stop": 1755549941568, "duration": 22736}, "status": "passed", "severity": "normal"}, {"uid": "febd4965bc5b5d32", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "time": {"start": 1755534418034, "stop": 1755534453910, "duration": 35876}, "status": "passed", "severity": "critical"}, {"uid": "ea971f8afc8ac3d8", "name": "测试set languages返回正确的不支持响应", "time": {"start": 1755549095869, "stop": 1755549118203, "duration": 22334}, "status": "passed", "severity": "normal"}, {"uid": "3e1ef9c9539919e2", "name": "测试turn down the brightness to the min能正常执行", "time": {"start": 1755540890128, "stop": 1755540912248, "duration": 22120}, "status": "passed", "severity": "critical"}, {"uid": "36bd15928d9b0905", "name": "测试global gdp trends能正常执行", "time": {"start": 1755534301066, "stop": 1755534326958, "duration": 25892}, "status": "passed", "severity": "critical"}, {"uid": "d907a6931b9bab45", "name": "测试open whatsapp", "time": {"start": 1755547463774, "stop": 1755547487298, "duration": 23524}, "status": "failed", "severity": "critical"}, {"uid": "23aff1aca63900f9", "name": "测试play video", "time": {"start": 1755547899549, "stop": 1755547927800, "duration": 28251}, "status": "passed", "severity": "critical"}, {"uid": "304d1991e7b7338d", "name": "测试privacy policy", "time": {"start": 1755548061813, "stop": 1755548084125, "duration": 22312}, "status": "passed", "severity": "critical"}, {"uid": "b255e6f959e5a001", "name": "测试adjustment the brightness to minimun能正常执行", "time": {"start": 1755537930409, "stop": 1755537952531, "duration": 22122}, "status": "passed", "severity": "critical"}, {"uid": "2c4695e673696dce", "name": "测试take a note on how to build a treehouse能正常执行", "time": {"start": 1755536252538, "stop": 1755536274482, "duration": 21944}, "status": "passed", "severity": "critical"}, {"uid": "11c915e41556cea3", "name": "测试jump to battery and power saving返回正确的不支持响应", "time": {"start": 1755546425673, "stop": 1755546447711, "duration": 22038}, "status": "passed", "severity": "normal"}, {"uid": "dc42dd62a92d5d19", "name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "time": {"start": 1755544951816, "stop": 1755544973951, "duration": 22135}, "status": "passed", "severity": "critical"}, {"uid": "3be2292986414830", "name": "测试the mobile phone is very hot", "time": {"start": 1755550404270, "stop": 1755550427074, "duration": 22804}, "status": "passed", "severity": "critical"}, {"uid": "3a4693475a14571f", "name": "测试turn off the 8 am alarm", "time": {"start": 1755533764116, "stop": 1755533786554, "duration": 22438}, "status": "passed", "severity": "critical"}, {"uid": "70ae52d960b93bbd", "name": "测试play political news", "time": {"start": 1755535571881, "stop": 1755535599049, "duration": 27168}, "status": "passed", "severity": "critical"}, {"uid": "f866a2bad0ee5662", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1755532931498, "stop": 1755532953979, "duration": 22481}, "status": "passed", "severity": "critical"}, {"uid": "3409f70c757d998f", "name": "测试Generate a picture of a jungle stream for me", "time": {"start": 1755545096743, "stop": 1755545118876, "duration": 22133}, "status": "passed", "severity": "critical"}, {"uid": "d5ebc6c67f353f03", "name": "测试extend the image能正常执行", "time": {"start": 1755544880054, "stop": 1755544901864, "duration": 21810}, "status": "failed", "severity": "critical"}, {"uid": "dc8175669360e0", "name": "测试enable brightness locking返回正确的不支持响应", "time": {"start": 1755544535387, "stop": 1755544557375, "duration": 21988}, "status": "passed", "severity": "normal"}, {"uid": "ee1adc8b75cf0025", "name": "测试play afro strut", "time": {"start": 1755533197292, "stop": 1755533240096, "duration": 42804}, "status": "passed", "severity": "critical"}, {"uid": "cfc600f66f56271b", "name": "测试set the alarm at 9 o'clock on weekends", "time": {"start": 1755539787019, "stop": 1755539810024, "duration": 23005}, "status": "passed", "severity": "critical"}, {"uid": "2e458485db602770", "name": "测试open whatsapp", "time": {"start": 1755542374615, "stop": 1755542398281, "duration": 23666}, "status": "passed", "severity": "critical"}, {"uid": "9b668cc2394b5bc4", "name": "测试turn down notifications volume能正常执行", "time": {"start": 1755540817193, "stop": 1755540839637, "duration": 22444}, "status": "passed", "severity": "critical"}, {"uid": "8b5755924feaa165", "name": "测试what's the date today", "time": {"start": 1755551004319, "stop": 1755551026729, "duration": 22410}, "status": "passed", "severity": "critical"}, {"uid": "409d638ac1bedc2", "name": "测试enable unfreeze返回正确的不支持响应", "time": {"start": 1755544772103, "stop": 1755544794095, "duration": 21992}, "status": "passed", "severity": "normal"}, {"uid": "25a1d94359b88fb7", "name": "测试play music on boomplayer", "time": {"start": 1755535438162, "stop": 1755535462677, "duration": 24515}, "status": "passed", "severity": "critical"}, {"uid": "644d8cb140f62e0a", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "time": {"start": 1755546542657, "stop": 1755546564878, "duration": 22221}, "status": "passed", "severity": "normal"}, {"uid": "b0523e40835680c9", "name": "测试set split-screen apps返回正确的不支持响应", "time": {"start": 1755549803696, "stop": 1755549826120, "duration": 22424}, "status": "passed", "severity": "normal"}, {"uid": "915a979c1ab3f826", "name": "测试turn off driving mode返回正确的不支持响应", "time": {"start": 1755550599676, "stop": 1755550621782, "duration": 22106}, "status": "passed", "severity": "normal"}, {"uid": "97499e652f70f1f2", "name": "测试start record能正常执行", "time": {"start": 1755539861533, "stop": 1755539917984, "duration": 56451}, "status": "passed", "severity": "critical"}, {"uid": "586d00224ff103cd", "name": "测试extend the image能正常执行", "time": {"start": 1755537293489, "stop": 1755537410226, "duration": 116737}, "status": "passed", "severity": "critical"}, {"uid": "cf36fb78559bcbb3", "name": "测试kill whatsapp能正常执行", "time": {"start": 1755546694143, "stop": 1755546717481, "duration": 23338}, "status": "passed", "severity": "critical"}, {"uid": "aac5c19fbbd75fa1", "name": "测试increase the brightness能正常执行", "time": {"start": 1755538613838, "stop": 1755538636244, "duration": 22406}, "status": "passed", "severity": "critical"}, {"uid": "4f28f303d3b93e43", "name": "测试help me take a screenshot能正常执行", "time": {"start": 1755538499457, "stop": 1755538525684, "duration": 26227}, "status": "passed", "severity": "critical"}, {"uid": "bbf40f8013d48bf8", "name": "测试play music by VLC", "time": {"start": 1755535270790, "stop": 1755535294960, "duration": 24170}, "status": "passed", "severity": "critical"}, {"uid": "ea916f8d49f87076", "name": "测试max alarm clock volume", "time": {"start": 1755538763210, "stop": 1755538785995, "duration": 22785}, "status": "passed", "severity": "critical"}, {"uid": "18d532e0c27f0a7d", "name": "测试open bluetooth", "time": {"start": 1755539192709, "stop": 1755539215056, "duration": 22347}, "status": "passed", "severity": "critical"}, {"uid": "6a06414489839fca", "name": "测试hello hello能正常执行", "time": {"start": 1755534378496, "stop": 1755534404032, "duration": 25536}, "status": "passed", "severity": "critical"}, {"uid": "15dbd978c9e9ff3c", "name": "测试order a takeaway返回正确的不支持响应", "time": {"start": 1755547537971, "stop": 1755547559923, "duration": 21952}, "status": "passed", "severity": "normal"}, {"uid": "a3fec9292f8b0113", "name": "测试Scan this QR code 能正常执行", "time": {"start": 1755537647829, "stop": 1755537765913, "duration": 118084}, "status": "passed", "severity": "critical"}, {"uid": "78003ba8ba5d066c", "name": "测试play love sotry", "time": {"start": 1755547694222, "stop": 1755547737272, "duration": 43050}, "status": "passed", "severity": "critical"}, {"uid": "94a260d39cc61397", "name": "测试turn off show battery percentage返回正确的不支持响应", "time": {"start": 1755550635665, "stop": 1755550658012, "duration": 22347}, "status": "passed", "severity": "normal"}, {"uid": "68c40bdfdb691181", "name": "测试play jay chou's music", "time": {"start": 1755533254235, "stop": 1755533293660, "duration": 39425}, "status": "passed", "severity": "critical"}, {"uid": "b8620cc65f4a7b43", "name": "测试set screen to maximum brightness能正常执行", "time": {"start": 1755539749356, "stop": 1755539772999, "duration": 23643}, "status": "passed", "severity": "critical"}, {"uid": "6b9bb04eb7f0c695", "name": "测试Switch Magic Voice to Grace能正常执行", "time": {"start": 1755540205374, "stop": 1755540227557, "duration": 22183}, "status": "passed", "severity": "critical"}, {"uid": "e1839257fa3f6667", "name": "测试set screen timeout返回正确的不支持响应", "time": {"start": 1755549582878, "stop": 1755549605151, "duration": 22273}, "status": "passed", "severity": "normal"}, {"uid": "bc289b8805ed6f47", "name": "测试remember the parking space", "time": {"start": 1755548175424, "stop": 1755548197727, "duration": 22303}, "status": "failed", "severity": "critical"}, {"uid": "cc934885a239c888", "name": "测试who is j k rowling能正常执行", "time": {"start": 1755536882797, "stop": 1755536908635, "duration": 25838}, "status": "passed", "severity": "critical"}, {"uid": "70a5f4ae04e6558e", "name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "time": {"start": 1755542750283, "stop": 1755542772092, "duration": 21809}, "status": "failed", "severity": "critical"}, {"uid": "f82ce057e728b5af", "name": "测试send my recent photos to mom through whatsapp能正常执行", "time": {"start": 1755535842792, "stop": 1755535868100, "duration": 25308}, "status": "passed", "severity": "critical"}, {"uid": "744cbb103ecdfa25", "name": "测试display the route go company", "time": {"start": 1755532595464, "stop": 1755532622408, "duration": 26944}, "status": "passed", "severity": "critical"}, {"uid": "de12e238457e041e", "name": "测试disable magic voice changer返回正确的不支持响应", "time": {"start": 1755544012434, "stop": 1755544034816, "duration": 22382}, "status": "passed", "severity": "normal"}, {"uid": "ab36b5e97a9a304d", "name": "测试disable accelerate dialogue返回正确的不支持响应", "time": {"start": 1755543787128, "stop": 1755543809387, "duration": 22259}, "status": "passed", "severity": "normal"}, {"uid": "dc1728464f2098ce", "name": "测试change your language to chinese能正常执行", "time": {"start": 1755538030786, "stop": 1755538030786, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "86944148d896cd06", "name": "测试show me premier leaguage goal ranking能正常执行", "time": {"start": 1755535882062, "stop": 1755535905697, "duration": 23635}, "status": "failed", "severity": "critical"}, {"uid": "ff50a601a62b5563", "name": "测试switch to performance mode返回正确的不支持响应", "time": {"start": 1755550179343, "stop": 1755550201447, "duration": 22104}, "status": "passed", "severity": "normal"}, {"uid": "20d4b045c93c589c", "name": "测试document summary能正常执行", "time": {"start": 1755537225693, "stop": 1755537279470, "duration": 53777}, "status": "passed", "severity": "critical"}, {"uid": "c4a9a1ad677f0098", "name": "测试navigation to the address in thie image能正常执行", "time": {"start": 1755547027098, "stop": 1755547058930, "duration": 31832}, "status": "failed", "severity": "critical"}, {"uid": "7480b55353f93bc6", "name": "测试close wifi能正常执行", "time": {"start": 1755538242038, "stop": 1755538242038, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "4bce7b61e04ef6f8", "name": "测试countdown 5 min能正常执行", "time": {"start": 1755538254786, "stop": 1755538285262, "duration": 30476}, "status": "failed", "severity": "critical"}, {"uid": "b082323455bd01a3", "name": "测试go home能正常执行", "time": {"start": 1755545168500, "stop": 1755545190680, "duration": 22180}, "status": "passed", "severity": "critical"}, {"uid": "f5cd234643930df6", "name": "测试take a joke能正常执行", "time": {"start": 1755536213534, "stop": 1755536238671, "duration": 25137}, "status": "passed", "severity": "critical"}, {"uid": "3c1d99d953a261c6", "name": "测试set phantom v pen返回正确的不支持响应", "time": {"start": 1755549398624, "stop": 1755549421021, "duration": 22397}, "status": "passed", "severity": "normal"}, {"uid": "b6e2a81031476512", "name": "测试close folax能正常执行", "time": {"start": 1755532358236, "stop": 1755532391952, "duration": 33716}, "status": "passed", "severity": "critical"}, {"uid": "d7139503c14d5b2d", "name": "测试set screen refresh rate返回正确的不支持响应", "time": {"start": 1755549509614, "stop": 1755549531578, "duration": 21964}, "status": "passed", "severity": "normal"}, {"uid": "f4f27779b7087710", "name": "测试set a timer for 10 minutes能正常执行", "time": {"start": 1755539466302, "stop": 1755539496281, "duration": 29979}, "status": "passed", "severity": "critical"}, {"uid": "7186c09ba4d6ed7b", "name": "测试go on playing fm能正常执行", "time": {"start": 1755534340663, "stop": 1755534364374, "duration": 23711}, "status": "passed", "severity": "critical"}, {"uid": "20d5bddf727c0fba", "name": "测试download in play store", "time": {"start": 1755544267541, "stop": 1755544294143, "duration": 26602}, "status": "passed", "severity": "critical"}, {"uid": "7fb3652513992510", "name": "测试set battery saver settings返回正确的不支持响应", "time": {"start": 1755548568717, "stop": 1755548591450, "duration": 22733}, "status": "passed", "severity": "normal"}, {"uid": "b58347ad96176240", "name": "测试Summarize what I'm reading能正常执行", "time": {"start": 1755537779885, "stop": 1755537843216, "duration": 63331}, "status": "failed", "severity": "critical"}, {"uid": "7becbd0e020b5726", "name": "测试tell me a joke能正常执行", "time": {"start": 1755536328555, "stop": 1755536353272, "duration": 24717}, "status": "failed", "severity": "critical"}, {"uid": "4c621b6aa0134d82", "name": "测试install whatsapp", "time": {"start": 1755546196098, "stop": 1755546224342, "duration": 28244}, "status": "passed", "severity": "critical"}, {"uid": "ddc06db5db20b677", "name": "测试measure heart rate", "time": {"start": 1755535091232, "stop": 1755535113013, "duration": 21781}, "status": "passed", "severity": "critical"}, {"uid": "2aaf5347dbc01d8b", "name": "测试set app auto rotate返回正确的不支持响应", "time": {"start": 1755548495332, "stop": 1755548517734, "duration": 22402}, "status": "passed", "severity": "normal"}, {"uid": "abcbb8c6f6452862", "name": "测试smart charge能正常执行", "time": {"start": 1755539824246, "stop": 1755539847138, "duration": 22892}, "status": "failed", "severity": "critical"}, {"uid": "77d90cbd0aa54ede", "name": "测试turn on the screen record能正常执行", "time": {"start": 1755538374910, "stop": 1755538402909, "duration": 27999}, "status": "passed", "severity": "critical"}, {"uid": "2cf73ad699817a37", "name": "测试help me write an thanks letter能正常执行", "time": {"start": 1755545875017, "stop": 1755545900251, "duration": 25234}, "status": "passed", "severity": "critical"}, {"uid": "afa51a402f294990", "name": "测试play music by Audiomack", "time": {"start": 1755547751251, "stop": 1755547776030, "duration": 24779}, "status": "passed", "severity": "critical"}, {"uid": "ce2c76aab15be26", "name": "测试continue playing能正常执行", "time": {"start": 1755534149151, "stop": 1755534172506, "duration": 23355}, "status": "passed", "severity": "critical"}, {"uid": "d993e90b89a322eb", "name": "测试how to say hello in french能正常执行", "time": {"start": 1755534672894, "stop": 1755534693038, "duration": 20144}, "status": "passed", "severity": "critical"}, {"uid": "f005f18c11305148", "name": "测试jump to battery usage返回正确的不支持响应", "time": {"start": 1755546461408, "stop": 1755546483763, "duration": 22355}, "status": "passed", "severity": "normal"}, {"uid": "87e4c8157a7c0b0a", "name": "测试open the settings", "time": {"start": 1755547417706, "stop": 1755547450006, "duration": 32300}, "status": "passed", "severity": "critical"}, {"uid": "294d0761bd0b35ea", "name": "测试how is the wheather today能正常执行", "time": {"start": 1755534550532, "stop": 1755534572689, "duration": 22157}, "status": "passed", "severity": "critical"}, {"uid": "bc58b8085ad08e08", "name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "time": {"start": 1755548458658, "stop": 1755548481293, "duration": 22635}, "status": "passed", "severity": "normal"}, {"uid": "8bb36711edb0df7f", "name": "测试switch to equilibrium mode返回正确的不支持响应", "time": {"start": 1755550143772, "stop": 1755550165334, "duration": 21562}, "status": "passed", "severity": "normal"}, {"uid": "c76ab9470c51fe56", "name": "测试set phone number返回正确的不支持响应", "time": {"start": 1755549435535, "stop": 1755549457878, "duration": 22343}, "status": "passed", "severity": "normal"}, {"uid": "bd361d609157db", "name": "测试turn on smart reminder能正常执行", "time": {"start": 1755541482387, "stop": 1755541505404, "duration": 23017}, "status": "failed", "severity": "critical"}, {"uid": "5c1299382f028a5e", "name": "测试turn off nfc能正常执行", "time": {"start": 1755541074574, "stop": 1755541099089, "duration": 24515}, "status": "passed", "severity": "critical"}, {"uid": "2fc78b29d7c3b8a0", "name": "测试stop workout能正常执行", "time": {"start": 1755536105054, "stop": 1755536127302, "duration": 22248}, "status": "passed", "severity": "critical"}, {"uid": "73f1f654df418a3e", "name": "测试power off能正常执行", "time": {"start": 1755539329699, "stop": 1755539329699, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "c28179de4e531562", "name": "测试enable auto pickup返回正确的不支持响应", "time": {"start": 1755544499399, "stop": 1755544521558, "duration": 22159}, "status": "passed", "severity": "normal"}, {"uid": "5f18b39a20f5d622", "name": "测试navigate to shanghai disneyland能正常执行", "time": {"start": 1755542242740, "stop": 1755542271847, "duration": 29107}, "status": "passed", "severity": "critical"}, {"uid": "efbf1ae95846c87f", "name": "测试end exercising能正常执行", "time": {"start": 1755544844031, "stop": 1755544866050, "duration": 22019}, "status": "passed", "severity": "critical"}, {"uid": "1092ee4849172c85", "name": "测试open flashlight", "time": {"start": 1755539265631, "stop": 1755539290669, "duration": 25038}, "status": "passed", "severity": "critical"}, {"uid": "6e8ca94f901f3575", "name": "测试whatsapp能正常执行", "time": {"start": 1755542521457, "stop": 1755542545051, "duration": 23594}, "status": "passed", "severity": "critical"}, {"uid": "a36e4a54b6cc5d0", "name": "测试call number by whatsapp能正常执行", "time": {"start": 1755543026002, "stop": 1755543057161, "duration": 31159}, "status": "passed", "severity": "critical"}, {"uid": "d356792bb10c9dcb", "name": "测试switch to power saving mode返回正确的不支持响应", "time": {"start": 1755550215198, "stop": 1755550237680, "duration": 22482}, "status": "passed", "severity": "normal"}, {"uid": "1c93ac7f69b05022", "name": "测试disable running lock返回正确的不支持响应", "time": {"start": 1755544085000, "stop": 1755544108381, "duration": 23381}, "status": "passed", "severity": "normal"}, {"uid": "e6a76f74a137ce40", "name": "测试Dial the number on the screen", "time": {"start": 1755543750696, "stop": 1755543773049, "duration": 22353}, "status": "passed", "severity": "critical"}, {"uid": "563422272498211e", "name": "测试turn on the flashlight能正常执行", "time": {"start": 1755541555184, "stop": 1755541580044, "duration": 24860}, "status": "passed", "severity": "critical"}, {"uid": "8ac2fbb40ff2fe45", "name": "open clock", "time": {"start": 1755532748815, "stop": 1755532778833, "duration": 30018}, "status": "failed", "severity": "critical"}, {"uid": "a600cbbbab2efcc8", "name": "测试open contact命令", "time": {"start": 1755533003998, "stop": 1755533039380, "duration": 35382}, "status": "passed", "severity": "critical"}, {"uid": "3f740db0cef2375b", "name": "测试enable running lock返回正确的不支持响应", "time": {"start": 1755544698511, "stop": 1755544721984, "duration": 23473}, "status": "passed", "severity": "normal"}, {"uid": "dfd3fac825d35b2e", "name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "time": {"start": 1755545725296, "stop": 1755545747340, "duration": 22044}, "status": "passed", "severity": "critical"}, {"uid": "71fced2abbc8460c", "name": "测试show scores between livepool and manchester city能正常执行", "time": {"start": 1755535957935, "stop": 1755535983319, "duration": 25384}, "status": "passed", "severity": "critical"}, {"uid": "825ccf498017b3be", "name": "测试search picture in my gallery能正常执行", "time": {"start": 1755535766243, "stop": 1755535793130, "duration": 26887}, "status": "passed", "severity": "critical"}, {"uid": "8f694e90876ac004", "name": "测试download in playstore", "time": {"start": 1755544308198, "stop": 1755544335028, "duration": 26830}, "status": "passed", "severity": "critical"}, {"uid": "9e09c78c059d573c", "name": "测试appeler maman能正常执行", "time": {"start": 1755533879354, "stop": 1755533901182, "duration": 21828}, "status": "passed", "severity": "critical"}, {"uid": "8948c2eff36606c6", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "time": {"start": 1755546657406, "stop": 1755546679914, "duration": 22508}, "status": "passed", "severity": "normal"}, {"uid": "9b5c88885de7c800", "name": "stop  screen recording能正常执行", "time": {"start": 1755541635822, "stop": 1755541662964, "duration": 27142}, "status": "failed", "severity": "critical"}, {"uid": "565d3a68eadd14f6", "name": "测试Switch to davido voice能正常执行", "time": {"start": 1755550107536, "stop": 1755550129785, "duration": 22249}, "status": "passed", "severity": "critical"}, {"uid": "3d8ea4f9bffaeeda", "name": "测试decrease the volume to the minimun能正常执行", "time": {"start": 1755538337147, "stop": 1755538360848, "duration": 23701}, "status": "passed", "severity": "critical"}, {"uid": "ec3f00031eaf4371", "name": "测试restart my phone能正常执行", "time": {"start": 1755548240961, "stop": 1755548240961, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "6e8cfebab1b113de", "name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "time": {"start": 1755543714952, "stop": 1755543736798, "duration": 21846}, "status": "passed", "severity": "critical"}, {"uid": "8daac15c9f9ea1a8", "name": "测试play football video by youtube", "time": {"start": 1755547651841, "stop": 1755547680054, "duration": 28213}, "status": "passed", "severity": "critical"}, {"uid": "303d6a001e695137", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "time": {"start": 1755546353628, "stop": 1755546375896, "duration": 22268}, "status": "passed", "severity": "normal"}, {"uid": "a009973ed07360d8", "name": "测试more settings返回正确的不支持响应", "time": {"start": 1755546940225, "stop": 1755546965932, "duration": 25707}, "status": "passed", "severity": "normal"}, {"uid": "979aaae63733c3e6", "name": "测试yandex eats返回正确的不支持响应", "time": {"start": 1755551295036, "stop": 1755551317273, "duration": 22237}, "status": "failed", "severity": "normal"}, {"uid": "e1f427e407268742", "name": "测试puppy", "time": {"start": 1755548098626, "stop": 1755548124639, "duration": 26013}, "status": "passed", "severity": "critical"}, {"uid": "687c67ac43338e6c", "name": "测试stop playing", "time": {"start": 1755533651835, "stop": 1755533675491, "duration": 23656}, "status": "passed", "severity": "critical"}, {"uid": "8dd63573fd58dbfa", "name": "测试new year wishes", "time": {"start": 1755547119803, "stop": 1755547144285, "duration": 24482}, "status": "passed", "severity": "critical"}, {"uid": "523026d721903565", "name": "测试switch charging modes能正常执行", "time": {"start": 1755540169131, "stop": 1755540191004, "duration": 21873}, "status": "failed", "severity": "critical"}, {"uid": "684fcf296df9cee5", "name": "测试set notifications volume to 50能正常执行", "time": {"start": 1755539672907, "stop": 1755539696420, "duration": 23513}, "status": "failed", "severity": "critical"}, {"uid": "2fb5de1bf6466d20", "name": "测试take notes能正常执行", "time": {"start": 1755550287593, "stop": 1755550313863, "duration": 26270}, "status": "failed", "severity": "critical"}, {"uid": "18dcfed9cffe3fc1", "name": "测试check ram information", "time": {"start": 1755543498357, "stop": 1755543520294, "duration": 21937}, "status": "passed", "severity": "critical"}, {"uid": "3daf15b7b3653968", "name": "测试play music by visha", "time": {"start": 1755535347151, "stop": 1755535385655, "duration": 38504}, "status": "failed", "severity": "critical"}, {"uid": "685452b2ba4f9a89", "name": "测试start boosting phone能正常执行", "time": {"start": 1755549955814, "stop": 1755549977337, "duration": 21523}, "status": "passed", "severity": "critical"}, {"uid": "e34af531cc9853a3", "name": "测试where`s my car能正常执行", "time": {"start": 1755551258713, "stop": 1755551281024, "duration": 22311}, "status": "passed", "severity": "critical"}, {"uid": "7cc6c01c614625cd", "name": "测试turn on driving mode返回正确的不支持响应", "time": {"start": 1755550672216, "stop": 1755550698097, "duration": 25881}, "status": "passed", "severity": "normal"}, {"uid": "5717823ea652bea4", "name": "测试min notifications volume能正常执行", "time": {"start": 1755539082567, "stop": 1755539105335, "duration": 22768}, "status": "passed", "severity": "critical"}, {"uid": "44ed27c7b7731a47", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1755536692634, "stop": 1755536714426, "duration": 21792}, "status": "failed", "severity": "critical"}, {"uid": "4947f2cef0c48e09", "name": "测试say hello能正常执行", "time": {"start": 1755535686786, "stop": 1755535712138, "duration": 25352}, "status": "passed", "severity": "critical"}, {"uid": "f35ab6662635c7df", "name": "测试Three Little Pigs", "time": {"start": 1755550560264, "stop": 1755550585695, "duration": 25431}, "status": "passed", "severity": "critical"}, {"uid": "feb779f3ce9b15a7", "name": "测试set ringtone volume to 50能正常执行", "time": {"start": 1755539710917, "stop": 1755539734868, "duration": 23951}, "status": "passed", "severity": "critical"}, {"uid": "1bcb128e66a5b9a5", "name": "测试puppy能正常执行", "time": {"start": 1755537424190, "stop": 1755537502424, "duration": 78234}, "status": "failed", "severity": "critical"}, {"uid": "1d166cd5bb89f599", "name": "测试pls open whatsapp", "time": {"start": 1755548023055, "stop": 1755548046986, "duration": 23931}, "status": "failed", "severity": "critical"}, {"uid": "a69cd11544f75120", "name": "测试enable touch optimization返回正确的不支持响应", "time": {"start": 1755544736044, "stop": 1755544757995, "duration": 21951}, "status": "passed", "severity": "normal"}, {"uid": "6463f34280457263", "name": "测试set flex-still mode返回正确的不支持响应", "time": {"start": 1755548872001, "stop": 1755548894307, "duration": 22306}, "status": "passed", "severity": "normal"}, {"uid": "ffe9139580a870b0", "name": "stop  screen recording能正常执行", "time": {"start": 1755538416925, "stop": 1755538445384, "duration": 28459}, "status": "failed", "severity": "critical"}, {"uid": "409e680a2a7a5258", "name": "测试measure blood oxygen", "time": {"start": 1755535055553, "stop": 1755535077198, "duration": 21645}, "status": "passed", "severity": "critical"}, {"uid": "c3fab8a57bf5eaa5", "name": "测试i want to watch fireworks能正常执行", "time": {"start": 1755534860314, "stop": 1755534885786, "duration": 25472}, "status": "passed", "severity": "critical"}, {"uid": "11fc47f08ff73cab", "name": "测试check my to-do list能正常执行", "time": {"start": 1755543462596, "stop": 1755543484509, "duration": 21913}, "status": "passed", "severity": "critical"}, {"uid": "5dee92077c27d125", "name": "测试it wears a yellow leather collar", "time": {"start": 1755546278113, "stop": 1755546303687, "duration": 25574}, "status": "passed", "severity": "critical"}, {"uid": "72118dc54e25e056", "name": "测试disable unfreeze返回正确的不支持响应", "time": {"start": 1755544158549, "stop": 1755544180596, "duration": 22047}, "status": "passed", "severity": "normal"}, {"uid": "626820f2f317f7d6", "name": "测试A furry little monkey", "time": {"start": 1755545397220, "stop": 1755545422399, "duration": 25179}, "status": "passed", "severity": "critical"}, {"uid": "9a3290f96fe7666b", "name": "测试switching charging speed能正常执行", "time": {"start": 1755550251513, "stop": 1755550273578, "duration": 22065}, "status": "passed", "severity": "critical"}, {"uid": "59a4ff0abe989014", "name": "测试next music能正常执行", "time": {"start": 1755535127017, "stop": 1755535149023, "duration": 22006}, "status": "passed", "severity": "critical"}, {"uid": "a5616c43d872369b", "name": "测试help me write an thanks email能正常执行", "time": {"start": 1755545835689, "stop": 1755545861121, "duration": 25432}, "status": "failed", "severity": "critical"}, {"uid": "d0217fa5fc8a4a81", "name": "测试turn on brightness to 80能正常执行", "time": {"start": 1755541260399, "stop": 1755541282499, "duration": 22100}, "status": "passed", "severity": "critical"}, {"uid": "789a9ec2b9f26c16", "name": "测试There are transparent, glowing multicolored soap bubbles around it", "time": {"start": 1755550480853, "stop": 1755550507042, "duration": 26189}, "status": "passed", "severity": "critical"}, {"uid": "ee55ab8579ed7a97", "name": "测试A sports car is parked on the street side", "time": {"start": 1755542941770, "stop": 1755542967243, "duration": 25473}, "status": "passed", "severity": "critical"}, {"uid": "be07492f78740106", "name": "测试close whatsapp能正常执行", "time": {"start": 1755534111697, "stop": 1755534135236, "duration": 23539}, "status": "passed", "severity": "critical"}, {"uid": "4bb0f3490eb7fd10", "name": "测试order a takeaway能正常执行", "time": {"start": 1755542448115, "stop": 1755542469919, "duration": 21804}, "status": "failed", "severity": "critical"}, {"uid": "305a8e737b98a962", "name": "测试set Battery Saver setting能正常执行", "time": {"start": 1755539586962, "stop": 1755539621174, "duration": 34212}, "status": "passed", "severity": "critical"}, {"uid": "c7c9715779136ab0", "name": "测试Search for addresses on the screen能正常执行", "time": {"start": 1755548295766, "stop": 1755548321953, "duration": 26187}, "status": "passed", "severity": "critical"}, {"uid": "ce0cbaec22f70255", "name": "测试turn off smart reminder能正常执行", "time": {"start": 1755541113139, "stop": 1755541135920, "duration": 22781}, "status": "passed", "severity": "critical"}, {"uid": "41af1b410102153b", "name": "测试set parallel windows返回正确的不支持响应", "time": {"start": 1755549324908, "stop": 1755549347327, "duration": 22419}, "status": "passed", "severity": "normal"}, {"uid": "10ed7fd6efa81c6a", "name": "测试there is a colorful butterfly beside it", "time": {"start": 1755550520777, "stop": 1755550546193, "duration": 25416}, "status": "passed", "severity": "critical"}, {"uid": "d4033ac84f856cd7", "name": "测试set special function返回正确的不支持响应", "time": {"start": 1755549766920, "stop": 1755549789346, "duration": 22426}, "status": "passed", "severity": "normal"}, {"uid": "13f549d9d5b7167a", "name": "测试Change the style of this image to 3D cartoon能正常执行", "time": {"start": 1755537094408, "stop": 1755537211531, "duration": 117123}, "status": "failed", "severity": "critical"}, {"uid": "e062c6b9a8b843fd", "name": "测试introduce yourself能正常执行", "time": {"start": 1755534899694, "stop": 1755534924691, "duration": 24997}, "status": "passed", "severity": "critical"}, {"uid": "98d99df8f07af07c", "name": "测试turn on the alarm at 8 am", "time": {"start": 1755533800620, "stop": 1755533822664, "duration": 22044}, "status": "passed", "severity": "critical"}, {"uid": "f034caa98e78f0aa", "name": "测试help me generate a picture of an elegant girl", "time": {"start": 1755545653284, "stop": 1755545675171, "duration": 21887}, "status": "passed", "severity": "critical"}, {"uid": "130b6c368ae3f475", "name": "测试check contact能正常执行", "time": {"start": 1755543264398, "stop": 1755543295558, "duration": 31160}, "status": "passed", "severity": "critical"}, {"uid": "7d88c44af202bec", "name": "测试set customized cover screen返回正确的不支持响应", "time": {"start": 1755548761809, "stop": 1755548784471, "duration": 22662}, "status": "passed", "severity": "normal"}, {"uid": "baa21503fd9a96ca", "name": "测试turn on do not disturb mode能正常执行", "time": {"start": 1755541296337, "stop": 1755541319475, "duration": 23138}, "status": "passed", "severity": "critical"}, {"uid": "8290a3a5e5bcf3f4", "name": "stop  screen recording能正常执行", "time": {"start": 1755540132285, "stop": 1755540155496, "duration": 23211}, "status": "passed", "severity": "critical"}, {"uid": "29e24e26da6b1f7c", "name": "测试close airplane能正常执行", "time": {"start": 1755538143900, "stop": 1755538166705, "duration": 22805}, "status": "passed", "severity": "critical"}, {"uid": "5b707a563124442", "name": "测试enable zonetouch master返回正确的不支持响应", "time": {"start": 1755544808173, "stop": 1755544830192, "duration": 22019}, "status": "passed", "severity": "normal"}, {"uid": "f8218991466f15e9", "name": "测试set my fonts返回正确的不支持响应", "time": {"start": 1755549168756, "stop": 1755549191000, "duration": 22244}, "status": "passed", "severity": "normal"}, {"uid": "d621004ece966b52", "name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "time": {"start": 1755542152419, "stop": 1755542185515, "duration": 33096}, "status": "passed", "severity": "critical"}, {"uid": "6179f1c471bf7b0", "name": "测试turn down ring volume能正常执行", "time": {"start": 1755540853327, "stop": 1755540875951, "duration": 22624}, "status": "passed", "severity": "critical"}, {"uid": "66074d91611f0379", "name": "测试check front camera information能正常执行", "time": {"start": 1755538042556, "stop": 1755538067497, "duration": 24941}, "status": "passed", "severity": "critical"}, {"uid": "454a02f184d6e886", "name": "测试set app notifications返回正确的不支持响应", "time": {"start": 1755548531993, "stop": 1755548554414, "duration": 22421}, "status": "passed", "severity": "normal"}, {"uid": "95216e3dbb2b6a8e", "name": "测试turn on location services能正常执行", "time": {"start": 1755541406362, "stop": 1755541429561, "duration": 23199}, "status": "passed", "severity": "critical"}, {"uid": "e21c5cf1146a7ec", "name": "测试play sun be song of jide chord", "time": {"start": 1755533451310, "stop": 1755533491041, "duration": 39731}, "status": "passed", "severity": "critical"}, {"uid": "71be630e71caa50e", "name": "测试open bt", "time": {"start": 1755539229114, "stop": 1755539251618, "duration": 22504}, "status": "passed", "severity": "critical"}, {"uid": "f7c114017e246923", "name": "测试search my gallery for food pictures能正常执行", "time": {"start": 1755535726211, "stop": 1755535752166, "duration": 25955}, "status": "passed", "severity": "critical"}, {"uid": "f47927df2a6b5ce3", "name": "测试disable call rejection返回正确的不支持响应", "time": {"start": 1755543931278, "stop": 1755543962526, "duration": 31248}, "status": "passed", "severity": "normal"}, {"uid": "16d295102618a407", "name": "测试what's your name？能正常执行", "time": {"start": 1755536728444, "stop": 1755536750354, "duration": 21910}, "status": "passed", "severity": "critical"}, {"uid": "c8d53c70b4d6dbed", "name": "测试increase screen brightness能正常执行", "time": {"start": 1755538577374, "stop": 1755538599601, "duration": 22227}, "status": "passed", "severity": "critical"}, {"uid": "b888d3bc51fa3b1b", "name": "测试i want to make a call能正常执行", "time": {"start": 1755534815267, "stop": 1755534846296, "duration": 31029}, "status": "passed", "severity": "critical"}, {"uid": "c86277f1fc22de8e", "name": "测试switch to default mode能正常执行", "time": {"start": 1755540313809, "stop": 1755540345990, "duration": 32181}, "status": "failed", "severity": "critical"}, {"uid": "9401f2aafc72cbb6", "name": "测试set smart panel返回正确的不支持响应", "time": {"start": 1755549729979, "stop": 1755549752409, "duration": 22430}, "status": "passed", "severity": "normal"}, {"uid": "7b92e467542433c3", "name": "测试summarize content on this page能正常执行", "time": {"start": 1755536141449, "stop": 1755536163238, "duration": 21789}, "status": "passed", "severity": "critical"}, {"uid": "110bca580595cc26", "name": "测试redial", "time": {"start": 1755548126150, "stop": 1755548126150, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "86b9e6f5cf0f3680", "name": "测试set folding screen zone返回正确的不支持响应", "time": {"start": 1755548980760, "stop": 1755549003220, "duration": 22460}, "status": "passed", "severity": "normal"}, {"uid": "36874b7f55820c4a", "name": "测试open dialer能正常执行", "time": {"start": 1755532881286, "stop": 1755532917469, "duration": 36183}, "status": "passed", "severity": "critical"}, {"uid": "f475b7a1580f4c44", "name": "测试play music on visha", "time": {"start": 1755535476591, "stop": 1755535516009, "duration": 39418}, "status": "passed", "severity": "critical"}, {"uid": "cd7d712956ba0f25", "name": "测试disable brightness locking返回正确的不支持响应", "time": {"start": 1755543895172, "stop": 1755543917265, "duration": 22093}, "status": "passed", "severity": "normal"}, {"uid": "29e79e9a23da4107", "name": "测试help me generate a picture of a white facial cleanser product advertisement", "time": {"start": 1755545581193, "stop": 1755545603404, "duration": 22211}, "status": "passed", "severity": "critical"}, {"uid": "e34cb01b2c00ec2a", "name": "测试decrease the brightness能正常执行", "time": {"start": 1755538299424, "stop": 1755538322745, "duration": 23321}, "status": "failed", "severity": "critical"}, {"uid": "32df01cfab8864af", "name": "测试navigation to the lucky能正常执行", "time": {"start": 1755542285440, "stop": 1755542316964, "duration": 31524}, "status": "passed", "severity": "critical"}, {"uid": "1a5d810bd216a785", "name": "测试i want make a video call to能正常执行", "time": {"start": 1755546074978, "stop": 1755546105867, "duration": 30889}, "status": "failed", "severity": "critical"}, {"uid": "774b232eb8b4279", "name": "测试turn on adaptive brightness能正常执行", "time": {"start": 1755541150238, "stop": 1755541173024, "duration": 22786}, "status": "failed", "severity": "critical"}, {"uid": "221ac10e9d0a0fd8", "name": "测试boost phone能正常执行", "time": {"start": 1755537966527, "stop": 1755537988156, "duration": 21629}, "status": "passed", "severity": "critical"}, {"uid": "1701645e26679093", "name": "测试tell me a joke能正常执行", "time": {"start": 1755550328224, "stop": 1755550350425, "duration": 22201}, "status": "passed", "severity": "critical"}, {"uid": "576291327acaf0e2", "name": "测试Switch to Hyper Charge能正常执行", "time": {"start": 1755540443086, "stop": 1755540465098, "duration": 22012}, "status": "passed", "severity": "critical"}, {"uid": "1c8fd1a76b13782e", "name": "测试Switch to Barrage Notification能正常执行", "time": {"start": 1755540277666, "stop": 1755540299565, "duration": 21899}, "status": "passed", "severity": "critical"}, {"uid": "ab28f76d4ee5ffab", "name": "测试switch to flash notification能正常执行", "time": {"start": 1755540396088, "stop": 1755540428767, "duration": 32679}, "status": "failed", "severity": "critical"}, {"uid": "b3d6c60bb022e56e", "name": "测试what's your name", "time": {"start": 1755551076900, "stop": 1755551098907, "duration": 22007}, "status": "passed", "severity": "critical"}, {"uid": "15453db92b625b22", "name": "测试turn up notifications volume能正常执行", "time": {"start": 1755541750629, "stop": 1755541773466, "duration": 22837}, "status": "passed", "severity": "critical"}, {"uid": "4d5cccf79669ff0c", "name": "测试pause screen recording能正常执行", "time": {"start": 1755539973003, "stop": 1755539997769, "duration": 24766}, "status": "passed", "severity": "critical"}, {"uid": "480a0fe4bb19fcbc", "name": "测试what is the weather today能正常执行", "time": {"start": 1755550960004, "stop": 1755550990198, "duration": 30194}, "status": "passed", "severity": "critical"}, {"uid": "32a4642822207cf0", "name": "测试merry christmas", "time": {"start": 1755546864527, "stop": 1755546889045, "duration": 24518}, "status": "failed", "severity": "critical"}, {"uid": "65d99825173c0947", "name": "测试how to say i love you in french能正常执行", "time": {"start": 1755534707111, "stop": 1755534727518, "duration": 20407}, "status": "passed", "severity": "critical"}, {"uid": "35deb8fb0261ed29", "name": "测试pause song能正常执行", "time": {"start": 1755533126191, "stop": 1755533147873, "duration": 21682}, "status": "failed", "severity": "critical"}, {"uid": "397b1501c16d5e75", "name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "time": {"start": 1755545279615, "stop": 1755545304641, "duration": 25026}, "status": "failed", "severity": "critical"}, {"uid": "ce4bd0701fd3c3fd", "name": "测试turn off adaptive brightness能正常执行", "time": {"start": 1755540926281, "stop": 1755540949515, "duration": 23234}, "status": "passed", "severity": "critical"}, {"uid": "80c8b496993f5e37", "name": "测试how's the weather today in shanghai能正常执行", "time": {"start": 1755534629582, "stop": 1755534658909, "duration": 29327}, "status": "passed", "severity": "critical"}, {"uid": "8e7863fa9e0516bc", "name": "测试set color style返回正确的不支持响应", "time": {"start": 1755548651804, "stop": 1755548674233, "duration": 22429}, "status": "passed", "severity": "normal"}, {"uid": "ceea2a980c7d0d9c", "name": "测试what's the weather like in shanghai today能正常执行", "time": {"start": 1755536563259, "stop": 1755536592217, "duration": 28958}, "status": "failed", "severity": "critical"}, {"uid": "dbd33f4313f9e7e1", "name": "测试play music", "time": {"start": 1755533344992, "stop": 1755533383918, "duration": 38926}, "status": "passed", "severity": "critical"}, {"uid": "1c736fd2e0572192", "name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "time": {"start": 1755540241758, "stop": 1755540263880, "duration": 22122}, "status": "passed", "severity": "critical"}, {"uid": "c13cdb2a38eadf83", "name": "测试disable call on hold返回正确的不支持响应", "time": {"start": 1755532551996, "stop": 1755532582809, "duration": 30813}, "status": "passed", "severity": "normal"}, {"uid": "d25630d6258acf1e", "name": "测试power saving能正常执行", "time": {"start": 1755539342207, "stop": 1755539370883, "duration": 28676}, "status": "passed", "severity": "critical"}, {"uid": "f0bc2546728b57be", "name": "测试set cover screen apps返回正确的不支持响应", "time": {"start": 1755548725100, "stop": 1755548747846, "duration": 22746}, "status": "passed", "severity": "normal"}, {"uid": "4e20f74cb4938ea2", "name": "测试change man voice能正常执行", "time": {"start": 1755543156187, "stop": 1755543177815, "duration": 21628}, "status": "passed", "severity": "critical"}, {"uid": "467ff75891f341fb", "name": "测试navigate from to red square能正常执行", "time": {"start": 1755542199306, "stop": 1755542229004, "duration": 29698}, "status": "passed", "severity": "critical"}, {"uid": "f3e2e05aaeb14ddc", "name": "测试open camera能正常执行", "time": {"start": 1755532704470, "stop": 1755532734454, "duration": 29984}, "status": "passed", "severity": "critical"}, {"uid": "4eadf7aaf6065e14", "name": "测试turn off flashlight能正常执行", "time": {"start": 1755541000559, "stop": 1755541024984, "duration": 24425}, "status": "passed", "severity": "critical"}, {"uid": "bad169110b617eec", "name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "time": {"start": 1755545132693, "stop": 1755545154594, "duration": 21901}, "status": "passed", "severity": "critical"}, {"uid": "d3dea9e9d4c15593", "name": "测试disable hide notifications返回正确的不支持响应", "time": {"start": 1755543976460, "stop": 1755543998487, "duration": 22027}, "status": "passed", "severity": "normal"}, {"uid": "e31835399ffcc5d7", "name": "测试hi能正常执行", "time": {"start": 1755534467838, "stop": 1755534493385, "duration": 25547}, "status": "passed", "severity": "critical"}, {"uid": "10d2f5628c8cbadc", "name": "测试open settings", "time": {"start": 1755546025986, "stop": 1755546061293, "duration": 35307}, "status": "passed", "severity": "critical"}, {"uid": "61332991e71deb47", "name": "测试take a screenshot能正常执行", "time": {"start": 1755533689519, "stop": 1755533713980, "duration": 24461}, "status": "passed", "severity": "critical"}, {"uid": "dd571f0ce49847cb", "name": "测试summarize what i'm reading能正常执行", "time": {"start": 1755536177633, "stop": 1755536199724, "duration": 22091}, "status": "passed", "severity": "critical"}, {"uid": "aafe14e472f370a3", "name": "测试search whatsapp for me能正常执行", "time": {"start": 1755548376434, "stop": 1755548404737, "duration": 28303}, "status": "passed", "severity": "critical"}, {"uid": "1c76bbb443caa181", "name": "测试play music by yandex music", "time": {"start": 1755535399772, "stop": 1755535424407, "duration": 24635}, "status": "passed", "severity": "critical"}, {"uid": "7bcfa670f3717f3c", "name": "测试set lockscreen passwords返回正确的不支持响应", "time": {"start": 1755549132314, "stop": 1755549154625, "duration": 22311}, "status": "passed", "severity": "normal"}, {"uid": "c862ac6b8bc8ba94", "name": "测试parking space能正常执行", "time": {"start": 1755547574100, "stop": 1755547596332, "duration": 22232}, "status": "passed", "severity": "critical"}, {"uid": "1ee3a2d6d4b65ad", "name": "测试set timer", "time": {"start": 1755549840641, "stop": 1755549867778, "duration": 27137}, "status": "passed", "severity": "critical"}, {"uid": "c0bcef3f87616eb5", "name": "测试reset phone返回正确的不支持响应", "time": {"start": 1755548240955, "stop": 1755548240955, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "19693adbe0f7f612", "name": "测试set timezone返回正确的不支持响应", "time": {"start": 1755549882185, "stop": 1755549904488, "duration": 22303}, "status": "passed", "severity": "normal"}, {"uid": "7a71af2e1e53eaf4", "name": "测试gold coin rain能正常执行", "time": {"start": 1755545241022, "stop": 1755545265610, "duration": 24588}, "status": "passed", "severity": "critical"}, {"uid": "b1915364bac38f76", "name": "测试Summarize what I'm reading", "time": {"start": 1755550071657, "stop": 1755550093619, "duration": 21962}, "status": "passed", "severity": "critical"}, {"uid": "e738d908d4f56738", "name": "测试close ella能正常执行", "time": {"start": 1755532307829, "stop": 1755532344211, "duration": 36382}, "status": "passed", "severity": "critical"}, {"uid": "d7a88c8a026436bb", "name": "测试play rock music", "time": {"start": 1755533397763, "stop": 1755533436640, "duration": 38877}, "status": "passed", "severity": "critical"}, {"uid": "ce531324130742d4", "name": "测试max notifications volume能正常执行", "time": {"start": 1755538836770, "stop": 1755538858989, "duration": 22219}, "status": "failed", "severity": "critical"}, {"uid": "5d13ab9424ca7076", "name": "测试check model information返回正确的不支持响应", "time": {"start": 1755543390467, "stop": 1755543412287, "duration": 21820}, "status": "passed", "severity": "normal"}, {"uid": "5b7b43a0a224ef94", "name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "time": {"start": 1755545989614, "stop": 1755546011887, "duration": 22273}, "status": "passed", "severity": "critical"}, {"uid": "e25e8aa73ba38521", "name": "测试switched to data mode能正常执行", "time": {"start": 1755540587325, "stop": 1755540611357, "duration": 24032}, "status": "passed", "severity": "critical"}, {"uid": "eb294921beeb20c3", "name": "测试turn on nfc能正常执行", "time": {"start": 1755541443841, "stop": 1755541468196, "duration": 24355}, "status": "failed", "severity": "critical"}, {"uid": "5a07f04e872ede5e", "name": "测试searching for a method of violent murder能正常执行", "time": {"start": 1755535806907, "stop": 1755535828451, "duration": 21544}, "status": "broken", "severity": "critical"}, {"uid": "bcecb343f6bed949", "name": "测试turn on the screen record能正常执行", "time": {"start": 1755541594412, "stop": 1755541621649, "duration": 27237}, "status": "passed", "severity": "critical"}, {"uid": "e61d8722a56d0e37", "name": "测试play taylor swift‘s song love story", "time": {"start": 1755547790139, "stop": 1755547831409, "duration": 41270}, "status": "passed", "severity": "critical"}, {"uid": "cf9f2c5a2ad8b9eb", "name": "测试play video by youtube", "time": {"start": 1755547941987, "stop": 1755547970029, "duration": 28042}, "status": "passed", "severity": "critical"}, {"uid": "ce3ad8cd15332bdc", "name": "测试Adjustment the brightness to 50%能正常执行", "time": {"start": 1755537857648, "stop": 1755537880029, "duration": 22381}, "status": "passed", "severity": "critical"}, {"uid": "b8b38e493df50885", "name": "测试new year wishs能正常执行", "time": {"start": 1755547158710, "stop": 1755547183523, "duration": 24813}, "status": "passed", "severity": "critical"}, {"uid": "e01919838c9e2688", "name": "测试enable all ai magic box features返回正确的不支持响应", "time": {"start": 1755544463027, "stop": 1755544485067, "duration": 22040}, "status": "passed", "severity": "normal"}, {"uid": "68e268cc4fc1b311", "name": "测试pls open the newest whatsapp activity", "time": {"start": 1755542483785, "stop": 1755542507616, "duration": 23831}, "status": "passed", "severity": "critical"}, {"uid": "d0cb405c048b5762", "name": "测试close phonemaster能正常执行", "time": {"start": 1755532405659, "stop": 1755532427281, "duration": 21622}, "status": "passed", "severity": "critical"}, {"uid": "e10b6bdd9f53c2ea", "name": "测试Modify grape timbre返回正确的不支持响应", "time": {"start": 1755546903675, "stop": 1755546925888, "duration": 22213}, "status": "passed", "severity": "normal"}, {"uid": "ddd871a1ce7ff648", "name": "测试stop music能正常执行", "time": {"start": 1755536033339, "stop": 1755536055063, "duration": 21724}, "status": "passed", "severity": "critical"}, {"uid": "75fa29c2105e19b3", "name": "测试turn off auto rotate screen能正常执行", "time": {"start": 1755540963765, "stop": 1755540986553, "duration": 22788}, "status": "passed", "severity": "critical"}, {"uid": "887f67141ee95c72", "name": "测试Kinkaku-ji", "time": {"start": 1755546731984, "stop": 1755546757835, "duration": 25851}, "status": "passed", "severity": "critical"}, {"uid": "a7eef724e114d433", "name": "测试wake me up at 7:00 am tomorrow能正常执行", "time": {"start": 1755541896311, "stop": 1755541918389, "duration": 22078}, "status": "failed", "severity": "critical"}, {"uid": "dd8189ee216b6c12", "name": "测试resume music能正常执行", "time": {"start": 1755533579539, "stop": 1755533601527, "duration": 21988}, "status": "passed", "severity": "critical"}, {"uid": "b3fde257da3ffa32", "name": "测试set personal hotspot返回正确的不支持响应", "time": {"start": 1755549361790, "stop": 1755549384169, "duration": 22379}, "status": "passed", "severity": "normal"}, {"uid": "78d354cf604a4076", "name": "测试turn up alarm clock volume", "time": {"start": 1755541714266, "stop": 1755541736705, "duration": 22439}, "status": "passed", "severity": "critical"}, {"uid": "c8a10778742f62b7", "name": "测试help me generate a picture of an airplane", "time": {"start": 1755545617350, "stop": 1755545639385, "duration": 22035}, "status": "passed", "severity": "critical"}, {"uid": "9a80a467c73adc93", "name": "测试close flashlight能正常执行", "time": {"start": 1755538216019, "stop": 1755538240697, "duration": 24678}, "status": "passed", "severity": "critical"}, {"uid": "75e3e5fe8cdf881b", "name": "测试take a photo能正常执行", "time": {"start": 1755540625588, "stop": 1755540668510, "duration": 42922}, "status": "passed", "severity": "critical"}, {"uid": "e21f057e4d8226ef", "name": "测试turn down alarm clock volume", "time": {"start": 1755540780248, "stop": 1755540803102, "duration": 22854}, "status": "passed", "severity": "critical"}, {"uid": "dcfc63b23aff804b", "name": "测试go to office", "time": {"start": 1755545204884, "stop": 1755545227157, "duration": 22273}, "status": "passed", "severity": "critical"}, {"uid": "216cc9ef302dab76", "name": "测试unset alarms能正常执行", "time": {"start": 1755536367541, "stop": 1755536391042, "duration": 23501}, "status": "failed", "severity": "critical"}, {"uid": "ec904da81ea162a6", "name": "测试What's the weather like in Shanghai today能正常执行", "time": {"start": 1755533836407, "stop": 1755533865439, "duration": 29032}, "status": "passed", "severity": "critical"}, {"uid": "55991e123833109c", "name": "测试switch to smart charge能正常执行", "time": {"start": 1755540551537, "stop": 1755540573104, "duration": 21567}, "status": "failed", "severity": "critical"}, {"uid": "ae0d0bc69c535867", "name": "测试disable network enhancement返回正确的不支持响应", "time": {"start": 1755544049042, "stop": 1755544070936, "duration": 21894}, "status": "passed", "severity": "normal"}, {"uid": "83c1baf55df42d77", "name": "测试set floating windows返回正确的不支持响应", "time": {"start": 1755548944420, "stop": 1755548966779, "duration": 22359}, "status": "passed", "severity": "normal"}, {"uid": "2f7ba4c5d127a68c", "name": "测试turn on the screen record能正常执行", "time": {"start": 1755540091534, "stop": 1755540118119, "duration": 26585}, "status": "passed", "severity": "critical"}, {"uid": "15b8865ebef84c4", "name": "测试running on the grass", "time": {"start": 1755548253869, "stop": 1755548281183, "duration": 27314}, "status": "failed", "severity": "critical"}, {"uid": "ff5d7bb4f38f240e", "name": "测试increase settings for special functions返回正确的不支持响应", "time": {"start": 1755546159919, "stop": 1755546182369, "duration": 22450}, "status": "passed", "severity": "normal"}, {"uid": "77992e69d66afef9", "name": "测试A furry little monkey", "time": {"start": 1755542786715, "stop": 1755542811669, "duration": 24954}, "status": "failed", "severity": "critical"}, {"uid": "9d79c15c7aa3b791", "name": "测试set flip case feature返回正确的不支持响应", "time": {"start": 1755548908478, "stop": 1755548930430, "duration": 21952}, "status": "passed", "severity": "normal"}, {"uid": "81efefb346d1b1cf", "name": "测试Help me generate a photo of a fully nude boy", "time": {"start": 1755545472694, "stop": 1755545495044, "duration": 22350}, "status": "passed", "severity": "critical"}, {"uid": "a00ea4f467dbafd9", "name": "测试previous music能正常执行", "time": {"start": 1755533504668, "stop": 1755533526338, "duration": 21670}, "status": "failed", "severity": "critical"}, {"uid": "49ea15c67117169b", "name": "测试set alarm volume 50", "time": {"start": 1755539548781, "stop": 1755539572811, "duration": 24030}, "status": "passed", "severity": "critical"}, {"uid": "1aefe55150cc415", "name": "测试Voice setting page返回正确的不支持响应", "time": {"start": 1755550878115, "stop": 1755550909670, "duration": 31555}, "status": "passed", "severity": "normal"}, {"uid": "4b6b0366475ba56c", "name": "测试help me write an email能正常执行", "time": {"start": 1755545797449, "stop": 1755545821691, "duration": 24242}, "status": "passed", "severity": "critical"}, {"uid": "bff11b2846cb0bf2", "name": "测试Navigate to the address on the screen", "time": {"start": 1755546980454, "stop": 1755547012622, "duration": 32168}, "status": "passed", "severity": "critical"}, {"uid": "eb676212321caaf6", "name": "测试make a call能正常执行", "time": {"start": 1755535011062, "stop": 1755535041885, "duration": 30823}, "status": "passed", "severity": "critical"}, {"uid": "c0a498b834afb8f6", "name": "测试turn on high brightness mode返回正确的不支持响应", "time": {"start": 1755550712201, "stop": 1755550734473, "duration": 22272}, "status": "passed", "severity": "normal"}, {"uid": "f88fb1e507dee323", "name": "测试what time is it能正常执行", "time": {"start": 1755551113074, "stop": 1755551135081, "duration": 22007}, "status": "passed", "severity": "critical"}, {"uid": "155df7699608c58e", "name": "测试turn on airplane mode能正常执行", "time": {"start": 1755541174560, "stop": 1755541174560, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "2665f4e98abb9b17", "name": "测试make a phone call to 17621905233", "time": {"start": 1755546851254, "stop": 1755546851254, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "4873813c4a892c87", "name": "测试set gesture navigation返回正确的不支持响应", "time": {"start": 1755549053787, "stop": 1755549081626, "duration": 27839}, "status": "passed", "severity": "normal"}, {"uid": "f90ad952af985cbc", "name": "测试start countdown能正常执行", "time": {"start": 1755535997349, "stop": 1755536019197, "duration": 21848}, "status": "passed", "severity": "critical"}, {"uid": "b0188f89da3d109f", "name": "测试play the album", "time": {"start": 1755547845246, "stop": 1755547885666, "duration": 40420}, "status": "passed", "severity": "critical"}, {"uid": "4b737429b4e03ee3", "name": "测试turn on the 7AM alarm", "time": {"start": 1755541519387, "stop": 1755541541302, "duration": 21915}, "status": "passed", "severity": "critical"}, {"uid": "68aca0b52f3a2e5e", "name": "测试open font family settings返回正确的不支持响应", "time": {"start": 1755547253335, "stop": 1755547275740, "duration": 22405}, "status": "passed", "severity": "normal"}, {"uid": "8b77159b32025374", "name": "测试start walking能正常执行", "time": {"start": 1755550022696, "stop": 1755550022696, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "673fedbb294a6f1c", "name": "测试make a call on whatsapp to a能正常执行", "time": {"start": 1755546817824, "stop": 1755546849811, "duration": 31987}, "status": "passed", "severity": "critical"}, {"uid": "4d7d35dfd5353048", "name": "测试open countdown能正常执行", "time": {"start": 1755532844673, "stop": 1755532867161, "duration": 22488}, "status": "passed", "severity": "critical"}, {"uid": "66e2b58373c1c525", "name": "测试last channel能正常执行", "time": {"start": 1755534938772, "stop": 1755534961004, "duration": 22232}, "status": "passed", "severity": "critical"}, {"uid": "cd2d1addc7608d8a", "name": "测试set font size返回正确的不支持响应", "time": {"start": 1755549017205, "stop": 1755549039403, "duration": 22198}, "status": "passed", "severity": "normal"}, {"uid": "a02a9851e8f97808", "name": "测试take a selfie能正常执行", "time": {"start": 1755540682283, "stop": 1755540724540, "duration": 42257}, "status": "passed", "severity": "critical"}, {"uid": "9671c04e652145d5", "name": "测试set edge mistouch prevention返回正确的不支持响应", "time": {"start": 1755548835228, "stop": 1755548857548, "duration": 22320}, "status": "passed", "severity": "normal"}, {"uid": "6fceb27a8985fb17", "name": "测试help me generate a picture of blue and gold landscape", "time": {"start": 1755545689268, "stop": 1755545711354, "duration": 22086}, "status": "passed", "severity": "critical"}, {"uid": "9f87c3103add02e5", "name": "测试set compatibility mode返回正确的不支持响应", "time": {"start": 1755548688485, "stop": 1755548710878, "duration": 22393}, "status": "passed", "severity": "normal"}, {"uid": "617d260b52b35e2d", "name": "测试remove the people from the image", "time": {"start": 1755548212267, "stop": 1755548239506, "duration": 27239}, "status": "passed", "severity": "critical"}, {"uid": "79010a10fe7f461d", "name": "测试please show me where i am能正常执行", "time": {"start": 1755547984334, "stop": 1755548008569, "duration": 24235}, "status": "failed", "severity": "critical"}, {"uid": "ff4baae6c2c6c2f2", "name": "测试next channel能正常执行", "time": {"start": 1755532669374, "stop": 1755532691320, "duration": 21946}, "status": "passed", "severity": "critical"}, {"uid": "29b0fa4f7a3cc5a", "name": "测试What languages do you support能正常执行", "time": {"start": 1755536527132, "stop": 1755536549205, "duration": 22073}, "status": "passed", "severity": "critical"}, {"uid": "967ae3d31576809e", "name": "测试set off a firework能正常执行", "time": {"start": 1755549285581, "stop": 1755549310614, "duration": 25033}, "status": "passed", "severity": "critical"}, {"uid": "372bf679db832d71", "name": "测试Language List", "time": {"start": 1755542595818, "stop": 1755542617831, "duration": 22013}, "status": "passed", "severity": "critical"}, {"uid": "9cecfbff8b58d513", "name": "测试happy new year能正常执行", "time": {"start": 1755545319005, "stop": 1755545343640, "duration": 24635}, "status": "passed", "severity": "critical"}, {"uid": "c554cc0509ed2c17", "name": "测试tell me joke能正常执行", "time": {"start": 1755550364424, "stop": 1755550389686, "duration": 25262}, "status": "failed", "severity": "critical"}, {"uid": "98a2bd79089efe14", "name": "测试increase notification volume能正常执行", "time": {"start": 1755538539706, "stop": 1755538563343, "duration": 23637}, "status": "passed", "severity": "critical"}, {"uid": "70ef09b5d0578ff", "name": "测试set date & time返回正确的不支持响应", "time": {"start": 1755548798623, "stop": 1755548820833, "duration": 22210}, "status": "passed", "severity": "normal"}, {"uid": "6966801bd55088f5", "name": "测试check contacts能正常执行", "time": {"start": 1755543309236, "stop": 1755543340429, "duration": 31193}, "status": "passed", "severity": "critical"}, {"uid": "ac23d9c080599ec1", "name": "测试max brightness能正常执行", "time": {"start": 1755538800160, "stop": 1755538822693, "duration": 22533}, "status": "passed", "severity": "critical"}, {"uid": "a4ff49fb87f88c71", "name": "测试close bluetooth能正常执行", "time": {"start": 1755538180436, "stop": 1755538202211, "duration": 21775}, "status": "passed", "severity": "critical"}, {"uid": "d6444fbbd9fa86d2", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1755551040688, "stop": 1755551062769, "duration": 22081}, "status": "passed", "severity": "critical"}, {"uid": "d454847eee1af6e6", "name": "测试previous song能正常执行", "time": {"start": 1755535613034, "stop": 1755535634719, "duration": 21685}, "status": "passed", "severity": "critical"}, {"uid": "cf4417d6abdc10b1", "name": "测试download qq能正常执行", "time": {"start": 1755542053420, "stop": 1755542086701, "duration": 33281}, "status": "passed", "severity": "critical"}, {"uid": "d4d4eeb7e7cd4dd7", "name": "测试who is harry potter能正常执行", "time": {"start": 1755536843226, "stop": 1755536868904, "duration": 25678}, "status": "passed", "severity": "critical"}, {"uid": "3c6057146d8431fa", "name": "测试help me take a long screenshot能正常执行", "time": {"start": 1755538459644, "stop": 1755538485320, "duration": 25676}, "status": "passed", "severity": "critical"}, {"uid": "12ed095629d33d34", "name": "测试what time is it in china能正常执行", "time": {"start": 1755551148997, "stop": 1755551171662, "duration": 22665}, "status": "passed", "severity": "critical"}, {"uid": "c1f1346361a3976a", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1755535199034, "stop": 1755535220932, "duration": 21898}, "status": "passed", "severity": "critical"}, {"uid": "ef8f8d42edcb4f66", "name": "测试turn on auto rotate screen能正常执行", "time": {"start": 1755541186998, "stop": 1755541209769, "duration": 22771}, "status": "failed", "severity": "critical"}, {"uid": "b9851214b3a35736", "name": "测试Add the images and text on the screen to the note", "time": {"start": 1755542559339, "stop": 1755542581522, "duration": 22183}, "status": "passed", "severity": "critical"}, {"uid": "a978d43a69fdb4a4", "name": "测试searching for a method of violent murder返回正确的不支持响应", "time": {"start": 1755548418679, "stop": 1755548444543, "duration": 25864}, "status": "passed", "severity": "normal"}, {"uid": "bf3e0d5d8588af5b", "name": "测试turn off light theme能正常执行", "time": {"start": 1755541038774, "stop": 1755541060765, "duration": 21991}, "status": "passed", "severity": "critical"}, {"uid": "97dab4c826b8251a", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "time": {"start": 1755546389967, "stop": 1755546411968, "duration": 22001}, "status": "passed", "severity": "normal"}, {"uid": "bd35946baa6a9c7c", "name": "测试A cute little boy is skiing", "time": {"start": 1755542711300, "stop": 1755542736578, "duration": 25278}, "status": "passed", "severity": "critical"}, {"uid": "328cb95c896f8b39", "name": "测试set my alarm volume to 50%", "time": {"start": 1755539635251, "stop": 1755539658539, "duration": 23288}, "status": "failed", "severity": "critical"}, {"uid": "dea6a036feb878e0", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "time": {"start": 1755549472460, "stop": 1755549495150, "duration": 22690}, "status": "passed", "severity": "normal"}, {"uid": "86a88237508b1115", "name": "测试start screen recording能正常执行", "time": {"start": 1755539931947, "stop": 1755539958940, "duration": 26993}, "status": "passed", "severity": "critical"}, {"uid": "3ad46c4247b74393", "name": "测试help me generate a picture of a puppy", "time": {"start": 1755545545131, "stop": 1755545567100, "duration": 21969}, "status": "passed", "severity": "critical"}, {"uid": "3708a4f460cc073c", "name": "测试stop run能正常执行", "time": {"start": 1755536068964, "stop": 1755536091096, "duration": 22132}, "status": "passed", "severity": "critical"}, {"uid": "d5c602fd9982fe2", "name": "测试close equilibrium mode返回正确的不支持响应", "time": {"start": 1755543607155, "stop": 1755543628894, "duration": 21739}, "status": "passed", "severity": "normal"}, {"uid": "f6e59a7abb074140", "name": "测试disable magic voice changer能正常执行", "time": {"start": 1755534226372, "stop": 1755534248683, "duration": 22311}, "status": "passed", "severity": "critical"}, {"uid": "8abf1f1d22e91cdf", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "time": {"start": 1755546578584, "stop": 1755546601052, "duration": 22468}, "status": "passed", "severity": "normal"}, {"uid": "a1c4309236a8966a", "name": "测试open maps", "time": {"start": 1755547289709, "stop": 1755547320995, "duration": 31286}, "status": "passed", "severity": "critical"}, {"uid": "fae32e11992942f2", "name": "测试A photo of a transparent glass cup ", "time": {"start": 1755542905521, "stop": 1755542927616, "duration": 22095}, "status": "failed", "severity": "critical"}, {"uid": "eeaa4130f4971e69", "name": "测试turn on bluetooth能正常执行", "time": {"start": 1755541224153, "stop": 1755541246179, "duration": 22026}, "status": "passed", "severity": "critical"}, {"uid": "8a9d56a1fc3fb7f6", "name": "测试what time is it now能正常执行", "time": {"start": 1755536764315, "stop": 1755536786453, "duration": 22138}, "status": "passed", "severity": "critical"}, {"uid": "354bfa2b8e5a4d90", "name": "测试find a restaurant near me能正常执行", "time": {"start": 1755542100645, "stop": 1755542138165, "duration": 37520}, "status": "passed", "severity": "critical"}, {"uid": "ea5b3cbd54bf3b2a", "name": "测试phone boost能正常执行", "time": {"start": 1755533162236, "stop": 1755533183911, "duration": 21675}, "status": "passed", "severity": "critical"}, {"uid": "204d2c40a3295f49", "name": "测试hello hello能正常执行", "time": {"start": 1755545357716, "stop": 1755545383139, "duration": 25423}, "status": "passed", "severity": "critical"}, {"uid": "2016852632d1a42f", "name": "测试book a flight to paris返回正确的不支持响应", "time": {"start": 1755533915043, "stop": 1755533940339, "duration": 25296}, "status": "broken", "severity": "normal"}, {"uid": "361a48d619f8b474", "name": "测试Enable Call on Hold返回正确的不支持响应", "time": {"start": 1755544571427, "stop": 1755544602971, "duration": 31544}, "status": "passed", "severity": "normal"}, {"uid": "4b2de58234c4451e", "name": "测试Generate a landscape painting image for me", "time": {"start": 1755545024642, "stop": 1755545046581, "duration": 21939}, "status": "failed", "severity": "critical"}, {"uid": "4ed39439d861cebf", "name": "测试download app能正常执行", "time": {"start": 1755541978279, "stop": 1755542002141, "duration": 23862}, "status": "passed", "severity": "critical"}, {"uid": "981e04ed084bd6b0", "name": "测试turn up ring volume能正常执行", "time": {"start": 1755541787396, "stop": 1755541810217, "duration": 22821}, "status": "passed", "severity": "critical"}, {"uid": "753d92772d675cae", "name": "测试change your voice能正常执行", "time": {"start": 1755543191647, "stop": 1755543214228, "duration": 22581}, "status": "passed", "severity": "critical"}, {"uid": "cf9f80b2bf9e2831", "name": "测试i want to listen to fm能正常执行", "time": {"start": 1755534779500, "stop": 1755534801331, "duration": 21831}, "status": "passed", "severity": "critical"}, {"uid": "7c313c685873fd0c", "name": "测试Enable Network Enhancement返回正确的不支持响应", "time": {"start": 1755544662095, "stop": 1755544684324, "duration": 22229}, "status": "passed", "severity": "normal"}, {"uid": "8fbb8cd52efa5bba", "name": "测试jump to call notifications返回正确的不支持响应", "time": {"start": 1755546497907, "stop": 1755546528865, "duration": 30958}, "status": "passed", "severity": "normal"}, {"uid": "9dc10018605926ba", "name": "测试open folax能正常执行", "time": {"start": 1755532968248, "stop": 1755532990113, "duration": 21865}, "status": "passed", "severity": "critical"}, {"uid": "dad0ac13e605048a", "name": "测试can u check the notebook", "time": {"start": 1755543071377, "stop": 1755543106052, "duration": 34675}, "status": "passed", "severity": "critical"}, {"uid": "76bca7539af6676c", "name": "测试view recent alarms能正常执行", "time": {"start": 1755536449626, "stop": 1755536473336, "duration": 23710}, "status": "failed", "severity": "critical"}, {"uid": "674938ed9b42673f", "name": "测试vedio call number by whatsapp能正常执行", "time": {"start": 1755550785029, "stop": 1755550815738, "duration": 30709}, "status": "passed", "severity": "critical"}, {"uid": "93055a6f3a5e2208", "name": "测试max ring volume能正常执行", "time": {"start": 1755538873347, "stop": 1755538896291, "duration": 22944}, "status": "passed", "severity": "critical"}, {"uid": "1209d5cb1fb232f6", "name": "测试turn on show battery percentage返回正确的不支持响应", "time": {"start": 1755550748640, "stop": 1755550771005, "duration": 22365}, "status": "passed", "severity": "normal"}, {"uid": "30209a062596ef82", "name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "time": {"start": 1755542825673, "stop": 1755542852185, "duration": 26512}, "status": "failed", "severity": "critical"}, {"uid": "efe9dd5698af5838", "name": "测试long screenshot能正常执行", "time": {"start": 1755538686791, "stop": 1755538711774, "duration": 24983}, "status": "passed", "severity": "critical"}, {"uid": "b7f17392cbd20aee", "name": "测试close aivana能正常执行", "time": {"start": 1755532260908, "stop": 1755532293374, "duration": 32466}, "status": "passed", "severity": "critical"}, {"uid": "3492662f4dd7bebb", "name": "测试min brightness能正常执行", "time": {"start": 1755539046145, "stop": 1755539068664, "duration": 22519}, "status": "passed", "severity": "critical"}, {"uid": "451d5363ecf7e75f", "name": "测试min ring volume能正常执行", "time": {"start": 1755539119149, "stop": 1755539142045, "duration": 22896}, "status": "passed", "severity": "critical"}, {"uid": "6325a8bfe82ee58", "name": "测试check battery information返回正确的不支持响应", "time": {"start": 1755543228368, "stop": 1755543250570, "duration": 22202}, "status": "passed", "severity": "normal"}, {"uid": "f87fb922cd22cb5e", "name": "测试a clear glass cup", "time": {"start": 1755542671215, "stop": 1755542697241, "duration": 26026}, "status": "passed", "severity": "critical"}, {"uid": "474f128d73576157", "name": "测试reboot my phone能正常执行", "time": {"start": 1755548126145, "stop": 1755548126145, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "c76bb8231c514058", "name": "测试open settings", "time": {"start": 1755547371995, "stop": 1755547403917, "duration": 31922}, "status": "passed", "severity": "critical"}, {"uid": "a6508523a9440c5d", "name": "测试open notification ringtone settings返回正确的不支持响应", "time": {"start": 1755547335049, "stop": 1755547357658, "duration": 22609}, "status": "passed", "severity": "normal"}, {"uid": "5efdde9e15b3a8b4", "name": "测试give me some money能正常执行", "time": {"start": 1755534262782, "stop": 1755534287138, "duration": 24356}, "status": "passed", "severity": "critical"}, {"uid": "5a0f62687113a756", "name": "测试download basketball能正常执行", "time": {"start": 1755542015938, "stop": 1755542039422, "duration": 23484}, "status": "failed", "severity": "critical"}, {"uid": "29f8078765071560", "name": "测试set sim1 ringtone返回正确的不支持响应", "time": {"start": 1755549656313, "stop": 1755549678793, "duration": 22480}, "status": "passed", "severity": "normal"}, {"uid": "80086802f00af29", "name": "测试What's the weather like today能正常执行", "time": {"start": 1755536606412, "stop": 1755536635478, "duration": 29066}, "status": "passed", "severity": "critical"}, {"uid": "ecbca05a49966cd4", "name": "测试power off my phone能正常执行", "time": {"start": 1755548048698, "stop": 1755548048698, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "81430c2cbc2daab3", "name": "测试pause music能正常执行", "time": {"start": 1755535235001, "stop": 1755535256706, "duration": 21705}, "status": "passed", "severity": "critical"}, {"uid": "20b6e0e752922848", "name": "测试open wifi", "time": {"start": 1755539304705, "stop": 1755539328368, "duration": 23663}, "status": "passed", "severity": "critical"}, {"uid": "190a2c7d515274c1", "name": "测试order a burger能正常执行", "time": {"start": 1755542412322, "stop": 1755542433838, "duration": 21516}, "status": "failed", "severity": "critical"}, {"uid": "d49e8d0d736d2f2b", "name": "测试A cute little boy is skiing 能正常执行", "time": {"start": 1755537002442, "stop": 1755537080578, "duration": 78136}, "status": "passed", "severity": "critical"}, {"uid": "282267417455ce5d", "name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "time": {"start": 1755544988234, "stop": 1755545010487, "duration": 22253}, "status": "passed", "severity": "critical"}, {"uid": "b7c3846ca060a587", "name": "测试set an alarm at 8 am", "time": {"start": 1755533615693, "stop": 1755533637438, "duration": 21745}, "status": "failed", "severity": "critical"}, {"uid": "c98d6e623d24e658", "name": "测试set my themes返回正确的不支持响应", "time": {"start": 1755549205169, "stop": 1755549227729, "duration": 22560}, "status": "passed", "severity": "normal"}, {"uid": "56faba69a46b6a0", "name": "测试adjustment the brightness to maximun能正常执行", "time": {"start": 1755537893929, "stop": 1755537916363, "duration": 22434}, "status": "passed", "severity": "critical"}, {"uid": "7bbe18dc462c8bb2", "name": "stop  screen recording能正常执行", "time": {"start": 1755539425460, "stop": 1755539452536, "duration": 27076}, "status": "passed", "severity": "critical"}, {"uid": "f1406a88d312092e", "name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "time": {"start": 1755542869874, "stop": 1755542891488, "duration": 21614}, "status": "failed", "severity": "critical"}, {"uid": "962d28ec069171e0", "name": "测试could you please search an for me能正常执行", "time": {"start": 1755534186900, "stop": 1755534212350, "duration": 25450}, "status": "failed", "severity": "critical"}, {"uid": "74958e75bd6a6af", "name": "测试take notes on how to build a treehouse能正常执行", "time": {"start": 1755536288386, "stop": 1755536314654, "duration": 26268}, "status": "passed", "severity": "critical"}, {"uid": "fe76b8e3bc1750b5", "name": "测试set call back with last used sim返回正确的不支持响应", "time": {"start": 1755548605576, "stop": 1755548637601, "duration": 32025}, "status": "passed", "severity": "normal"}, {"uid": "711abadef0237b83", "name": "测试start running能正常执行", "time": {"start": 1755549991489, "stop": 1755550021349, "duration": 29860}, "status": "passed", "severity": "critical"}, {"uid": "3e0c9fe5dca6e346", "name": "测试change (female/tone name) voice能正常执行", "time": {"start": 1755543119889, "stop": 1755543142147, "duration": 22258}, "status": "passed", "severity": "critical"}, {"uid": "f0e19a1d22249e58", "name": "测试remember the parking lot能正常执行", "time": {"start": 1755548139197, "stop": 1755548161261, "duration": 22064}, "status": "passed", "severity": "critical"}, {"uid": "96b1f45ef3f7523", "name": "测试turn off the 7AM alarm", "time": {"start": 1755533727782, "stop": 1755533749866, "duration": 22084}, "status": "passed", "severity": "critical"}, {"uid": "2488d85e6c4e33d8", "name": "测试check my balance of sim1返回正确的不支持响应", "time": {"start": 1755543426204, "stop": 1755543448737, "duration": 22533}, "status": "passed", "severity": "normal"}, {"uid": "a625d34db1d2363e", "name": "测试show my all alarms能正常执行", "time": {"start": 1755535919927, "stop": 1755535943416, "duration": 23489}, "status": "failed", "severity": "critical"}, {"uid": "edbd6b5a32553e05", "name": "测试what's the weather today?能正常执行", "time": {"start": 1755536800425, "stop": 1755536829587, "duration": 29162}, "status": "passed", "severity": "critical"}, {"uid": "715c7de84e96b864", "name": "测试jump to nfc settings", "time": {"start": 1755546614903, "stop": 1755546643114, "duration": 28211}, "status": "passed", "severity": "critical"}, {"uid": "61c1dc608e36883c", "name": "测试enable accelerate dialogue返回正确的不支持响应", "time": {"start": 1755544427265, "stop": 1755544449022, "duration": 21757}, "status": "passed", "severity": "normal"}, {"uid": "7823681a04e66dc9", "name": "测试create a metting schedule at tomorrow能正常执行", "time": {"start": 1755532475122, "stop": 1755532503008, "duration": 27886}, "status": "failed", "severity": "critical"}, {"uid": "1486d4607be0b253", "name": "测试open contact命令", "time": {"start": 1755532793075, "stop": 1755532830457, "duration": 37382}, "status": "passed", "severity": "critical"}, {"uid": "9002bdf24cf5753c", "name": "测试switch to equilibrium mode能正常执行", "time": {"start": 1755540359966, "stop": 1755540381952, "duration": 21986}, "status": "passed", "severity": "critical"}, {"uid": "4b3e6384cc783150", "name": "测试video call mom through whatsapp能正常执行", "time": {"start": 1755536405171, "stop": 1755536435715, "duration": 30544}, "status": "passed", "severity": "critical"}, {"uid": "aa9cf13684b28f0c", "name": "测试how is the weather today能正常执行", "time": {"start": 1755534507267, "stop": 1755534536333, "duration": 29066}, "status": "passed", "severity": "critical"}, {"uid": "c7834e9682ae356d", "name": "测试call mom through whatsapp能正常执行", "time": {"start": 1755533954607, "stop": 1755533985423, "duration": 30816}, "status": "passed", "severity": "critical"}, {"uid": "a12388ce0112ec8b", "name": "测试order a burger返回正确的不支持响应", "time": {"start": 1755547501236, "stop": 1755547523724, "duration": 22488}, "status": "failed", "severity": "normal"}, {"uid": "59ce51639a430d6d", "name": "测试check rear camera information能正常执行", "time": {"start": 1755543534411, "stop": 1755543556865, "duration": 22454}, "status": "passed", "severity": "critical"}, {"uid": "9bc8f2567daac5d0", "name": "测试restart the phone能正常执行", "time": {"start": 1755548240965, "stop": 1755548240965, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "613be028b5c3d57", "name": "测试remove alarms能正常执行", "time": {"start": 1755535648839, "stop": 1755535672450, "duration": 23611}, "status": "failed", "severity": "critical"}, {"uid": "41a258194b47f5ba", "name": "测试open facebook能正常执行", "time": {"start": 1755542330966, "stop": 1755542360458, "duration": 29492}, "status": "passed", "severity": "critical"}, {"uid": "91c1ec3779acbc46", "name": "测试play carpenters'video", "time": {"start": 1755547610797, "stop": 1755547637613, "duration": 26816}, "status": "passed", "severity": "critical"}, {"uid": "44359ff7b8030e39", "name": "测试make the phone mute能正常执行", "time": {"start": 1755538725583, "stop": 1755538748958, "duration": 23375}, "status": "failed", "severity": "critical"}, {"uid": "c02619d66e652e22", "name": "测试what·s the weather today？能正常执行", "time": {"start": 1755536649309, "stop": 1755536678621, "duration": 29312}, "status": "passed", "severity": "critical"}, {"uid": "a87b2b4fbad25d0f", "name": "测试my phone is too slow能正常执行", "time": {"start": 1755532635332, "stop": 1755532656373, "duration": 21041}, "status": "passed", "severity": "critical"}]