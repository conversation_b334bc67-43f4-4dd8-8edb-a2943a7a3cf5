{"total": 6, "items": [{"uid": "09cb3650ff0a2cc2af23d31dd3c975a2", "name": "testcases.test_ella.unsupported_commands", "statistic": {"failed": 26, "broken": 1, "skipped": 8, "passed": 200, "unknown": 0, "total": 235}}, {"uid": "4159dc35ce06d1422bb1b7c5665d834a", "name": "testcases.test_ella.system_coupling", "statistic": {"failed": 19, "broken": 0, "skipped": 4, "passed": 86, "unknown": 0, "total": 109}}, {"uid": "0a5f897bb744ec2f8b960fc5954cddf6", "name": "testcases.test_ella.dialogue", "statistic": {"failed": 11, "broken": 2, "skipped": 0, "passed": 67, "unknown": 0, "total": 80}}, {"uid": "5948c7c27387d214d4b5e1b876d4cb27", "name": "testcases.test_ella.component_coupling", "statistic": {"failed": 5, "broken": 0, "skipped": 0, "passed": 34, "unknown": 0, "total": 39}}, {"uid": "2660f6320a566ad526d6ea679fb2528f", "name": "testcases.test_ella.third_coupling", "statistic": {"failed": 3, "broken": 0, "skipped": 0, "passed": 11, "unknown": 0, "total": 14}}, {"uid": "6dc11cb9bb372733a2ed6f167a12d612", "name": "testcases.test_ella.self_function", "statistic": {"failed": 3, "broken": 0, "skipped": 0, "passed": 5, "unknown": 0, "total": 8}}]}