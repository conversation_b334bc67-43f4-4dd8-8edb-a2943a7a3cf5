{"7c50481bc992b9ff109abbeeeece073a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "858c22d7630a2a7d", "status": "passed", "time": {"start": 1755543354491, "stop": 1755543376726, "duration": 22235}}]}, "8a87a1d0f534afc4770901f3d1dfa316": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "95216e3dbb2b6a8e", "status": "passed", "time": {"start": 1755541406362, "stop": 1755541429561, "duration": 23199}}]}, "1f9da5f1f2977bbbae31e0d3334eff82": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2bb3ce4693c21548", "status": "passed", "time": {"start": 1755541823865, "stop": 1755541846352, "duration": 22487}}]}, "4dcdc98a8f38f748728aec71f673e025": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "586d00224ff103cd", "status": "passed", "time": {"start": 1755537293489, "stop": 1755537410226, "duration": 116737}}]}, "8e367b8da758818b9a0fe21deca7ec48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c76ab9470c51fe56", "status": "passed", "time": {"start": 1755549435535, "stop": 1755549457878, "duration": 22343}}]}, "c50847e2010bac3c5a9bb7ff0b690fb6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7d126021ffb45811", "status": "passed", "time": {"start": 1755548336283, "stop": 1755548362375, "duration": 26092}}]}, "783144ed3c9603f07a5306d78cb4fde3": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "73f1f654df418a3e", "status": "skipped", "statusDetails": "Skipped: power off 会导致设备断开，先跳过", "time": {"start": 1755539329699, "stop": 1755539329699, "duration": 0}}]}, "70c9bd8c4aab57e96eb06acb93ca2223": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b6b0366475ba56c", "status": "passed", "time": {"start": 1755545797449, "stop": 1755545821691, "duration": 24242}}]}, "57c053de6acd628d4b4cd1230b702a40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b4b33da4101679c", "status": "passed", "time": {"start": 1755543642589, "stop": 1755543664873, "duration": 22284}}]}, "1d8131ec3deb65d953f2f16c259f261b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "817201f101a3fce8", "status": "passed", "time": {"start": 1755535162990, "stop": 1755535185150, "duration": 22160}}]}, "1a5cbbb97cbe59e003ae71750a8d910f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cc934885a239c888", "status": "passed", "time": {"start": 1755536882797, "stop": 1755536908635, "duration": 25838}}]}, "36066a5bbb1f962a6ad5842baefe4ff3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "91c1ec3779acbc46", "status": "passed", "time": {"start": 1755547610797, "stop": 1755547637613, "duration": 26816}}]}, "fc477656b55eea3a3906a5bdcaa93554": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68e268cc4fc1b311", "status": "passed", "time": {"start": 1755542483785, "stop": 1755542507616, "duration": 23831}}]}, "de0ed312f350c708e7a00bb74aeaac0f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b7c3846ca060a587", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['8:00 AM']\nassert False", "time": {"start": 1755533615693, "stop": 1755533637438, "duration": 21745}}]}, "3eec462c8da39eaf95742ed9ab45b7c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d3dea9e9d4c15593", "status": "passed", "time": {"start": 1755543976460, "stop": 1755543998487, "duration": 22027}}]}, "f27eb1a59c1d5c5c845852e05e6a17d9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3492662f4dd7bebb", "status": "passed", "time": {"start": 1755539046145, "stop": 1755539068664, "duration": 22519}}]}, "a2c60aff2518d86c9e5686c943130f40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "789a9ec2b9f26c16", "status": "passed", "time": {"start": 1755550480853, "stop": 1755550507042, "duration": 26189}}]}, "d45827de1782723ad9f8cd9d38f067dc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe76b8e3bc1750b5", "status": "passed", "time": {"start": 1755548605576, "stop": 1755548637601, "duration": 32025}}]}, "c5debd3414c81b3cc4349236d4020867": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6933b6fb12ae346d", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['your Infinix phone assistant', 'your TECNO phone assistant']，实际响应: '['i am your voice assistant', '', '', '', \"That's right! I'm here to assist you. How can I help you today?\", 'Generated by AI, for reference only', \"Dialogue Explore Refresh Can physics & chemistry problems be solved by photo? AI Reshaping HR Roles Why do metals expand when heated i am your voice assistant That's right! I'm here to assist you. How can I help you today? Generated by AI, for reference only What are your main capabilities? What are your limitations as a voice assistant? How do you process user requests? DeepSeek-R1 Feel free to ask me any questions… 03:39\"]'\nassert False", "time": {"start": 1755545950033, "stop": 1755545975296, "duration": 25263}}]}, "9b6faa79e3fe09fed639b1092082745b": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "dc1728464f2098ce", "status": "skipped", "statusDetails": "Skipped: 语言设置为中文，影响别的Case，先跳过", "time": {"start": 1755538030786, "stop": 1755538030786, "duration": 0}}]}, "da0740a44317121ce1879d022e95ae23": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d49e8d0d736d2f2b", "status": "passed", "time": {"start": 1755537002442, "stop": 1755537080578, "duration": 78136}}]}, "19cc9ff22947964a912a8f57a87a3c68": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "962d28ec069171e0", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['I am sorry', 'I can help you with that']，实际响应: '['could you please search an for me', '', '', '', 'I am sorry, I am unable to search for you at this time.', 'Generated by AI, for reference only', '00:23 Dialogue Explore Arsenal, Chelsea in 5/1 Acca Tip How soothe spicy food stomach pain? How to solve primary & junior high questions? could you please search an for me I am sorry, I am unable to search for you at this time. Generated by AI, for reference only What can you search for me today? Search for the weather forecast now? Can you search for restaurants nearby? DeepSeek-R1 Feel free to ask me any questions…']'\nassert False", "time": {"start": 1755534186900, "stop": 1755534212350, "duration": 25450}}]}, "1bf9bd9c91ab7da6f818ff587cfff7da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70ae52d960b93bbd", "status": "passed", "time": {"start": 1755535571881, "stop": 1755535599049, "duration": 27168}}]}, "b68db9e7b007fe641926babf537afa6c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e34cb01b2c00ec2a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Brightness goes down']\nassert False", "time": {"start": 1755538299424, "stop": 1755538322745, "duration": 23321}}]}, "3bd2ea64304a19b2c3694e3a1975c668": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "81efefb346d1b1cf", "status": "passed", "time": {"start": 1755545472694, "stop": 1755545495044, "duration": 22350}}]}, "e8f9ac327bc5f166a4c4a14509f365bf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce0cbaec22f70255", "status": "passed", "time": {"start": 1755541113139, "stop": 1755541135920, "duration": 22781}}]}, "ac6792be4ebb553a5655a2c44aca218c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80086802f00af29", "status": "passed", "time": {"start": 1755536606412, "stop": 1755536635478, "duration": 29066}}]}, "6d49aaf4a5e11b32b961aa0aa3dbbf6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a009973ed07360d8", "status": "passed", "time": {"start": 1755546940225, "stop": 1755546965932, "duration": 25707}}]}, "6c59bb8b6ba250c58da738ab8237ed3c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c76bbb443caa181", "status": "passed", "time": {"start": 1755535399772, "stop": 1755535424407, "duration": 24635}}]}, "b236a18e19a6aa2218c85b80afef2746": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "59a4ff0abe989014", "status": "passed", "time": {"start": 1755535127017, "stop": 1755535149023, "duration": 22006}}]}, "13916c423237bf06a74b0b109aad0d66": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "684fcf296df9cee5", "status": "failed", "statusDetails": "AssertionError: 初始=0, 最终=0, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '', '', '']'\nassert 0 == 7", "time": {"start": 1755539672907, "stop": 1755539696420, "duration": 23513}}]}, "7ec382c77bede0ad015817770cd9e1eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "409e680a2a7a5258", "status": "passed", "time": {"start": 1755535055553, "stop": 1755535077198, "duration": 21645}}]}, "5dff8ffa0041df33df80919398086e48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a0520ca561c270a8", "status": "passed", "time": {"start": 1755540515305, "stop": 1755540537560, "duration": 22255}}]}, "f622c7c4831272dc58cb99e6af8d9943": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d621004ece966b52", "status": "passed", "time": {"start": 1755542152419, "stop": 1755542185515, "duration": 33096}}]}, "5fc780d1e7f790011f0e4a521e125a16": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9401f2aafc72cbb6", "status": "passed", "time": {"start": 1755549729979, "stop": 1755549752409, "duration": 22430}}]}, "e2beda2a0bda4155b33d47f14bdcb9ed": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8e7863fa9e0516bc", "status": "passed", "time": {"start": 1755548651804, "stop": 1755548674233, "duration": 22429}}]}, "468e62202269d66d1ea3c0ae6e2a0e21": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f1406a88d312092e", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "time": {"start": 1755542869874, "stop": 1755542891488, "duration": 21614}}]}, "2b244b852ff1236f560ec792596ae556": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a12388ce0112ec8b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755547501236, "stop": 1755547523724, "duration": 22488}}]}, "9e84af065ec0018044fd43f37b4c2179": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3177d325c06c0ced", "status": "passed", "time": {"start": 1755545509327, "stop": 1755545531209, "duration": 21882}}]}, "148d3ba280bfe2b41b8464beec5f6763": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dbd33f4313f9e7e1", "status": "passed", "time": {"start": 1755533344992, "stop": 1755533383918, "duration": 38926}}]}, "de524bccf252aabc016822f1a65de7f4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ddc06db5db20b677", "status": "passed", "time": {"start": 1755535091232, "stop": 1755535113013, "duration": 21781}}]}, "0fa1017773031b1388876c73f0e0e653": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "78d354cf604a4076", "status": "passed", "time": {"start": 1755541714266, "stop": 1755541736705, "duration": 22439}}]}, "fe7a0f349fbd2027990e72ab5a6650f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "15453db92b625b22", "status": "passed", "time": {"start": 1755541750629, "stop": 1755541773466, "duration": 22837}}]}, "fe3d09fe0bad56e7804ef2f5ea49d283": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c6057146d8431fa", "status": "passed", "time": {"start": 1755538459644, "stop": 1755538485320, "duration": 25676}}]}, "b3a1d4f5c4ea6e3e176798cf3deda55b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "878b77da97742d88", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755544230502, "stop": 1755544252918, "duration": 22416}}]}, "04cef4934fe29e90ca0248af7b395794": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b9bb04eb7f0c695", "status": "passed", "time": {"start": 1755540205374, "stop": 1755540227557, "duration": 22183}}]}, "223077c4b7372197db11e72856c8b7e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "887f67141ee95c72", "status": "passed", "time": {"start": 1755546731984, "stop": 1755546757835, "duration": 25851}}]}, "acca7d0b06a28ad8e6ccfe3c35828ce1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "edbd6b5a32553e05", "status": "passed", "time": {"start": 1755536800425, "stop": 1755536829587, "duration": 29162}}]}, "6bdbaaabff6497c5d3be4727f1a7cd8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "61332991e71deb47", "status": "passed", "time": {"start": 1755533689519, "stop": 1755533713980, "duration": 24461}}]}, "693bce6b8bfdefa98827246122355bf1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dc42dd62a92d5d19", "status": "passed", "time": {"start": 1755544951816, "stop": 1755544973951, "duration": 22135}}]}, "762b1ab748e39965c1484eb7fe38bfe4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ea5b3cbd54bf3b2a", "status": "passed", "time": {"start": 1755533162236, "stop": 1755533183911, "duration": 21675}}]}, "d6d97ebce763bf8ead601650bfb2383c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f6e59a7abb074140", "status": "passed", "time": {"start": 1755534226372, "stop": 1755534248683, "duration": 22311}}]}, "2b4b7555520a6b757239820871731d81": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a1c4309236a8966a", "status": "passed", "time": {"start": 1755547289709, "stop": 1755547320995, "duration": 31286}}]}, "e8c3c7bb72cf538a9e89a7b790c5e689": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9a3290f96fe7666b", "status": "passed", "time": {"start": 1755550251513, "stop": 1755550273578, "duration": 22065}}]}, "4aad505422b0d8c87c499477cd89ff5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3d8ea4f9bffaeeda", "status": "passed", "time": {"start": 1755538337147, "stop": 1755538360848, "duration": 23701}}]}, "edb3a77ed85c79b290dd8cce24f372c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd571f0ce49847cb", "status": "passed", "time": {"start": 1755536177633, "stop": 1755536199724, "duration": 22091}}]}, "6c46a38570672e3c21f37ef82690d639": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a02a9851e8f97808", "status": "passed", "time": {"start": 1755540682283, "stop": 1755540724540, "duration": 42257}}]}, "79b68fd9ac84793c5f55250aad03649a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c8fd1a76b13782e", "status": "passed", "time": {"start": 1755540277666, "stop": 1755540299565, "duration": 21899}}]}, "05cfb3e6f186373be53bb1a7166ac69f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "23aff1aca63900f9", "status": "passed", "time": {"start": 1755547899549, "stop": 1755547927800, "duration": 28251}}]}, "ff945a5d436679bddd13261b231955ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "83c1baf55df42d77", "status": "passed", "time": {"start": 1755548944420, "stop": 1755548966779, "duration": 22359}}]}, "ad18e983dce31052b87b7404f3b347ce": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce3ad8cd15332bdc", "status": "passed", "time": {"start": 1755537857648, "stop": 1755537880029, "duration": 22381}}]}, "83e3a1b41834e87017b680efd7c16b92": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "71fced2abbc8460c", "status": "passed", "time": {"start": 1755535957935, "stop": 1755535983319, "duration": 25384}}]}, "50e811b93ce50e5ac8364a9fea07e234": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f034caa98e78f0aa", "status": "passed", "time": {"start": 1755545653284, "stop": 1755545675171, "duration": 21887}}]}, "32ba476d46e86e963aa12ced39981955": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "29e24e26da6b1f7c", "status": "passed", "time": {"start": 1755538143900, "stop": 1755538166705, "duration": 22805}}]}, "b07852caec1a6673427b80552f64be85": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ffe9139580a870b0", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1755538416925, "stop": 1755538445384, "duration": 28459}}]}, "af89bd1d18cd6a175678a8fe1f43ee33": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c76bb8231c514058", "status": "passed", "time": {"start": 1755547371995, "stop": 1755547403917, "duration": 31922}}]}, "039e454ca4c329751543f1bfbb5e008e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5f18b39a20f5d622", "status": "passed", "time": {"start": 1755542242740, "stop": 1755542271847, "duration": 29107}}]}, "f2f6762c5ec83e110ace25b47e3112d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3006e468cff1526", "status": "passed", "time": {"start": 1755534586604, "stop": 1755534615590, "duration": 28986}}]}, "d8a3659601151a79f3b71ba4e47cafee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7cc6c01c614625cd", "status": "passed", "time": {"start": 1755550672216, "stop": 1755550698097, "duration": 25881}}]}, "f9558282973df5c72bd1c57fb0e19984": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "372bf679db832d71", "status": "passed", "time": {"start": 1755542595818, "stop": 1755542617831, "duration": 22013}}]}, "eb142151b4b5ba4125a1a866dc2b58ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "41af1b410102153b", "status": "passed", "time": {"start": 1755549324908, "stop": 1755549347327, "duration": 22419}}]}, "3238a485ed8e76dc2866c0b0bcd4930e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "49ea15c67117169b", "status": "passed", "time": {"start": 1755539548781, "stop": 1755539572811, "duration": 24030}}]}, "ff8706df57207971727cf6e1326d4a26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a6508523a9440c5d", "status": "passed", "time": {"start": 1755547335049, "stop": 1755547357658, "duration": 22609}}]}, "95e68fb1d20b8d7ff190c67b0bbc2ee8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2fc78b29d7c3b8a0", "status": "passed", "time": {"start": 1755536105054, "stop": 1755536127302, "duration": 22248}}]}, "a19924fb0a564cf26596907610c0f678": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e062c6b9a8b843fd", "status": "passed", "time": {"start": 1755534899694, "stop": 1755534924691, "duration": 24997}}]}, "503ff57584874e8387e6b367bfa70c8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "de12e238457e041e", "status": "passed", "time": {"start": 1755544012434, "stop": 1755544034816, "duration": 22382}}]}, "c190fe929896ea57ed1e33f8bc5bf113": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4eadf7aaf6065e14", "status": "passed", "time": {"start": 1755541000559, "stop": 1755541024984, "duration": 24425}}]}, "db313838c77140c89e69785e101f25d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e10b6bdd9f53c2ea", "status": "passed", "time": {"start": 1755546903675, "stop": 1755546925888, "duration": 22213}}]}, "fcfaafeb2b49ed6f468b8db263c64a18": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8d53c70b4d6dbed", "status": "passed", "time": {"start": 1755538577374, "stop": 1755538599601, "duration": 22227}}]}, "7af47ccbdaf69e5292e05041f822c0c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "75fa29c2105e19b3", "status": "passed", "time": {"start": 1755540963765, "stop": 1755540986553, "duration": 22788}}]}, "70c4e0c16f5cb3629f87876768739a8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "753d92772d675cae", "status": "passed", "time": {"start": 1755543191647, "stop": 1755543214228, "duration": 22581}}]}, "c7b8111fa78410a413dfc969cfe6f0e1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "87e4c8157a7c0b0a", "status": "passed", "time": {"start": 1755547417706, "stop": 1755547450006, "duration": 32300}}]}, "8b2d3084bb429ea5def5db416bbf10a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b6e2a81031476512", "status": "passed", "time": {"start": 1755532358236, "stop": 1755532391952, "duration": 33716}}]}, "9da64d3434f91a12d693ed9c71b62e87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9dc10018605926ba", "status": "passed", "time": {"start": 1755532968248, "stop": 1755532990113, "duration": 21865}}]}, "f4da532f5d62abff197a05947efc027a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b888d3bc51fa3b1b", "status": "passed", "time": {"start": 1755534815267, "stop": 1755534846296, "duration": 31029}}]}, "fcda536a017e05b2edb24a2e80ce1ec0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2fb5de1bf6466d20", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['What do you want it to say?']\nassert False", "time": {"start": 1755550287593, "stop": 1755550313863, "duration": 26270}}]}, "d29b58e8e7ac85336b7dc255831fd5ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b0a2a501433e61e", "status": "passed", "time": {"start": 1755535308816, "stop": 1755535333056, "duration": 24240}}]}, "bd4f9d0c0f70cf6b24bb9923810b25c1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bc289b8805ed6f47", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', '\"remember the parking space\" recorded', '', '', '', '']'\nassert False", "time": {"start": 1755548175424, "stop": 1755548197727, "duration": 22303}}]}, "733cc57b9e666f7c16017a85f41c410d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "18d532e0c27f0a7d", "status": "passed", "time": {"start": 1755539192709, "stop": 1755539215056, "duration": 22347}}]}, "f1bf796cd6804ca9b19a3e3f949a04ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "303d6a001e695137", "status": "passed", "time": {"start": 1755546353628, "stop": 1755546375896, "duration": 22268}}]}, "00495562396e9306113e5f37378ac991": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bad169110b617eec", "status": "passed", "time": {"start": 1755545132693, "stop": 1755545154594, "duration": 21901}}]}, "c359e36718cf3ac8fd335c333a6470cf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bff11b2846cb0bf2", "status": "passed", "time": {"start": 1755546980454, "stop": 1755547012622, "duration": 32168}}]}, "71c64c122f83e6fd138db517bbda4aef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dde17385f415640f", "status": "passed", "time": {"start": 1755549918832, "stop": 1755549941568, "duration": 22736}}]}, "49ae31ee7fe8baa7f1604fd83d56bb68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5d13ab9424ca7076", "status": "passed", "time": {"start": 1755543390467, "stop": 1755543412287, "duration": 21820}}]}, "0e5513d569c7270e4332e484218ae36b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "61c1dc608e36883c", "status": "passed", "time": {"start": 1755544427265, "stop": 1755544449022, "duration": 21757}}]}, "fee3033814a8b17ff8c8abe6bbcdc839": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e6acbfc61014ddb0", "status": "passed", "time": {"start": 1755535530009, "stop": 1755535557931, "duration": 27922}}]}, "c796c03cca51cea23bdc87f3f9d6fa95": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "344438313a655b16", "status": "passed", "time": {"start": 1755546317774, "stop": 1755546339813, "duration": 22039}}]}, "e14cd5a605f26d24de7f5f63d4667c68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4873813c4a892c87", "status": "passed", "time": {"start": 1755549053787, "stop": 1755549081626, "duration": 27839}}]}, "4b3ad3bdf0873599e48d8f20d70246c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ac23d9c080599ec1", "status": "passed", "time": {"start": 1755538800160, "stop": 1755538822693, "duration": 22533}}]}, "cb21afc40e4aec32b847936756c8ba6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8e014ba05d1d3d20", "status": "passed", "time": {"start": 1755533307222, "stop": 1755533331544, "duration": 24322}}]}, "cf3df9e3e259f083301aa2ec640729fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f866a2bad0ee5662", "status": "passed", "time": {"start": 1755532931498, "stop": 1755532953979, "duration": 22481}}]}, "dbd7a7f96e1740fa05f50ed6fa7becfb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "19693adbe0f7f612", "status": "passed", "time": {"start": 1755549882185, "stop": 1755549904488, "duration": 22303}}]}, "4bda544c08fa4bc5494c7dda01d4cc77": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "94a260d39cc61397", "status": "passed", "time": {"start": 1755550635665, "stop": 1755550658012, "duration": 22347}}]}, "eb6abc860fad339739076abacb13ac83": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a216829f25a48ac0", "status": "passed", "time": {"start": 1755544915793, "stop": 1755544937936, "duration": 22143}}]}, "217ee9f7b3be9f625903076716d45106": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c862ac6b8bc8ba94", "status": "passed", "time": {"start": 1755547574100, "stop": 1755547596332, "duration": 22232}}]}, "0a0a3640b2ba4adce516043bd9362070": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a87b2b4fbad25d0f", "status": "passed", "time": {"start": 1755532635332, "stop": 1755532656373, "duration": 21041}}]}, "540cff5d6d552c22ec37f66efd17315f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8fbb8cd52efa5bba", "status": "passed", "time": {"start": 1755546497907, "stop": 1755546528865, "duration": 30958}}]}, "87f3dc53ab72c729262e053c16a3dbcb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fe991698e5267e29", "status": "passed", "time": {"start": 1755532440416, "stop": 1755532462076, "duration": 21660}}]}, "d4ded95517fa8a5af49f09554cc49725": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e01919838c9e2688", "status": "passed", "time": {"start": 1755544463027, "stop": 1755544485067, "duration": 22040}}]}, "b9fc05e613fdd4d145434e9cf6378c4b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "216cc9ef302dab76", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms. Which one do you want to turn off? ']\nassert False", "time": {"start": 1755536367541, "stop": 1755536391042, "duration": 23501}}]}, "6075008522e5d0ae1667c4ac4be759eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7a88c8a026436bb", "status": "passed", "time": {"start": 1755533397763, "stop": 1755533436640, "duration": 38877}}]}, "400a9b197316d3b1e59fe33ed78a836a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dad0ac13e605048a", "status": "passed", "time": {"start": 1755543071377, "stop": 1755543106052, "duration": 34675}}]}, "3215de286c6ddd59d6e52a44f2a9967d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e99caa00c608f900", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755544390869, "stop": 1755544413086, "duration": 22217}}]}, "0e7fd56ff1d5c85e0ce1830d9899313d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "25a1d94359b88fb7", "status": "passed", "time": {"start": 1755535438162, "stop": 1755535462677, "duration": 24515}}]}, "657acdf17dda1a11abf6946763f6ed52": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7c313c685873fd0c", "status": "passed", "time": {"start": 1755544662095, "stop": 1755544684324, "duration": 22229}}]}, "104cf8a7ef102b6850b6d14f4cb14052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7d88c44af202bec", "status": "passed", "time": {"start": 1755548761809, "stop": 1755548784471, "duration": 22662}}]}, "a8ceb9cec2faa4662cde95140569091b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "78003ba8ba5d066c", "status": "passed", "time": {"start": 1755547694222, "stop": 1755547737272, "duration": 43050}}]}, "ebd2c9b6cd7c07b69e348328a207e18b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce4bd0701fd3c3fd", "status": "passed", "time": {"start": 1755540926281, "stop": 1755540949515, "duration": 23234}}]}, "1bc9389e45f0f75c30d3dfb39134948d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d7139503c14d5b2d", "status": "passed", "time": {"start": 1755549509614, "stop": 1755549531578, "duration": 21964}}]}, "c9c4c38d0ca341040a41178716623909": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "44359ff7b8030e39", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['make the phone mute', 'Mute is turned on now.', 'Mute', '', '', '']'\nassert None", "time": {"start": 1755538725583, "stop": 1755538748958, "duration": 23375}}]}, "92c8c8e017b096314ffde2f610a6791e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "305a8e737b98a962", "status": "passed", "time": {"start": 1755539586962, "stop": 1755539621174, "duration": 34212}}]}, "123d65cc114fff79e459e14acfbcd445": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "774b232eb8b4279", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on adaptive brightness', 'Auto-brightness is turned on now.', 'Auto-brightness', '', '', '']'\nassert None", "time": {"start": 1755541150238, "stop": 1755541173024, "duration": 22786}}]}, "c046a1c6e6cc8effa10641e329b1cfad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4033ac84f856cd7", "status": "passed", "time": {"start": 1755549766920, "stop": 1755549789346, "duration": 22426}}]}, "459c099a876d1129ddcb7cb28663b756": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f28f303d3b93e43", "status": "passed", "time": {"start": 1755538499457, "stop": 1755538525684, "duration": 26227}}]}, "6f2c4144233271771cdd01a5c48ea3ca": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5a0f62687113a756", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['download basketball', 'No relevant apps found. Please download and try again.', '', '', '', '']'\nassert False", "time": {"start": 1755542015938, "stop": 1755542039422, "duration": 23484}}]}, "70b49b2a6719030c7c51e642fdaec270": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f90ad952af985cbc", "status": "passed", "time": {"start": 1755535997349, "stop": 1755536019197, "duration": 21848}}]}, "4f84a6588e41dde581e4eef4fccd6344": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "245590744fd1760c", "status": "passed", "time": {"start": 1755541932633, "stop": 1755541964641, "duration": 32008}}]}, "d60fcab377d9b5093e0f03ecf20f5d10": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7186c09ba4d6ed7b", "status": "passed", "time": {"start": 1755534340663, "stop": 1755534364374, "duration": 23711}}]}, "6980dbcce9a72cd9dea6dee04c6891de": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "626820f2f317f7d6", "status": "passed", "time": {"start": 1755545397220, "stop": 1755545422399, "duration": 25179}}]}, "7acb737855a3a3110ed556a3e5fe1256": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "97499e652f70f1f2", "status": "passed", "time": {"start": 1755539861533, "stop": 1755539917984, "duration": 56451}}]}, "99709ca7d9951f6f7049b49ea81d0cd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "20b6e0e752922848", "status": "passed", "time": {"start": 1755539304705, "stop": 1755539328368, "duration": 23663}}]}, "44720253edec52ccf0868b33f1938265": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f35ab6662635c7df", "status": "passed", "time": {"start": 1755550560264, "stop": 1755550585695, "duration": 25431}}]}, "ecfbca0f5d1122fac0e0543e38291ce2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2f7ba4c5d127a68c", "status": "passed", "time": {"start": 1755540091534, "stop": 1755540118119, "duration": 26585}}]}, "b89775573784e6ef95769309baebeae4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf4417d6abdc10b1", "status": "passed", "time": {"start": 1755542053420, "stop": 1755542086701, "duration": 33281}}]}, "876e77318cece5d1079b726f0c97bc45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be07492f78740106", "status": "passed", "time": {"start": 1755534111697, "stop": 1755534135236, "duration": 23539}}]}, "3333dd58fd9312a504ae6bc6edf830af": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e68af3605869f6ab", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['These suggestions are for your reference']\nassert False", "time": {"start": 1755536967108, "stop": 1755536988272, "duration": 21164}}]}, "b5e1711cce3102fc710ff74e18bf9129": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b0523e40835680c9", "status": "passed", "time": {"start": 1755549803696, "stop": 1755549826120, "duration": 22424}}]}, "19df0c79ab8c9ce909771e1a9d21fed3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "486198e7ca2f5671", "status": "passed", "time": {"start": 1755550829705, "stop": 1755550864436, "duration": 34731}}]}, "b6eee20d1fad16a048ce8490d7189be4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ee55ab8579ed7a97", "status": "passed", "time": {"start": 1755542941770, "stop": 1755542967243, "duration": 25473}}]}, "7b90ee3ed7bdd2837a37215aac61cfd9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "981e04ed084bd6b0", "status": "passed", "time": {"start": 1755541787396, "stop": 1755541810217, "duration": 22821}}]}, "18377608d977aa5cb5e2fc6b03b9ad05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf36fb78559bcbb3", "status": "passed", "time": {"start": 1755546694143, "stop": 1755546717481, "duration": 23338}}]}, "3a27fef360a79f638f96f0461df262da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9d79c15c7aa3b791", "status": "passed", "time": {"start": 1755548908478, "stop": 1755548930430, "duration": 21952}}]}, "b8a3cad490db8fccbe1d65628faf6743": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b8b38e493df50885", "status": "passed", "time": {"start": 1755547158710, "stop": 1755547183523, "duration": 24813}}]}, "2428ad915810150c12838b88ee13f49c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eb676212321caaf6", "status": "passed", "time": {"start": 1755535011062, "stop": 1755535041885, "duration": 30823}}]}, "563b9c2c8d2e1d68901eeac733e12913": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "472b2f5bc7ebd088", "status": "passed", "time": {"start": 1755538650413, "stop": 1755538672876, "duration": 22463}}]}, "9511be8e6426d5078713c6e78f3b02e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "71be630e71caa50e", "status": "passed", "time": {"start": 1755539229114, "stop": 1755539251618, "duration": 22504}}]}, "fc75b92fb4a100575b2c948dd6c5a008": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a69cd11544f75120", "status": "passed", "time": {"start": 1755544736044, "stop": 1755544757995, "duration": 21951}}]}, "ae86b8d534909e1e7c8c7adb4ee39e5c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c7c9f887c88cd32", "status": "passed", "time": {"start": 1755541369911, "stop": 1755541391953, "duration": 22042}}]}, "fa47cb0b4427dd62fb8f91c9e5e15ace": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f88fb1e507dee323", "status": "passed", "time": {"start": 1755551113074, "stop": 1755551135081, "duration": 22007}}]}, "57acf2797af332487c1fdb9a53a30e4f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c28179de4e531562", "status": "passed", "time": {"start": 1755544499399, "stop": 1755544521558, "duration": 22159}}]}, "b7bfa1ba094155307273abc83c43a0d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "81430c2cbc2daab3", "status": "passed", "time": {"start": 1755535235001, "stop": 1755535256706, "duration": 21705}}]}, "a416a85ec867e3b2cfd6e23150d72859": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6e8cfebab1b113de", "status": "passed", "time": {"start": 1755543714952, "stop": 1755543736798, "duration": 21846}}]}, "c0d6ce0b7c5e41242c01a6e0c0186608": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "16d295102618a407", "status": "passed", "time": {"start": 1755536728444, "stop": 1755536750354, "duration": 21910}}]}, "643a7bbfbf5c5eedbae7ae814fbc8b52": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "55991e123833109c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755540551537, "stop": 1755540573104, "duration": 21567}}]}, "3dae350db69abded432b3e7f5f8463c8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7becbd0e020b5726", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "time": {"start": 1755536328555, "stop": 1755536353272, "duration": 24717}}]}, "44c9275711c93730c8d2cb2a7374b3cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e6a76f74a137ce40", "status": "passed", "time": {"start": 1755543750696, "stop": 1755543773049, "duration": 22353}}]}, "772728b3468560788490a3673352724d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "74958e75bd6a6af", "status": "passed", "time": {"start": 1755536288386, "stop": 1755536314654, "duration": 26268}}]}, "92e2909ea81e82011e43342b4fc06c3b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70ef09b5d0578ff", "status": "passed", "time": {"start": 1755548798623, "stop": 1755548820833, "duration": 22210}}]}, "3d685d9ca6a0d7795be3c96921595318": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ae0d0bc69c535867", "status": "passed", "time": {"start": 1755544049042, "stop": 1755544070936, "duration": 21894}}]}, "4933b925ec694ecfcd17b3423ac28184": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "41a258194b47f5ba", "status": "passed", "time": {"start": 1755542330966, "stop": 1755542360458, "duration": 29492}}]}, "28f9087c186df37701fba71f366c084e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a7eef724e114d433", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm']\nassert False", "time": {"start": 1755541896311, "stop": 1755541918389, "duration": 22078}}]}, "bb51fd67dac102de95e755be72996bd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c93ac7f69b05022", "status": "passed", "time": {"start": 1755544085000, "stop": 1755544108381, "duration": 23381}}]}, "7d8296483e3dfc0902b42ccfe6759e59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df6140718ba46854", "status": "passed", "time": {"start": 1755549241914, "stop": 1755549271257, "duration": 29343}}]}, "9458f45d3c37d9141658da9964a470f5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "454a02f184d6e886", "status": "passed", "time": {"start": 1755548531993, "stop": 1755548554414, "duration": 22421}}]}, "51b4b46de04a8c1e37077a9f688cb490": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d907a6931b9bab45", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1755547463774, "stop": 1755547487298, "duration": 23524}}]}, "286e9cba8578d73b1f445f9d6d3a7d2e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "77992e69d66afef9", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A furry little monkey', '', '', '', \"That sounds like a cute image! I am unable to generate images directly. Since you're interested in image generation, would you like to explore some other visual styles or image ideas?\", 'Generated by AI, for reference only', \"Dialogue Explore Can you help with high school function problems? Educators Guide GenAI Ethics Help remove people from images A furry little monkey That sounds like a cute image! I am unable to generate images directly. Since you're interested in image generation, would you like to explore some other visual styles or image ideas? Generated by AI, for reference only Monkey image visual style exploration Monkey image creation software options Monkey image generation techniques DeepSeek-R1 Feel free to ask me any questions… 02:46\"]'\nassert False", "time": {"start": 1755542786715, "stop": 1755542811669, "duration": 24954}}]}, "2ead070abca949d328b727d85148a577": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "77d90cbd0aa54ede", "status": "passed", "time": {"start": 1755538374910, "stop": 1755538402909, "duration": 27999}}]}, "3e1e4da6344de7cdf40fa1d59c43dcc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7b92e467542433c3", "status": "passed", "time": {"start": 1755536141449, "stop": 1755536163238, "duration": 21789}}]}, "4fd50eb7a7e49fc09f612442a33e3010": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a36e4a54b6cc5d0", "status": "passed", "time": {"start": 1755543026002, "stop": 1755543057161, "duration": 31159}}]}, "59f89eb284ef9300c926c5e2f1d3fd26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b8620cc65f4a7b43", "status": "passed", "time": {"start": 1755539749356, "stop": 1755539772999, "duration": 23643}}]}, "02dda06829926b51f170419357629e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dcfc63b23aff804b", "status": "passed", "time": {"start": 1755545204884, "stop": 1755545227157, "duration": 22273}}]}, "4072ba85d37e03a8ef0a5dd9d0741631": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "451d5363ecf7e75f", "status": "passed", "time": {"start": 1755539119149, "stop": 1755539142045, "duration": 22896}}]}, "18415b75388fbfdac9a7e4232373c000": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e31835399ffcc5d7", "status": "passed", "time": {"start": 1755534467838, "stop": 1755534493385, "duration": 25547}}]}, "3bafa6d8eb5b49bc5b77f1784275285e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8290a3a5e5bcf3f4", "status": "passed", "time": {"start": 1755540132285, "stop": 1755540155496, "duration": 23211}}]}, "51c103053dd0c5596d9f4d9f178c3d8d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "30209a062596ef82", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance', '', '', '', \"That's a wonderfully detailed image! I can't generate images directly, but I know you're interested in image generation and design. Would you like to try creating that image with a jewelry ring, or maybe explore some visual styles like vector doodles or minimalist art?\", 'Generated by AI, for reference only', \"Dialogue Explore Can you help with high school function problems? A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance That's a wonderfully detailed image! I can't generate images directly, but I know you're interested in image generation and design. Would you like to try creating that image with a jewelry ring, or maybe explore some visual styles like vector doodles or minimalist art? Generated by AI, for reference only How to create the raccoon image now? Different art styles for the raccoon image Raccoon image generation tools and tips DeepSeek-R1 Feel free to ask me any questions… 02:47\"]'\nassert False", "time": {"start": 1755542825673, "stop": 1755542852185, "duration": 26512}}]}, "178c119ddfd51f19a22377df428e3fc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4e20f74cb4938ea2", "status": "passed", "time": {"start": 1755543156187, "stop": 1755543177815, "duration": 21628}}]}, "a0efcebc4cee6024e690bd290b4f3fbb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d356792bb10c9dcb", "status": "passed", "time": {"start": 1755550215198, "stop": 1755550237680, "duration": 22482}}]}, "12eb3852c333145c5906579f2346c37a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "febd4965bc5b5d32", "status": "passed", "time": {"start": 1755534418034, "stop": 1755534453910, "duration": 35876}}]}, "3d6cfc87445d1bd76dceee439d00b3d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf9f80b2bf9e2831", "status": "passed", "time": {"start": 1755534779500, "stop": 1755534801331, "duration": 21831}}]}, "75c9edd252211f9e74fd8c1a2faeefd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7bbe18dc462c8bb2", "status": "passed", "time": {"start": 1755539425460, "stop": 1755539452536, "duration": 27076}}]}, "1615e8617cafbed9e30baf38018d96b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c0bcef3f87616eb5", "status": "skipped", "statusDetails": "Skipped: reset phone 会导致设备断开，先跳过", "time": {"start": 1755548240955, "stop": 1755548240955, "duration": 0}}]}, "44e27936b56f219f63af671a8fd7f5fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c858841add323a3", "status": "passed", "time": {"start": 1755550923588, "stop": 1755550945795, "duration": 22207}}]}, "4ae696581fe41611547bc10ddba4f526": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c3fab8a57bf5eaa5", "status": "passed", "time": {"start": 1755534860314, "stop": 1755534885786, "duration": 25472}}]}, "20bd2e7c8179ca1901bc63c9a02b9ee1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9cecfbff8b58d513", "status": "passed", "time": {"start": 1755545319005, "stop": 1755545343640, "duration": 24635}}]}, "5c75e7ecaa2fe55a9b9666aae0ca1b5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "711abadef0237b83", "status": "passed", "time": {"start": 1755549991489, "stop": 1755550021349, "duration": 29860}}]}, "8bcc4c0c2b314e79a7177168f7d787b8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2488d85e6c4e33d8", "status": "passed", "time": {"start": 1755543426204, "stop": 1755543448737, "duration": 22533}}]}, "5697ea2b6f1cefef94d5b32e213e05a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a3fec9292f8b0113", "status": "passed", "time": {"start": 1755537647829, "stop": 1755537765913, "duration": 118084}}]}, "c121335a9bce64dd570aaf5b221b21df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "617d260b52b35e2d", "status": "passed", "time": {"start": 1755548212267, "stop": 1755548239506, "duration": 27239}}]}, "9445ebf64ec65c769332fec8dd505332": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1ee3a2d6d4b65ad", "status": "passed", "time": {"start": 1755549840641, "stop": 1755549867778, "duration": 27137}}]}, "56a09613cdb882018377e1c2c4e78472": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4d4eeb7e7cd4dd7", "status": "passed", "time": {"start": 1755536843226, "stop": 1755536868904, "duration": 25678}}]}, "bd4d204a449f3a4013b03af9a9101446": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80c8b496993f5e37", "status": "passed", "time": {"start": 1755534629582, "stop": 1755534658909, "duration": 29327}}]}, "a306f4c2244f596f1ab838aa7a80dd45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bd35946baa6a9c7c", "status": "passed", "time": {"start": 1755542711300, "stop": 1755542736578, "duration": 25278}}]}, "e8f03971277a71512b5ebaad612bc964": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "467ff75891f341fb", "status": "passed", "time": {"start": 1755542199306, "stop": 1755542229004, "duration": 29698}}]}, "75c56947a7b1061f7ec858fb20919b50": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "32df01cfab8864af", "status": "passed", "time": {"start": 1755542285440, "stop": 1755542316964, "duration": 31524}}]}, "c45251d74b80bdc9105dde9b444ef1c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ecc71f2f493e1262", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755543571254, "stop": 1755543593013, "duration": 21759}}]}, "88a6294df5f2ab484e181b1f196ff253": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "715c7de84e96b864", "status": "passed", "time": {"start": 1755546614903, "stop": 1755546643114, "duration": 28211}}]}, "a4af452e0448ec3c1ecc9afcc30459be": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "523026d721903565", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755540169131, "stop": 1755540191004, "duration": 21873}}]}, "0eb27e9cfa9ed24b7ee5e6be6e495cb7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a00ea4f467dbafd9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['OK']\nassert False", "time": {"start": 1755533504668, "stop": 1755533526338, "duration": 21670}}]}, "5f09c86ae320138eb73de73a25f73607": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dfd3fac825d35b2e", "status": "passed", "time": {"start": 1755545725296, "stop": 1755545747340, "duration": 22044}}]}, "61e923bb9b35a687b231b0c27b5ec620": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "16fa33ac9f4fc28a", "status": "passed", "time": {"start": 1755549693192, "stop": 1755549715674, "duration": 22482}}]}, "9c301cfc137fb94f119957b5f74291ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1aefe55150cc415", "status": "passed", "time": {"start": 1755550878115, "stop": 1755550909670, "duration": 31555}}]}, "caccad19499f4bc8494953ac84d8d23c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "13f549d9d5b7167a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry', 'a bit difficult']\nassert False", "time": {"start": 1755537094408, "stop": 1755537211531, "duration": 117123}}]}, "929b39cceeb7a01f602573951f5fef39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3d6c60bb022e56e", "status": "passed", "time": {"start": 1755551076900, "stop": 1755551098907, "duration": 22007}}]}, "e865942f74e70950eccebd8243dd6035": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b3e6384cc783150", "status": "passed", "time": {"start": 1755536405171, "stop": 1755536435715, "duration": 30544}}]}, "776dca6a65836abb5a943543ee2b9f12": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e34af531cc9853a3", "status": "passed", "time": {"start": 1755551258713, "stop": 1755551281024, "duration": 22311}}]}, "e78aa9affdc78502b8ad3b712ecf28b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aac5c19fbbd75fa1", "status": "passed", "time": {"start": 1755538613838, "stop": 1755538636244, "duration": 22406}}]}, "ffb0a39af30beaa699329479ec564117": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ee1adc8b75cf0025", "status": "passed", "time": {"start": 1755533197292, "stop": 1755533240096, "duration": 42804}}]}, "356db57bafcf61266d2f62fd1b8ab4e2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "70a5f4ae04e6558e", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "time": {"start": 1755542750283, "stop": 1755542772092, "duration": 21809}}]}, "9c84b087eb7d9fde94ed5bb5370b275b": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f252f9db319d4be8", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1755547073652, "stop": 1755547105068, "duration": 31416}}]}, "0c44c94f08feed70addcec44e96bda5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "29b0fa4f7a3cc5a", "status": "passed", "time": {"start": 1755536527132, "stop": 1755536549205, "duration": 22073}}]}, "ffd7dc86cbeda13ca78bbca09f06422a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4bce7b61e04ef6f8", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['countdown 5 min', 'Done!', '', '', '', '']'\nassert False", "time": {"start": 1755538254786, "stop": 1755538285262, "duration": 30476}}]}, "613ef0933e4be87696bbedd56b4f0052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6325a8bfe82ee58", "status": "passed", "time": {"start": 1755543228368, "stop": 1755543250570, "duration": 22202}}]}, "17788b061637289a04732fe5840218ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f1d7293de10d9144", "status": "passed", "time": {"start": 1755533540018, "stop": 1755533565891, "duration": 25873}}]}, "1ca8d9c300d55584cdcf637ede08bdba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f47927df2a6b5ce3", "status": "passed", "time": {"start": 1755543931278, "stop": 1755543962526, "duration": 31248}}]}, "1d15cba90ae0426fa12e3218f1c542a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ff4baae6c2c6c2f2", "status": "passed", "time": {"start": 1755532669374, "stop": 1755532691320, "duration": 21946}}]}, "0b659537bc9c9b47c2c23f702fadd56b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d93759f5df6d71b2", "status": "passed", "time": {"start": 1755544122605, "stop": 1755544144741, "duration": 22136}}]}, "eadc304b3069d4918c06805d847a62d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6fceb27a8985fb17", "status": "passed", "time": {"start": 1755545689268, "stop": 1755545711354, "duration": 22086}}]}, "1d703b69183fa98eda60c159840c4ffd": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8be6bece5fd1af0d", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=False, 响应='['call mom', 'Found the following contact. Would you like to call?', '', '', '', '']'\nassert False", "time": {"start": 1755542980983, "stop": 1755543012019, "duration": 31036}}]}, "b7e448432379b6f8a430f1cbdb3ee3fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f82ce057e728b5af", "status": "passed", "time": {"start": 1755535842792, "stop": 1755535868100, "duration": 25308}}]}, "0c4bd81bf0dbac094265e3ac47550bbd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "25841d99901eab3", "status": "passed", "time": {"start": 1755536487501, "stop": 1755536513261, "duration": 25760}}]}, "e8d2430f1712f99f0a178507bb398709": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9bc8f2567daac5d0", "status": "skipped", "statusDetails": "Skipped: 重启会导致设备断开，先跳过", "time": {"start": 1755548240965, "stop": 1755548240965, "duration": 0}}]}, "b4e75f584d82368436f820de28f92cfd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68c40bdfdb691181", "status": "passed", "time": {"start": 1755533254235, "stop": 1755533293660, "duration": 39425}}]}, "753ba105235625e906d023bd3aaa0821": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "faf45958c476966a", "status": "passed", "time": {"start": 1755540479232, "stop": 1755540501365, "duration": 22133}}]}, "b846e0492742bba04cf4a26ee4530889": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "110bca580595cc26", "status": "skipped", "statusDetails": "Skipped: redial命令需要先拨打电话，才能重拨，无法通用化", "time": {"start": 1755548126150, "stop": 1755548126150, "duration": 0}}]}, "ab2195315637668cad08b0606ef7ff17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "97dab4c826b8251a", "status": "passed", "time": {"start": 1755546389967, "stop": 1755546411968, "duration": 22001}}]}, "794f685415bbcd702feae0b55a4dd537": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ddd871a1ce7ff648", "status": "passed", "time": {"start": 1755536033339, "stop": 1755536055063, "duration": 21724}}]}, "48a2a80bfed06f0c82b99a0aaa26e252": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "644d8cb140f62e0a", "status": "passed", "time": {"start": 1755546542657, "stop": 1755546564878, "duration": 22221}}]}, "7b1fce8b7d3ff59dca969b439bb82f75": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2016852632d1a42f", "status": "broken", "statusDetails": "TypeError: a bytes-like object is required, not 'dict'", "time": {"start": 1755533915043, "stop": 1755533940339, "duration": 25296}}]}, "f09a8375806e200073a99f1cbabdc35c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "35deb8fb0261ed29", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Ok']\nassert False", "time": {"start": 1755533126191, "stop": 1755533147873, "duration": 21682}}]}, "1ab60ce22774c460e557aaa3b3f9120a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d5cccf79669ff0c", "status": "passed", "time": {"start": 1755539973003, "stop": 1755539997769, "duration": 24766}}]}, "aecf9a6f67cd29766190cbcc133448d2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9f87c3103add02e5", "status": "passed", "time": {"start": 1755548688485, "stop": 1755548710878, "duration": 22393}}]}, "ffd9b4e8a27f1469e05050bd5989e500": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "576291327acaf0e2", "status": "passed", "time": {"start": 1755540443086, "stop": 1755540465098, "duration": 22012}}]}, "9420fd606a04614c6f09bf36f2873f93": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1c736fd2e0572192", "status": "passed", "time": {"start": 1755540241758, "stop": 1755540263880, "duration": 22122}}]}, "1147a84f37b71eb5b15008169cadcc53": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a625d34db1d2363e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms']\nassert False", "time": {"start": 1755535919927, "stop": 1755535943416, "duration": 23489}}]}, "e9039096aff36ba6e4fae58e40eb8539": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "afa51a402f294990", "status": "passed", "time": {"start": 1755547751251, "stop": 1755547776030, "duration": 24779}}]}, "57c5370b1a13de534ed16f0ce2ee85b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e21f057e4d8226ef", "status": "passed", "time": {"start": 1755540780248, "stop": 1755540803102, "duration": 22854}}]}, "c052c8813edd9c2261dc1bcc29786fe9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a8d288b13eea8d3e", "status": "passed", "time": {"start": 1755550035242, "stop": 1755550057418, "duration": 22176}}]}, "e56b7788214bc4e18231f16dfd713954": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96b1f45ef3f7523", "status": "passed", "time": {"start": 1755533727782, "stop": 1755533749866, "duration": 22084}}]}, "bfddb3863bb9971cace5dae92df6977d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2cf73ad699817a37", "status": "passed", "time": {"start": 1755545875017, "stop": 1755545900251, "duration": 25234}}]}, "154a720f41d8f5a908552e8c7cf8e781": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0bc2546728b57be", "status": "passed", "time": {"start": 1755548725100, "stop": 1755548747846, "duration": 22746}}]}, "afa6af304cfb25a990764680de5fa777": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "409d638ac1bedc2", "status": "passed", "time": {"start": 1755544772103, "stop": 1755544794095, "duration": 21992}}]}, "fcabf101e08450542157f8740eeec9a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e458485db602770", "status": "passed", "time": {"start": 1755542374615, "stop": 1755542398281, "duration": 23666}}]}, "d094a0b21c0bd532e6db707dcbab5564": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "15cdef69b8beed3e", "status": "passed", "time": {"start": 1755544194307, "stop": 1755544216422, "duration": 22115}}]}, "f416bca94fc67372d77ac2dd1f3e4517": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4947f2cef0c48e09", "status": "passed", "time": {"start": 1755535686786, "stop": 1755535712138, "duration": 25352}}]}, "6ecc7e0fc961d0d4e7e46672c033625a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b81fc5f7f6d96890", "status": "passed", "time": {"start": 1755534741316, "stop": 1755534765733, "duration": 24417}}]}, "4276e587385154206726240ad06acd24": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f005f18c11305148", "status": "passed", "time": {"start": 1755546461408, "stop": 1755546483763, "duration": 22355}}]}, "5050d8dc816d181ca0c76dc56c8cb5f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ff5d7bb4f38f240e", "status": "passed", "time": {"start": 1755546159919, "stop": 1755546182369, "duration": 22450}}]}, "8acd3c85f9c9d0b7f252da4466c049e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d25630d6258acf1e", "status": "passed", "time": {"start": 1755539342207, "stop": 1755539370883, "duration": 28676}}]}, "00ae864f57f2146374ff8bf301b9b8af": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "397b1501c16d5e75", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.', '', '', '', \"That's a fantastic description for an image! Since you're interested in image generation, would you like to explore some other visual styles or image ideas?\", 'Generated by AI, for reference only', \"Dialogue Explore hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively. That's a fantastic description for an image! Since you're interested in image generation, would you like to explore some other visual styles or image ideas? Generated by AI, for reference only Hamster mascot design for brand identity Hamster mascot illustration software options Different hamster mascot poses and styles DeepSeek-R1 Feel free to ask me any questions… 03:28\"]'\nassert False", "time": {"start": 1755545279615, "stop": 1755545304641, "duration": 25026}}]}, "a297156fb1e7af53c032ac3c6277feee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "480a0fe4bb19fcbc", "status": "passed", "time": {"start": 1755550960004, "stop": 1755550990198, "duration": 30194}}]}, "b59687eed5ccb758650c7c0d96ed6bc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8b77159b32025374", "status": "skipped", "statusDetails": "Skipped: start walking命令都会开Health应用，才能执行，无法通用化", "time": {"start": 1755550022696, "stop": 1755550022696, "duration": 0}}]}, "7a670647c2336e6a5a5d07824fe89da6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b707a563124442", "status": "passed", "time": {"start": 1755544808173, "stop": 1755544830192, "duration": 22019}}]}, "ce2018f4cca8041a465d2a753ade920b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "56faba69a46b6a0", "status": "passed", "time": {"start": 1755537893929, "stop": 1755537916363, "duration": 22434}}]}, "098126ed77f375b3e0f5370b3ec7d0b7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3a4693475a14571f", "status": "passed", "time": {"start": 1755533764116, "stop": 1755533786554, "duration": 22438}}]}, "76a63bd8a63882a3a1ada32e63f1c971": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5dee92077c27d125", "status": "passed", "time": {"start": 1755546278113, "stop": 1755546303687, "duration": 25574}}]}, "a0ea006ce61aacded2720f8d2a03ba5b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "66e2b58373c1c525", "status": "passed", "time": {"start": 1755534938772, "stop": 1755534961004, "duration": 22232}}]}, "8971377f4371d1ea3384cde4ed276db1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0217fa5fc8a4a81", "status": "passed", "time": {"start": 1755541260399, "stop": 1755541282499, "duration": 22100}}]}, "6748f677dff755a4da95c520c3f05506": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "939cebe22906f8ea", "status": "passed", "time": {"start": 1755534037938, "stop": 1755534060173, "duration": 22235}}]}, "0ce2a3efa79db58c34a5590015948f51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "204d2c40a3295f49", "status": "passed", "time": {"start": 1755545357716, "stop": 1755545383139, "duration": 25423}}]}, "e76af38ac3a594aa2b7d7173d57e98ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1839257fa3f6667", "status": "passed", "time": {"start": 1755549582878, "stop": 1755549605151, "duration": 22273}}]}, "7cd08c87d5de8ec73ac863e8a636c8aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4997c4a01fffa2e1", "status": "passed", "time": {"start": 1755539155868, "stop": 1755539178585, "duration": 22717}}]}, "11875095b9997cfc7edbe407c3074b7e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "18dcfed9cffe3fc1", "status": "passed", "time": {"start": 1755543498357, "stop": 1755543520294, "duration": 21937}}]}, "d6ad2be1232377f5942b2d4f816b2e71": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "eb294921beeb20c3", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['turn on nfc', 'NFC is turned on now.', 'NFC', '', '', '']'\nassert False", "time": {"start": 1755541443841, "stop": 1755541468196, "duration": 24355}}]}, "063471c2e7f2d00ecd08e780860e0cf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e25e8aa73ba38521", "status": "passed", "time": {"start": 1755540587325, "stop": 1755540611357, "duration": 24032}}]}, "464c8ea2a15f7ff86b8e0a347a821945": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f8f62e26362f4acd", "status": "passed", "time": {"start": 1755533089991, "stop": 1755533112024, "duration": 22033}}]}, "94acf463e3e6d87a1e9cf7ff754044a2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8dd63573fd58dbfa", "status": "passed", "time": {"start": 1755547119803, "stop": 1755547144285, "duration": 24482}}]}, "d304f4e805a15a0351109cc931c26ffc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d454847eee1af6e6", "status": "passed", "time": {"start": 1755535613034, "stop": 1755535634719, "duration": 21685}}]}, "f4d12b1367b35df96178a58e48fe8f5e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "744cbb103ecdfa25", "status": "passed", "time": {"start": 1755532595464, "stop": 1755532622408, "duration": 26944}}]}, "dee08db8cb0f1293bf864f56326992d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "36bd15928d9b0905", "status": "passed", "time": {"start": 1755534301066, "stop": 1755534326958, "duration": 25892}}]}, "34f3c9cc9098f792051e7099b7a9fdc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ff50a601a62b5563", "status": "passed", "time": {"start": 1755550179343, "stop": 1755550201447, "duration": 22104}}]}, "578e52c6d5e868d5464682b454971c51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ed39439d861cebf", "status": "passed", "time": {"start": 1755541978279, "stop": 1755542002141, "duration": 23862}}]}, "306cbf11cdbcb045eb3c3c716515b1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a5616c43d872369b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Generated by AI, for reference only']\nassert False", "time": {"start": 1755545835689, "stop": 1755545861121, "duration": 25432}}]}, "ed94d8d97b63a623a1f6438b57774ff1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8daac15c9f9ea1a8", "status": "passed", "time": {"start": 1755547651841, "stop": 1755547680054, "duration": 28213}}]}, "c5050ea089fe0f7a5b962119cd32b32e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e09c78c059d573c", "status": "passed", "time": {"start": 1755533879354, "stop": 1755533901182, "duration": 21828}}]}, "f5346ff0fa4cb76e4b6ceea6116693ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c1f1346361a3976a", "status": "passed", "time": {"start": 1755535199034, "stop": 1755535220932, "duration": 21898}}]}, "8f69a86b2d665eb6925fa007d973040e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ea916f8d49f87076", "status": "passed", "time": {"start": 1755538763210, "stop": 1755538785995, "duration": 22785}}]}, "5fe7611f5b7d3ef438ce938b66e0b99f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "86a88237508b1115", "status": "passed", "time": {"start": 1755539931947, "stop": 1755539958940, "duration": 26993}}]}, "5ad5ef8bf0f7913e709dbc1e706db1e5": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c554cc0509ed2c17", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['joke for you']\nassert False", "time": {"start": 1755550364424, "stop": 1755550389686, "duration": 25262}}]}, "79ed3b173757e627534e24ad9289f338": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d69e193e35f210e0", "status": "passed", "time": {"start": 1755551222229, "stop": 1755551244233, "duration": 22004}}]}, "8d12bedb52d3f000f4269afc25f3fe30": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c02619d66e652e22", "status": "passed", "time": {"start": 1755536649309, "stop": 1755536678621, "duration": 29312}}]}, "2bf170e8c0013ab361afb23f8f059db8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "361a48d619f8b474", "status": "passed", "time": {"start": 1755544571427, "stop": 1755544602971, "duration": 31544}}]}, "b911308f3c1fe764715d778a884946c2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "979aaae63733c3e6", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755551295036, "stop": 1755551317273, "duration": 22237}}]}, "2ecac23eb3f511651fafc6ba6a3725f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d83d0ba4b2e5fcc3", "status": "passed", "time": {"start": 1755540738840, "stop": 1755540766433, "duration": 27593}}]}, "29293108cad153db021139a26e3455ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1701645e26679093", "status": "passed", "time": {"start": 1755550328224, "stop": 1755550350425, "duration": 22201}}]}, "5cc849d46714fff99c626e94dc28932d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b9851214b3a35736", "status": "passed", "time": {"start": 1755542559339, "stop": 1755542581522, "duration": 22183}}]}, "d9e62135fb3d98a8cadd206c651db3d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "feb779f3ce9b15a7", "status": "passed", "time": {"start": 1755539710917, "stop": 1755539734868, "duration": 23951}}]}, "a1088ee9683cc60d86b0994865138921": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "825ccf498017b3be", "status": "passed", "time": {"start": 1755535766243, "stop": 1755535793130, "duration": 26887}}]}, "a54454fcb441a45b0e29dcbbf21679aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "93055a6f3a5e2208", "status": "passed", "time": {"start": 1755538873347, "stop": 1755538896291, "duration": 22944}}]}, "9ba28642fd60826e21a949609570a951": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a978d43a69fdb4a4", "status": "passed", "time": {"start": 1755548418679, "stop": 1755548444543, "duration": 25864}}]}, "5dbd6c476e40c9de0215f0509dd43986": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3ff29417a9ae6010", "status": "passed", "time": {"start": 1755546120102, "stop": 1755546145733, "duration": 25631}}]}, "aff947fee562ec2636c3ce68a270b88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eeaa4130f4971e69", "status": "passed", "time": {"start": 1755541224153, "stop": 1755541246179, "duration": 22026}}]}, "fef04fbdd26caf7d3f0f60df2c3ed14d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d98e6b072f4e3cec", "status": "passed", "time": {"start": 1755545436695, "stop": 1755545458640, "duration": 21945}}]}, "411d5cfcc1960041b8df4decf67232f6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8a10778742f62b7", "status": "passed", "time": {"start": 1755545617350, "stop": 1755545639385, "duration": 22035}}]}, "7ebb09688c661659cc2b4a26d54a347f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "12ed095629d33d34", "status": "passed", "time": {"start": 1755551148997, "stop": 1755551171662, "duration": 22665}}]}, "b60d4c80cd1df80873da3fea78736e6a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ceea2a980c7d0d9c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Today: the high is forecast']\nassert False", "time": {"start": 1755536563259, "stop": 1755536592217, "duration": 28958}}]}, "969451307307a13b4d89a24bb46ad0bb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6463f34280457263", "status": "passed", "time": {"start": 1755548872001, "stop": 1755548894307, "duration": 22306}}]}, "db1dca9aabe4b3e03e7003d17cf3fc9c": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "155df7699608c58e", "status": "skipped", "statusDetails": "Skipped: airplane mode 会导致设备断开网络，先跳过", "time": {"start": 1755541174560, "stop": 1755541174560, "duration": 0}}]}, "74da34fd3c558df1234d7c0e937641b3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70ea41509f71ae68", "status": "passed", "time": {"start": 1755550440995, "stop": 1755550466694, "duration": 25699}}]}, "e1f3b35ff3714fe0225699eb84cc1b87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "20d5bddf727c0fba", "status": "passed", "time": {"start": 1755544267541, "stop": 1755544294143, "duration": 26602}}]}, "eda6bef994b1b0ef78f60f433fb1d4f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f7c114017e246923", "status": "passed", "time": {"start": 1755535726211, "stop": 1755535752166, "duration": 25955}}]}, "8f73bb4e2ab622960daf9c39a4008510": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bf798a6e3d70daa5", "status": "passed", "time": {"start": 1755546238351, "stop": 1755546264045, "duration": 25694}}]}, "891e31ec8bf99ceed4f462ce0c8629db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2c81a4a04cdc1071", "status": "passed", "time": {"start": 1755543859540, "stop": 1755543881464, "duration": 21924}}]}, "084048a337d3081654dc4414f67fce70": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c98d6e623d24e658", "status": "passed", "time": {"start": 1755549205169, "stop": 1755549227729, "duration": 22560}}]}, "0a76822399c9a8e342924e5ae6cce12c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bde5993ab80214c5", "status": "passed", "time": {"start": 1755542631858, "stop": 1755542657387, "duration": 25529}}]}, "e82a80866bdbe9a7e1ac367f20c977b5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1209d5cb1fb232f6", "status": "passed", "time": {"start": 1755550748640, "stop": 1755550771005, "duration": 22365}}]}, "b165a17be8ab35920a6af9be7611a2c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "98d99df8f07af07c", "status": "passed", "time": {"start": 1755533800620, "stop": 1755533822664, "duration": 22044}}]}, "f46a1c12d07d5949dbcf4b31314824ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f4f27779b7087710", "status": "passed", "time": {"start": 1755539466302, "stop": 1755539496281, "duration": 29979}}]}, "31c983fdcc8b62f5927f99c0482b828d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0e19a1d22249e58", "status": "passed", "time": {"start": 1755548139197, "stop": 1755548161261, "duration": 22064}}]}, "e3cc499fef74e68f1c802e097ccd0f42": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef7a15af380820c6", "status": "passed", "time": {"start": 1755544348925, "stop": 1755544376897, "duration": 27972}}]}, "cf0ebfd1b4e2ab43e2f516ad6a1a6917": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f78c40977d29fd5", "status": "passed", "time": {"start": 1755536922602, "stop": 1755536953417, "duration": 30815}}]}, "89b134ac1374e88187e793daf9f8fcab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7bcfa670f3717f3c", "status": "passed", "time": {"start": 1755549132314, "stop": 1755549154625, "duration": 22311}}]}, "c2ecb960f7f893feeaa2f24a34c9d77e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8f694e90876ac004", "status": "passed", "time": {"start": 1755544308198, "stop": 1755544335028, "duration": 26830}}]}, "5cf50058091f34fd1ed89d0b8f717355": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bcecb343f6bed949", "status": "passed", "time": {"start": 1755541594412, "stop": 1755541621649, "duration": 27237}}]}, "8c62567d8b8a27f77124afc90fa44336": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "79a84d7137b558e3", "status": "passed", "time": {"start": 1755532516620, "stop": 1755532538737, "duration": 22117}}]}, "de5ff490f92fc399976d91fe0edc371e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4bb0f3490eb7fd10", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755542448115, "stop": 1755542469919, "duration": 21804}}]}, "cce948f7c988b6f22bd4e8d08ca74deb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "565d3a68eadd14f6", "status": "passed", "time": {"start": 1755550107536, "stop": 1755550129785, "duration": 22249}}]}, "1a94a631f074dc4c0ba2bd39c9518123": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b255e6f959e5a001", "status": "passed", "time": {"start": 1755537930409, "stop": 1755537952531, "duration": 22122}}]}, "e60dab4e55edacbecf632d4d22f368e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7fb3652513992510", "status": "passed", "time": {"start": 1755548568717, "stop": 1755548591450, "duration": 22733}}]}, "728822fc623e888cd9efa450f4737787": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f87fb922cd22cb5e", "status": "passed", "time": {"start": 1755542671215, "stop": 1755542697241, "duration": 26026}}]}, "04263bf3ad5402ebd901c9c0e6682325": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "efbf1ae95846c87f", "status": "passed", "time": {"start": 1755544844031, "stop": 1755544866050, "duration": 22019}}]}, "a34b87ce6db1ff744d0ab6c172eb93da": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ec3f00031eaf4371", "status": "skipped", "statusDetails": "Skipped: 重启会导致设备断开，先跳过", "time": {"start": 1755548240961, "stop": 1755548240961, "duration": 0}}]}, "7413abdce214459d0e44671ef65b660b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df184aa14f08e16a", "status": "passed", "time": {"start": 1755538080665, "stop": 1755538130030, "duration": 49365}}]}, "6af3d12b439ba44d7cce5c3d3ba19e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "474f128d73576157", "status": "skipped", "statusDetails": "Skipped: reboot 会导致设备断开，先跳过", "time": {"start": 1755548126145, "stop": 1755548126145, "duration": 0}}]}, "1498285b0d63f23df3a22c9c5262f7f3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "93132b8d78ed6db", "status": "passed", "time": {"start": 1755537516454, "stop": 1755537633655, "duration": 117201}}]}, "bfd4a9e37b70dca0b14b0ccf5246fc4a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1e00a46747da8d89", "status": "passed", "time": {"start": 1755545914505, "stop": 1755545936269, "duration": 21764}}]}, "f7282303534c1c8599c3343608e6f453": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "294d0761bd0b35ea", "status": "passed", "time": {"start": 1755534550532, "stop": 1755534572689, "duration": 22157}}]}, "80dab16fde357aadc4387b5d440ed276": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bc58b8085ad08e08", "status": "passed", "time": {"start": 1755548458658, "stop": 1755548481293, "duration": 22635}}]}, "7d3b4e67344145885187c529ee88a9aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f8218991466f15e9", "status": "passed", "time": {"start": 1755549168756, "stop": 1755549191000, "duration": 22244}}]}, "ea48444c2b0789e59a64850ccfab3722": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7823681a04e66dc9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The following event has been added for you.']\nassert False", "time": {"start": 1755532475122, "stop": 1755532503008, "duration": 27886}}]}, "cd2650c8b690a339f6c3696b18b9dc0d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cfc600f66f56271b", "status": "passed", "time": {"start": 1755539787019, "stop": 1755539810024, "duration": 23005}}]}, "ca9dd7f70b2888aafceb94247d7986f0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c1d99d953a261c6", "status": "passed", "time": {"start": 1755549398624, "stop": 1755549421021, "duration": 22397}}]}, "7fb2c589f1fda51205a5af1b549d5045": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "66074d91611f0379", "status": "passed", "time": {"start": 1755538042556, "stop": 1755538067497, "duration": 24941}}]}, "54b47105d42d2a9f18eec071fba40c73": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e738d908d4f56738", "status": "passed", "time": {"start": 1755532307829, "stop": 1755532344211, "duration": 36382}}]}, "19fa56a92b4343c1894780564290d112": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1a5d810bd216a785", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call']\nassert False", "time": {"start": 1755546074978, "stop": 1755546105867, "duration": 30889}}]}, "56528a816381bba6ed1ca007f557362f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d5ebc6c67f353f03", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755544880054, "stop": 1755544901864, "duration": 21810}}]}, "f05a6eb960fbbc415e4c605538080373": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5a07f04e872ede5e", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1755535806907, "stop": 1755535828451, "duration": 21544}}]}, "5d0a6cda1787168fa2fdaaae1dee86f3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "14dee738f79101ae", "status": "passed", "time": {"start": 1755545061038, "stop": 1755545082942, "duration": 21904}}]}, "0f4d3881c287fa46a9fdaf099fc19f8d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ef8f8d42edcb4f66", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Auto-rotation is turned on now']\nassert False", "time": {"start": 1755541186998, "stop": 1755541209769, "duration": 22771}}]}, "2b2dcc407b5c428f968f62d94fe8025c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9671c04e652145d5", "status": "passed", "time": {"start": 1755548835228, "stop": 1755548857548, "duration": 22320}}]}, "7c32e753573a480d7d5c09abab43469e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1486d4607be0b253", "status": "passed", "time": {"start": 1755532793075, "stop": 1755532830457, "duration": 37382}}]}, "ae0ee984c3712fd05ea04b52289e14fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f3e2e05aaeb14ddc", "status": "passed", "time": {"start": 1755532704470, "stop": 1755532734454, "duration": 29984}}]}, "918c52f1eb9803594ff76c724b43d5f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "354bfa2b8e5a4d90", "status": "passed", "time": {"start": 1755542100645, "stop": 1755542138165, "duration": 37520}}]}, "a3c84dd7a2924ee198fdb33cbc4e20b6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6966801bd55088f5", "status": "passed", "time": {"start": 1755543309236, "stop": 1755543340429, "duration": 31193}}]}, "cc44ad4097a589726631a345e0cd01ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d993e90b89a322eb", "status": "passed", "time": {"start": 1755534672894, "stop": 1755534693038, "duration": 20144}}]}, "c3dde525f6a284fe4a3e4b670182329f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "32a4642822207cf0", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.']，实际响应: '['merry christmas', 'Merry Christmas to you🎄', '', '', '', '']'\nassert False", "time": {"start": 1755546864527, "stop": 1755546889045, "duration": 24518}}]}, "e32881dd9d54414fa74d523ef27b055c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "baa21503fd9a96ca", "status": "passed", "time": {"start": 1755541296337, "stop": 1755541319475, "duration": 23138}}]}, "488c24d02f5d5348f35881280e505f32": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c4a9a1ad677f0098", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to any address at the moment. Is there anything else I can help you with?', 'Generated by AI, for reference only', '03:57 Dialogue Explore Do traditional dance steps contain numerical sequences? Can you help with high school function problems? Send my recent photo to mom on WhatsApp navigation to the address in thie image I am sorry, I am unable to navigate to any address at the moment. Is there anything else I can help you with? Generated by AI, for reference only Troubleshooting navigation system errors How to save the address for later use Alternative navigation apps and features DeepSeek-R1 Feel free to ask me any questions…']'\nassert False", "time": {"start": 1755547027098, "stop": 1755547058930, "duration": 31832}}]}, "ab0169efb30d26689cbce230ff455598": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b0188f89da3d109f", "status": "passed", "time": {"start": 1755547845246, "stop": 1755547885666, "duration": 40420}}]}, "cf1bdc2b1d9b604939681e5b6ac6f506": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bd361d609157db", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on smart reminder', 'Smart reminder is turned on now.', 'Smart reminder', '', '', '']'\nassert None", "time": {"start": 1755541482387, "stop": 1755541505404, "duration": 23017}}]}, "963c2cd1bdb409e4cfe9589a18006e88": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96d8d1a506ac9b9e", "status": "passed", "time": {"start": 1755539009642, "stop": 1755539032100, "duration": 22458}}]}, "a5a3cc08eb97e600c97acb65a7439ec0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "29f8078765071560", "status": "passed", "time": {"start": 1755549656313, "stop": 1755549678793, "duration": 22480}}]}, "bd9c64dabd06671b98d60748492be267": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c7834e9682ae356d", "status": "passed", "time": {"start": 1755533954607, "stop": 1755533985423, "duration": 30816}}]}, "c2a64f07232d43585d1dfee25c2f9407": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "44ed27c7b7731a47", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Chong Qing Shi is Fair today. The high is forecast as 37°C and the low as 28°C.']\nassert False", "time": {"start": 1755536692634, "stop": 1755536714426, "duration": 21792}}]}, "a84e06839a37b7806fefd316aa632437": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7a71af2e1e53eaf4", "status": "passed", "time": {"start": 1755545241022, "stop": 1755545265610, "duration": 24588}}]}, "8b6d374ba70006ef7591c8e0bf72bb00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8bb36711edb0df7f", "status": "passed", "time": {"start": 1755550143772, "stop": 1755550165334, "duration": 21562}}]}, "0f3523ec9dc3ea86ebaca76b6956f01c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "23689521abf187ca", "status": "passed", "time": {"start": 1755538002305, "stop": 1755538029474, "duration": 27169}}]}, "489e5c631d3a3ce20f77bce4c7c6632a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "328cb95c896f8b39", "status": "failed", "statusDetails": "AssertionError: clock: 初始=None, 最终=None, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '', '', '']'\nassert None == 7", "time": {"start": 1755539635251, "stop": 1755539658539, "duration": 23288}}]}, "329fa4b06eb0b0d769c2c418ed03dab7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "687c67ac43338e6c", "status": "passed", "time": {"start": 1755533651835, "stop": 1755533675491, "duration": 23656}}]}, "66496441d7401e453a580b4a8c23d111": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1092ee4849172c85", "status": "passed", "time": {"start": 1755539265631, "stop": 1755539290669, "duration": 25038}}]}, "169e5b613c0fec2cebd053175998bf17": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8ac2fbb40ff2fe45", "status": "failed", "statusDetails": "AssertionError: clock: 初始=None, 最终=None, 响应='['open clock', 'Done!', '', '', '', '', '[com.transsion.deskclock页面内容] 闹钟 | 06:00 | 周一至周五 | 07:00 | 仅一次. 7 小时1 分钟后响铃 | 07:00 | 周日, 周六. 4 天后响铃 | 09:00 | 周日, 周六. 4 天后响铃 | 10:00 | 仅一次. 10 小时1 分钟后响铃 | 闹钟 | 世界时钟 | 定时器 | 秒表']'\nassert None", "time": {"start": 1755532748815, "stop": 1755532778833, "duration": 30018}}]}, "a5e15bb05795c5949d67f33200f4d69b": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ce531324130742d4", "status": "failed", "statusDetails": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '']'\nassert 0 == 15", "time": {"start": 1755538836770, "stop": 1755538858989, "duration": 22219}}]}, "37d8f85ba7c46a46b390c4fc5ab20de7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d7d35dfd5353048", "status": "passed", "time": {"start": 1755532844673, "stop": 1755532867161, "duration": 22488}}]}, "44b646d68146a0c48da2623a58b17f6f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd8189ee216b6c12", "status": "passed", "time": {"start": 1755533579539, "stop": 1755533601527, "duration": 21988}}]}, "4f538fc772535a0c0811ad87d3aa9494": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3fde257da3ffa32", "status": "passed", "time": {"start": 1755549361790, "stop": 1755549384169, "duration": 22379}}]}, "b12ee4e4a5d7e51ba0abe166b6c90352": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e61d8722a56d0e37", "status": "passed", "time": {"start": 1755547790139, "stop": 1755547831409, "duration": 41270}}]}, "309f3fbb8586cc15e324e94cec37e7ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce2c76aab15be26", "status": "passed", "time": {"start": 1755534149151, "stop": 1755534172506, "duration": 23355}}]}, "8814f1dafa698e785ee1f58faa6e745d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c7c9715779136ab0", "status": "passed", "time": {"start": 1755548295766, "stop": 1755548321953, "duration": 26187}}]}, "d9f01ef1af79559082ce9e9b2e40295f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b7f17392cbd20aee", "status": "passed", "time": {"start": 1755532260908, "stop": 1755532293374, "duration": 32466}}]}, "08bb1ce458e948b9a8084fb3ec1f2c83": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a764398584dbd337", "status": "passed", "time": {"start": 1755545761411, "stop": 1755545783561, "duration": 22150}}]}, "0e1d64e0daaaf11983cdc488ea4b0993": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ecbca05a49966cd4", "status": "skipped", "statusDetails": "Skipped: power off 会导致设备断开，先跳过", "time": {"start": 1755548048698, "stop": 1755548048698, "duration": 0}}]}, "984a0fd313bba0ca2f20f0bbff732eb8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "915a979c1ab3f826", "status": "passed", "time": {"start": 1755550599676, "stop": 1755550621782, "duration": 22106}}]}, "e5dc64184617a9497ec52a3b74103b55": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "98a2bd79089efe14", "status": "passed", "time": {"start": 1755538539706, "stop": 1755538563343, "duration": 23637}}]}, "981b71ad744b9a603c31ab4832ff9439": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b58347ad96176240", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['document provides a summary of the application']\nassert False", "time": {"start": 1755537779885, "stop": 1755537843216, "duration": 63331}}]}, "3f0254c16b7bc20d18094d502f49138a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b7b43a0a224ef94", "status": "passed", "time": {"start": 1755545989614, "stop": 1755546011887, "duration": 22273}}]}, "0d9f0969c1336e077b7ada5963a2516a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7480b55353f93bc6", "status": "skipped", "statusDetails": "Skipped: 该脚本较特殊，先跳过", "time": {"start": 1755538242038, "stop": 1755538242038, "duration": 0}}]}, "461fa25c15c0a862f353e5384438bc5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "29e79e9a23da4107", "status": "passed", "time": {"start": 1755545581193, "stop": 1755545603404, "duration": 22211}}]}, "aac47e3ff4229b41a579f7c9ec938afb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bbf40f8013d48bf8", "status": "passed", "time": {"start": 1755535270790, "stop": 1755535294960, "duration": 24170}}]}, "a084b34b62992b8f17deb64af6e57e39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd5f2e8b9cbf2bfa", "status": "passed", "time": {"start": 1755551185946, "stop": 1755551208146, "duration": 22200}}]}, "600ddf60808e2a751a4a4742a65811c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "674938ed9b42673f", "status": "passed", "time": {"start": 1755550785029, "stop": 1755550815738, "duration": 30709}}]}, "8c5a5747e91f2cb0412111d5027bb7ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "563422272498211e", "status": "passed", "time": {"start": 1755541555184, "stop": 1755541580044, "duration": 24860}}]}, "ca269ac93364dc6180676f5680956b32": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "304d1991e7b7338d", "status": "passed", "time": {"start": 1755548061813, "stop": 1755548084125, "duration": 22312}}]}, "5b1e68388004fe021690469c3d83b485": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "320a4646a2dfbba3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm for 10:00 AM has been set.']\nassert False", "time": {"start": 1755539510703, "stop": 1755539534441, "duration": 23738}}]}, "e4bab2ec1074fdbc8b4508dfad12adfc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5cb654923a5394e", "status": "passed", "time": {"start": 1755538947324, "stop": 1755538995563, "duration": 48239}}]}, "afde8e86697e1ec7ad65ac0c993e60f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5c1299382f028a5e", "status": "passed", "time": {"start": 1755541074574, "stop": 1755541099089, "duration": 24515}}]}, "bf94eb080274f830f3097dd5adde1ed1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6179f1c471bf7b0", "status": "passed", "time": {"start": 1755540853327, "stop": 1755540875951, "duration": 22624}}]}, "89e187687dfa3317845107c44a62f287": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3ad46c4247b74393", "status": "passed", "time": {"start": 1755545545131, "stop": 1755545567100, "duration": 21969}}]}, "09f397887d36f3ef1e86e3c78272f1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "190a2c7d515274c1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755542412322, "stop": 1755542433838, "duration": 21516}}]}, "fd79b0d35f1f4639521f70b269d3aadc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68aca0b52f3a2e5e", "status": "passed", "time": {"start": 1755547253335, "stop": 1755547275740, "duration": 22405}}]}, "d18ed3013dc7867440fb611fb474ca05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9a80a467c73adc93", "status": "passed", "time": {"start": 1755538216019, "stop": 1755538240697, "duration": 24678}}]}, "599b7a465f619c38a4638073f59c38c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c0a498b834afb8f6", "status": "passed", "time": {"start": 1755550712201, "stop": 1755550734473, "duration": 22272}}]}, "c612b04b455e8fbc03acd18c2fc89827": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b082323455bd01a3", "status": "passed", "time": {"start": 1755545168500, "stop": 1755545190680, "duration": 22180}}]}, "16f38913a9d7e0ffc4c5ac51d5acf5c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7d0c5927796b6392", "status": "passed", "time": {"start": 1755541333638, "stop": 1755541355918, "duration": 22280}}]}, "8bf93fd8ac952a757cc694ecc78b8d51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c13cdb2a38eadf83", "status": "passed", "time": {"start": 1755532551996, "stop": 1755532582809, "duration": 30813}}]}, "18fb8c43c609a9825fe52e528761fd1b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d987956ec23c66d0", "status": "passed", "time": {"start": 1755539384585, "stop": 1755539411294, "duration": 26709}}]}, "519d11d818a361bc75d5af94c6a68b28": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8a9d56a1fc3fb7f6", "status": "passed", "time": {"start": 1755536764315, "stop": 1755536786453, "duration": 22138}}]}, "700a4f81d53d76c265778c230c99dd8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3409f70c757d998f", "status": "passed", "time": {"start": 1755545096743, "stop": 1755545118876, "duration": 22133}}]}, "b9bb05ac1dcf8926da63d4ecbb1524cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a4ff49fb87f88c71", "status": "passed", "time": {"start": 1755538180436, "stop": 1755538202211, "duration": 21775}}]}, "57a9b2e6f318afd186b838ed42ebd55c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3f740db0cef2375b", "status": "passed", "time": {"start": 1755544698511, "stop": 1755544721984, "duration": 23473}}]}, "e21c5dda6a9f09862a68c3a0bcda554a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dea6a036feb878e0", "status": "passed", "time": {"start": 1755549472460, "stop": 1755549495150, "duration": 22690}}]}, "dc901cadfe1de0042de7c0f7461a804e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5c602fd9982fe2", "status": "passed", "time": {"start": 1755543607155, "stop": 1755543628894, "duration": 21739}}]}, "6ba20fdcdbf83dd327ea91bd93e697e0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7f62336971efe5f8", "status": "passed", "time": {"start": 1755547197681, "stop": 1755547238974, "duration": 41293}}]}, "543965b4120af95548616c95b1b70ef1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f5cd234643930df6", "status": "passed", "time": {"start": 1755536213534, "stop": 1755536238671, "duration": 25137}}]}, "e1ef5de48cb99781fc16bc01be62dca2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "10d2f5628c8cbadc", "status": "passed", "time": {"start": 1755546025986, "stop": 1755546061293, "duration": 35307}}]}, "07dafe2b3e3ed9841a34e9fd19de58be": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b1915364bac38f76", "status": "passed", "time": {"start": 1755550071657, "stop": 1755550093619, "duration": 21962}}]}, "c04d9357fdaf44e6ee27f8a97ece6c5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "86b9e6f5cf0f3680", "status": "passed", "time": {"start": 1755548980760, "stop": 1755549003220, "duration": 22460}}]}, "5d0294174a7d609e38392f61f2170810": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "59ce51639a430d6d", "status": "passed", "time": {"start": 1755543534411, "stop": 1755543556865, "duration": 22454}}]}, "3004e41c81a7ebd857f79d043aaf59df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa9cf13684b28f0c", "status": "passed", "time": {"start": 1755534507267, "stop": 1755534536333, "duration": 29066}}]}, "d960192ea83ce0c13a534ec13ca1700e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "76bca7539af6676c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms']\nassert False", "time": {"start": 1755536449626, "stop": 1755536473336, "duration": 23710}}]}, "fa8d6ac5c42acf5f644e6f5370a9a773": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "15de6c1c1a1bde89", "status": "passed", "time": {"start": 1755543823441, "stop": 1755543845293, "duration": 21852}}]}, "544fc8b021d2dbcaf295cd05b798f816": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6eb43be3ca854b83", "status": "passed", "time": {"start": 1755549619480, "stop": 1755549641974, "duration": 22494}}]}, "1470cf4116a3328d5a8812cd15bb56f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "acf343c73d15018b", "status": "passed", "time": {"start": 1755541859998, "stop": 1755541882503, "duration": 22505}}]}, "53ec4c77118606257c016fc2f7b22065": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ec904da81ea162a6", "status": "passed", "time": {"start": 1755533836407, "stop": 1755533865439, "duration": 29032}}]}, "85b61394b4b07c85faf6e5081371fbf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bf3e0d5d8588af5b", "status": "passed", "time": {"start": 1755541038774, "stop": 1755541060765, "duration": 21991}}]}, "1695232002b2ad29ffa1faf52965470d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "72118dc54e25e056", "status": "passed", "time": {"start": 1755544158549, "stop": 1755544180596, "duration": 22047}}]}, "9fcc7f87aa3845b28893959bf17baa2b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3be2292986414830", "status": "passed", "time": {"start": 1755550404270, "stop": 1755550427074, "duration": 22804}}]}, "e1b97b8698ff620d6d8faf32f381c874": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4b2de58234c4451e", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a landscape painting image for me', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "time": {"start": 1755545024642, "stop": 1755545046581, "duration": 21939}}]}, "2060dd1cfd03194548c0456a10798266": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5efdde9e15b3a8b4", "status": "passed", "time": {"start": 1755534262782, "stop": 1755534287138, "duration": 24356}}]}, "0dc117f78053bbddaf3ffcf69df0af9d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf9f2c5a2ad8b9eb", "status": "passed", "time": {"start": 1755547941987, "stop": 1755547970029, "duration": 28042}}]}, "43a8e6496d8d78f2b8bc066858f8bdd9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "15dbd978c9e9ff3c", "status": "passed", "time": {"start": 1755547537971, "stop": 1755547559923, "duration": 21952}}]}, "7c74ed3e622ad29dd79be61222ad59bc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "abcbb8c6f6452862", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755539824246, "stop": 1755539847138, "duration": 22892}}]}, "569e770c250388bfbcf64d0cbbb8b351": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "11fc47f08ff73cab", "status": "passed", "time": {"start": 1755543462596, "stop": 1755543484509, "duration": 21913}}]}, "e7de7a828ef1b59725204585ed7e1d64": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "967ae3d31576809e", "status": "passed", "time": {"start": 1755549285581, "stop": 1755549310614, "duration": 25033}}]}, "b28e20f7b76af65c54477681eee78169": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2665f4e98abb9b17", "status": "skipped", "statusDetails": "Skipped: make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化", "time": {"start": 1755546851254, "stop": 1755546851254, "duration": 0}}]}, "acdb323f998d6127fbebbc545f6e8a59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a600cbbbab2efcc8", "status": "passed", "time": {"start": 1755533003998, "stop": 1755533039380, "duration": 35382}}]}, "c076d6e18e779bfeb810e69b30339aa2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "75e3e5fe8cdf881b", "status": "passed", "time": {"start": 1755540625588, "stop": 1755540668510, "duration": 42922}}]}, "edbb2ef8b0440e0325be2bfae4eb0bee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cd7d712956ba0f25", "status": "passed", "time": {"start": 1755543895172, "stop": 1755543917265, "duration": 22093}}]}, "d235f5ef7da67b833ad362b7c693c8f1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b737429b4e03ee3", "status": "passed", "time": {"start": 1755541519387, "stop": 1755541541302, "duration": 21915}}]}, "8373737839693413a37e35b627a0f5de": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9b668cc2394b5bc4", "status": "passed", "time": {"start": 1755540817193, "stop": 1755540839637, "duration": 22444}}]}, "4cfe8e55b2a91a62bbf1141ffc0cc530": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ab36b5e97a9a304d", "status": "passed", "time": {"start": 1755543787128, "stop": 1755543809387, "duration": 22259}}]}, "d8a01bf3d9b9092622318d8d22f17d9e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3e0c9fe5dca6e346", "status": "passed", "time": {"start": 1755543119889, "stop": 1755543142147, "duration": 22258}}]}, "b3fa1d22b59def5f059cd9b0eefbe2b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a06414489839fca", "status": "passed", "time": {"start": 1755534378496, "stop": 1755534404032, "duration": 25536}}]}, "d18ea588937139bb162adb1092a66013": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "937a6a1b80540fdf", "status": "passed", "time": {"start": 1755541676941, "stop": 1755541700497, "duration": 23556}}]}, "511b4baaab6d7793eefd3f92b3a77d8b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5717823ea652bea4", "status": "passed", "time": {"start": 1755539082567, "stop": 1755539105335, "duration": 22768}}]}, "548362627ce690e10e5f8ca35d247c62": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "74799b443815fe47", "status": "passed", "time": {"start": 1755538910308, "stop": 1755538933258, "duration": 22950}}]}, "359341836df6d43ec99426e6918dc6ca": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1bcb128e66a5b9a5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The following images are generated for you']\nassert False", "time": {"start": 1755537424190, "stop": 1755537502424, "duration": 78234}}]}, "861f0c94cdad5a5d60cd9fc71e2429d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f3c3b82db6e5f74a", "status": "passed", "time": {"start": 1755534074311, "stop": 1755534097644, "duration": 23333}}]}, "30903d6e764eebda77a45c5af4464d00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3708a4f460cc073c", "status": "passed", "time": {"start": 1755536068964, "stop": 1755536091096, "duration": 22132}}]}, "7ba4a9d343c0f63e9f654ce03ea4fa51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2b5c5da56c75ff4", "status": "passed", "time": {"start": 1755549546096, "stop": 1755549568423, "duration": 22327}}]}, "5eaa5a3015ce02f75c4c021fdbd2f78d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "15b8865ebef84c4", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy being outdoors?', 'enjoy running', 'outdoors', 'Do you have a favorite park or location where you like to go running on the grass?']，实际响应: '['running on the grass', '', '', '', 'That sounds like a great way to enjoy some fresh air! Do you have a favorite park or trail for running?', 'Generated by AI, for reference only', \"04:18 Dialogue Explore How to solve primary & junior high questions? Send my recent photo to mom on WhatsApp Taylor Swift's 2025 Pop Reign running on the grass That sounds like a great way to enjoy some fresh air! Do you have a favorite park or trail for running? Generated by AI, for reference only Best shoes for running on grass Running on grass injury risks Benefits of running on grass DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1755548253869, "stop": 1755548281183, "duration": 27314}}]}, "42bb23fa5566b20ae050e85bbee099ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dc8175669360e0", "status": "passed", "time": {"start": 1755544535387, "stop": 1755544557375, "duration": 21988}}]}, "434b905bf8be3ce2ee79606468e155db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4c621b6aa0134d82", "status": "passed", "time": {"start": 1755546196098, "stop": 1755546224342, "duration": 28244}}]}, "fca782bf64e9cf595a09003471d4cc31": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8abf1f1d22e91cdf", "status": "passed", "time": {"start": 1755546578584, "stop": 1755546601052, "duration": 22468}}]}, "ebd9dc4871a5e78e68997fd53d4e9d06": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9b5c88885de7c800", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1755541635822, "stop": 1755541662964, "duration": 27142}}]}, "d4409d015660212a7021f4aa7f849f30": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fae32e11992942f2", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A photo of a transparent glass cup', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "time": {"start": 1755542905521, "stop": 1755542927616, "duration": 22095}}]}, "6df22ddc82daabcf8389e60296dc694e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "86944148d896cd06", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Premier League Goals Ranking']\nassert False", "time": {"start": 1755535882062, "stop": 1755535905697, "duration": 23635}}]}, "7c862e3b7376f0fe8afd8d3c5d41a1ab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d6444fbbd9fa86d2", "status": "passed", "time": {"start": 1755551040688, "stop": 1755551062769, "duration": 22081}}]}, "d28e2beb1494e42c051e9b99add608fb": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "613be028b5c3d57", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Here are your alarms. Which one do you want to delete?']\nassert False", "time": {"start": 1755535648839, "stop": 1755535672450, "duration": 23611}}]}, "861aa58f9a3d0d9c9861d88316e784c5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fd60a3ff320b3f36", "status": "passed", "time": {"start": 1755533053460, "stop": 1755533075835, "duration": 22375}}]}, "6b4c2fb43e48aa6ef45b7a33c8b2d9ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e1f427e407268742", "status": "passed", "time": {"start": 1755548098626, "stop": 1755548124639, "duration": 26013}}]}, "cda905ef365af8bbcc5fba28f6bde9ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e82690830b99a38a", "status": "passed", "time": {"start": 1755540050262, "stop": 1755540077630, "duration": 27368}}]}, "bc062eca91b16841cac5c9865921b5c1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0cb405c048b5762", "status": "passed", "time": {"start": 1755532405659, "stop": 1755532427281, "duration": 21622}}]}, "a455fee07fb3c7d389380cb95d9a092c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9890369f3896e4c", "status": "passed", "time": {"start": 1755546772083, "stop": 1755546803639, "duration": 31556}}]}, "ff8d76af98b9fdfaa206acbf87daa843": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "abd00a5f90b2dab6", "status": "passed", "time": {"start": 1755544616887, "stop": 1755544647919, "duration": 31032}}]}, "1da800483d0bd7f8dbe657a8d5c37f76": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "859cd1ac4096dca4", "status": "passed", "time": {"start": 1755543678926, "stop": 1755543701149, "duration": 22223}}]}, "4a00cb3818a7086991fb9f7a4d2a3bb5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8b5755924feaa165", "status": "passed", "time": {"start": 1755551004319, "stop": 1755551026729, "duration": 22410}}]}, "81f981a4ddbff762d2de1cd977c5568a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "282267417455ce5d", "status": "passed", "time": {"start": 1755544988234, "stop": 1755545010487, "duration": 22253}}]}, "6f7052acfdd45e34e5dded44ad87416e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "efe9dd5698af5838", "status": "passed", "time": {"start": 1755538686791, "stop": 1755538711774, "duration": 24983}}]}, "abcb20b3882e9c0dbf1add7f63082581": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1d166cd5bb89f599", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755548023055, "stop": 1755548046986, "duration": 23931}}]}, "fdcf3737e32a4361e11902caf25fed5f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3e1ef9c9539919e2", "status": "passed", "time": {"start": 1755540890128, "stop": 1755540912248, "duration": 22120}}]}, "00e9182de9c9d3297d90ff42d6771a57": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ea971f8afc8ac3d8", "status": "passed", "time": {"start": 1755549095869, "stop": 1755549118203, "duration": 22334}}]}, "1ed60306ec6b3749ffacc2d410664db2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "20d4b045c93c589c", "status": "passed", "time": {"start": 1755537225693, "stop": 1755537279470, "duration": 53777}}]}, "d41a9c89a400d807309a9cecf36c0728": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e21c5cf1146a7ec", "status": "passed", "time": {"start": 1755533451310, "stop": 1755533491041, "duration": 39731}}]}, "a6002c930e7d5977f441f6060af6308d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef1df60b7ba75692", "status": "passed", "time": {"start": 1755534974838, "stop": 1755534996912, "duration": 22074}}]}, "6c315a350a546e1382e435255d28245b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cd2d1addc7608d8a", "status": "passed", "time": {"start": 1755549017205, "stop": 1755549039403, "duration": 22198}}]}, "d8bd499fa9e4e04741c5c255fac9036d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8948c2eff36606c6", "status": "passed", "time": {"start": 1755546657406, "stop": 1755546679914, "duration": 22508}}]}, "936ae2bf6db744b69d4acf28b22f7646": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "36874b7f55820c4a", "status": "passed", "time": {"start": 1755532881286, "stop": 1755532917469, "duration": 36183}}]}, "436193bc4e8c44d21b6520da0589f88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "11c915e41556cea3", "status": "passed", "time": {"start": 1755546425673, "stop": 1755546447711, "duration": 22038}}]}, "b2f52f3c587a626460e5698cac861baf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ab28f76d4ee5ffab", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755540396088, "stop": 1755540428767, "duration": 32679}}]}, "78164cec6ab8359be9416229a4882ef9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "89372ef1b45d48b4", "status": "passed", "time": {"start": 1755533999433, "stop": 1755534024121, "duration": 24688}}]}, "f06396240414bb7ac3c5c049002eef1e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aafe14e472f370a3", "status": "passed", "time": {"start": 1755548376434, "stop": 1755548404737, "duration": 28303}}]}, "78de5607a6208f59723ba6cf4fcf09c4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "65d99825173c0947", "status": "passed", "time": {"start": 1755534707111, "stop": 1755534727518, "duration": 20407}}]}, "bda3c1f82ca5c246958b5bf50db09a67": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "685452b2ba4f9a89", "status": "passed", "time": {"start": 1755549955814, "stop": 1755549977337, "duration": 21523}}]}, "eda16efd838471b84f33f12ec91662c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2aaf5347dbc01d8b", "status": "passed", "time": {"start": 1755548495332, "stop": 1755548517734, "duration": 22402}}]}, "fbd0b78d6e1c10d8dc70e2bd96aa5bdc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "79010a10fe7f461d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Your current location:']\nassert False", "time": {"start": 1755547984334, "stop": 1755548008569, "duration": 24235}}]}, "45b57073b776ed5666f2f20a47a4638f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9002bdf24cf5753c", "status": "passed", "time": {"start": 1755540359966, "stop": 1755540381952, "duration": 21986}}]}, "454f04318d433db60e7e6f2de5790fc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7547d8dd682ea094", "status": "passed", "time": {"start": 1755540011356, "stop": 1755540036420, "duration": 25064}}]}, "9375a7a6d7b67755564dec18857d7c65": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "130b6c368ae3f475", "status": "passed", "time": {"start": 1755543264398, "stop": 1755543295558, "duration": 31160}}]}, "c45aa63628fe12b375ba7e65c39d93b1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2c4695e673696dce", "status": "passed", "time": {"start": 1755536252538, "stop": 1755536274482, "duration": 21944}}]}, "cc158f7c05891fbac271a86692bc57a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "10ed7fd6efa81c6a", "status": "passed", "time": {"start": 1755550520777, "stop": 1755550546193, "duration": 25416}}]}, "fae56e9bcf9e0511ef4a7c93775731e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6e8ca94f901f3575", "status": "passed", "time": {"start": 1755542521457, "stop": 1755542545051, "duration": 23594}}]}, "85e8e199dba3ac2c9d89e132805a4404": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "3daf15b7b3653968", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['not installed yet. Please download the app and try again.']\nassert False", "time": {"start": 1755535347151, "stop": 1755535385655, "duration": 38504}}]}, "29390733aaf67e070f7c061b70bad8a5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "221ac10e9d0a0fd8", "status": "passed", "time": {"start": 1755537966527, "stop": 1755537988156, "duration": 21629}}]}, "228f8713a07d6ddbb8729f2f567c21d0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f475b7a1580f4c44", "status": "passed", "time": {"start": 1755535476591, "stop": 1755535516009, "duration": 39418}}]}, "221d94649ab3a384e6bc24767dceba21": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "673fedbb294a6f1c", "status": "passed", "time": {"start": 1755546817824, "stop": 1755546849811, "duration": 31987}}]}, "d2d9aa669417404f06e84a7a4387c55a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c86277f1fc22de8e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755540313809, "stop": 1755540345990, "duration": 32181}}]}}