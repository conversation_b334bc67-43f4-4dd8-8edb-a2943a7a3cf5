{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella.component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "TestEllaCloseAivana", "children": [{"name": "测试close aivana能正常执行", "uid": "b7f17392cbd20aee", "parentUid": "dc766e0abf993d1226d15b68c77f4f69", "status": "passed", "time": {"start": 1755532260908, "stop": 1755532293374, "duration": 32466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dc766e0abf993d1226d15b68c77f4f69"}], "uid": "2326186ebd37334731865ec8b5679891"}, {"name": "test_close_ella", "children": [{"name": "TestEllaClose<PERSON>lla", "children": [{"name": "测试close ella能正常执行", "uid": "e738d908d4f56738", "parentUid": "cec0309765c155f4f8740ed45c580c80", "status": "passed", "time": {"start": 1755532307829, "stop": 1755532344211, "duration": 36382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cec0309765c155f4f8740ed45c580c80"}], "uid": "989d936277ca7c8b51dc874f7afac7c1"}, {"name": "test_close_folax", "children": [{"name": "TestEllaCloseFolax", "children": [{"name": "测试close folax能正常执行", "uid": "b6e2a81031476512", "parentUid": "7ae1e31d59d51e63408ae29d2c8c926e", "status": "passed", "time": {"start": 1755532358236, "stop": 1755532391952, "duration": 33716}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ae1e31d59d51e63408ae29d2c8c926e"}], "uid": "d7b55da797c782b6c2cc729406440a10"}, {"name": "test_close_phonemaster", "children": [{"name": "TestEllaClosePhonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "d0cb405c048b5762", "parentUid": "976102e81bb88d3807a1e25c3ddeca37", "status": "passed", "time": {"start": 1755532405659, "stop": 1755532427281, "duration": 21622}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "976102e81bb88d3807a1e25c3ddeca37"}], "uid": "e1a42dca75ede54847ef42fc018b38d7"}, {"name": "test_continue_music", "children": [{"name": "TestEllaContinueMusic", "children": [{"name": "测试continue music能正常执行", "uid": "fe991698e5267e29", "parentUid": "1006aa648a1d87005804dfc1cb937d40", "status": "passed", "time": {"start": 1755532440416, "stop": 1755532462076, "duration": 21660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1006aa648a1d87005804dfc1cb937d40"}], "uid": "091d4456268247868ef8ba9cc951a6e9"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "TestEllaCreateMettingScheduleTomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "7823681a04e66dc9", "parentUid": "2723183c861afd907653f50d6bfc622c", "status": "failed", "time": {"start": 1755532475122, "stop": 1755532503008, "duration": 27886}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2723183c861afd907653f50d6bfc622c"}], "uid": "e4b3f0da2dd18af340a32e173acf4400"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "79a84d7137b558e3", "parentUid": "75d733f64143d69107fc406cc5a4bb5d", "status": "passed", "time": {"start": 1755532516620, "stop": 1755532538737, "duration": 22117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "75d733f64143d69107fc406cc5a4bb5d"}], "uid": "5ac49ae4fb77ab653eda0b303f9fd551"}, {"name": "test_disable_call_on_hold", "children": [{"name": "TestEllaDisableCallHold", "children": [{"name": "测试disable call on hold返回正确的不支持响应", "uid": "c13cdb2a38eadf83", "parentUid": "da1ee3b782a3ae934466a10ab45d1e54", "status": "passed", "time": {"start": 1755532551996, "stop": 1755532582809, "duration": 30813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "da1ee3b782a3ae934466a10ab45d1e54"}], "uid": "8b37e9595815ca84fbf6e36410b63b3a"}, {"name": "test_display_the_route_go_company", "children": [{"name": "TestEllaOpenMaps", "children": [{"name": "测试display the route go company", "uid": "744cbb103ecdfa25", "parentUid": "fae47b3031d4ccce961b95f3d652edcf", "status": "passed", "time": {"start": 1755532595464, "stop": 1755532622408, "duration": 26944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae47b3031d4ccce961b95f3d652edcf"}], "uid": "a2b2fbfd344f33b51bd68e5977e57dae"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "TestEllaMyPhoneIsTooSlow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "a87b2b4fbad25d0f", "parentUid": "a0a6853a187713f8a5ac0e869d619853", "status": "passed", "time": {"start": 1755532635332, "stop": 1755532656373, "duration": 21041}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0a6853a187713f8a5ac0e869d619853"}], "uid": "1a3ac4d4348e23076891e9f62ee93107"}, {"name": "test_next_channel", "children": [{"name": "TestEllaNextChannel", "children": [{"name": "测试next channel能正常执行", "uid": "ff4baae6c2c6c2f2", "parentUid": "79fe0adb48b7a7e540c509bbc071945d", "status": "passed", "time": {"start": 1755532669374, "stop": 1755532691320, "duration": 21946}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79fe0adb48b7a7e540c509bbc071945d"}], "uid": "bf525e696763b8e3f2fb093dc5260aa2"}, {"name": "test_open_camera", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open camera能正常执行", "uid": "f3e2e05aaeb14ddc", "parentUid": "e03396d1fe1c4deef0c8cf91f821a468", "status": "passed", "time": {"start": 1755532704470, "stop": 1755532734454, "duration": 29984}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e03396d1fe1c4deef0c8cf91f821a468"}], "uid": "92e88ab08159dfaf49a78312925760b7"}, {"name": "test_open_clock", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "open clock", "uid": "8ac2fbb40ff2fe45", "parentUid": "b2210d96f4ac8230585a75115cd1d8c2", "status": "failed", "time": {"start": 1755532748815, "stop": 1755532778833, "duration": 30018}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2210d96f4ac8230585a75115cd1d8c2"}], "uid": "d636d9848e1c7419e6f84e7e49c07171"}, {"name": "test_open_contact", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "1486d4607be0b253", "parentUid": "39d05c6acdceec56c3c12fd23896f56f", "status": "passed", "time": {"start": 1755532793075, "stop": 1755532830457, "duration": 37382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39d05c6acdceec56c3c12fd23896f56f"}], "uid": "cd82497cee692a7c9a65a6184e27c221"}, {"name": "test_open_countdown", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open countdown能正常执行", "uid": "4d7d35dfd5353048", "parentUid": "e10c6770504fedd71bbf3e383f94dc48", "status": "passed", "time": {"start": 1755532844673, "stop": 1755532867161, "duration": 22488}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e10c6770504fedd71bbf3e383f94dc48"}], "uid": "f0d78da692838ddb314b12cc4febc20b"}, {"name": "test_open_dialer", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open dialer能正常执行", "uid": "36874b7f55820c4a", "parentUid": "068eb91b79bee6accc0bcab4a6367e76", "status": "passed", "time": {"start": 1755532881286, "stop": 1755532917469, "duration": 36183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "068eb91b79bee6accc0bcab4a6367e76"}], "uid": "3c3dd50d3fd335b243796d4c0c9bfe64"}, {"name": "test_open_ella", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "f866a2bad0ee5662", "parentUid": "907f87c6914ea345d20478681f484e8d", "status": "passed", "time": {"start": 1755532931498, "stop": 1755532953979, "duration": 22481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "907f87c6914ea345d20478681f484e8d"}], "uid": "6b30479a0405c27782687611167a8e3d"}, {"name": "test_open_folax", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open folax能正常执行", "uid": "9dc10018605926ba", "parentUid": "031c2cd337cd7647fd7b60d0d75ba16e", "status": "passed", "time": {"start": 1755532968248, "stop": 1755532990113, "duration": 21865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "031c2cd337cd7647fd7b60d0d75ba16e"}], "uid": "fb1e65e2e60ee7c63b5831633cb5005c"}, {"name": "test_open_phone", "children": [{"name": "TestEllaContactCommandConcise", "children": [{"name": "测试open contact命令", "uid": "a600cbbbab2efcc8", "parentUid": "147ffaef9b3cdea572ba5e4caea69dbe", "status": "passed", "time": {"start": 1755533003998, "stop": 1755533039380, "duration": 35382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "147ffaef9b3cdea572ba5e4caea69dbe"}], "uid": "75ded0b626adc3c7c8dbefcba1c0b5f0"}, {"name": "test_pause_fm", "children": [{"name": "TestEllaPauseFm", "children": [{"name": "测试pause fm能正常执行", "uid": "fd60a3ff320b3f36", "parentUid": "88ca4f005094f55c66457c720f6cbb47", "status": "passed", "time": {"start": 1755533053460, "stop": 1755533075835, "duration": 22375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88ca4f005094f55c66457c720f6cbb47"}], "uid": "d9c43a396815c7e853000c20e47b8fa8"}, {"name": "test_pause_music", "children": [{"name": "TestEllaPauseMusic", "children": [{"name": "测试pause music能正常执行", "uid": "f8f62e26362f4acd", "parentUid": "1f79feb59e2eb0bf0fb7e1387903bfee", "status": "passed", "time": {"start": 1755533089991, "stop": 1755533112024, "duration": 22033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f79feb59e2eb0bf0fb7e1387903bfee"}], "uid": "8a3b16d13b6ccc8492638853c9ba04aa"}, {"name": "test_pause_song", "children": [{"name": "TestEllaPauseSong", "children": [{"name": "测试pause song能正常执行", "uid": "35deb8fb0261ed29", "parentUid": "a3c6e1d0398474d5bb90c22ff0ad7930", "status": "failed", "time": {"start": 1755533126191, "stop": 1755533147873, "duration": 21682}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c6e1d0398474d5bb90c22ff0ad7930"}], "uid": "6c009013e1160a80039c4a7e2e07d104"}, {"name": "test_phone_boost", "children": [{"name": "TestEllaPhoneBoost", "children": [{"name": "测试phone boost能正常执行", "uid": "ea5b3cbd54bf3b2a", "parentUid": "64e4d2ec8ce2a83bbdd5f37990e1b292", "status": "passed", "time": {"start": 1755533162236, "stop": 1755533183911, "duration": 21675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "64e4d2ec8ce2a83bbdd5f37990e1b292"}], "uid": "b327c70b1579c29f98ae28c9e3ffe844"}, {"name": "test_play_afro_strut", "children": [{"name": "TestEllaOpenPlayAfroStrut", "children": [{"name": "测试play afro strut", "uid": "ee1adc8b75cf0025", "parentUid": "9988b4042f5d7026e75fc8fcb98f5519", "status": "passed", "time": {"start": 1755533197292, "stop": 1755533240096, "duration": 42804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9988b4042f5d7026e75fc8fcb98f5519"}], "uid": "3aef4f1ab476b7c11ee7ec75e072c264"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music", "uid": "68c40bdfdb691181", "parentUid": "a2f6d87f760b2cc31c5c6aad0f136dd1", "status": "passed", "time": {"start": 1755533254235, "stop": 1755533293660, "duration": 39425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2f6d87f760b2cc31c5c6aad0f136dd1"}], "uid": "5ac2a1940b596719fc1fe3160a450962"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "TestEllaOpenMusic", "children": [{"name": "测试play jay chou's music by spotify", "uid": "8e014ba05d1d3d20", "parentUid": "c8aebf1f28528d18214bd1a9e19bfca7", "status": "passed", "time": {"start": 1755533307222, "stop": 1755533331544, "duration": 24322}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8aebf1f28528d18214bd1a9e19bfca7"}], "uid": "4c3a9736ba488ccfdfd13dbb9608ebde"}, {"name": "test_play_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play music", "uid": "dbd33f4313f9e7e1", "parentUid": "de473794f9027034dfc0e767cb0a1fc8", "status": "passed", "time": {"start": 1755533344992, "stop": 1755533383918, "duration": 38926}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de473794f9027034dfc0e767cb0a1fc8"}], "uid": "755e0b08ce2f0d843df6c102fcc9d49b"}, {"name": "test_play_rock_music", "children": [{"name": "TestEllaOpen<PERSON>a", "children": [{"name": "测试play rock music", "uid": "d7a88c8a026436bb", "parentUid": "b0f90c45c8c8e64fb5c2a98f923bc6fa", "status": "passed", "time": {"start": 1755533397763, "stop": 1755533436640, "duration": 38877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0f90c45c8c8e64fb5c2a98f923bc6fa"}], "uid": "735fc1adab6496548b5a97d70b865131"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "TestEllaOpenPlaySunBeSongOfJideChord", "children": [{"name": "测试play sun be song of jide chord", "uid": "e21c5cf1146a7ec", "parentUid": "5c987d2d0d756e70bc56eda09e92e854", "status": "passed", "time": {"start": 1755533451310, "stop": 1755533491041, "duration": 39731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c987d2d0d756e70bc56eda09e92e854"}], "uid": "020b88bee0045afa929d84639829645d"}, {"name": "test_previous_music", "children": [{"name": "TestEllaPreviousMusic", "children": [{"name": "测试previous music能正常执行", "uid": "a00ea4f467dbafd9", "parentUid": "da7caa7ac31745b37f41cb283803912e", "status": "failed", "time": {"start": 1755533504668, "stop": 1755533526338, "duration": 21670}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "da7caa7ac31745b37f41cb283803912e"}], "uid": "bc6fa07388e696170fffb17281da4c7f"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "TestEllaRecordAudioSeconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "f1d7293de10d9144", "parentUid": "964e536f897371e5579180ce760d9610", "status": "passed", "time": {"start": 1755533540018, "stop": 1755533565891, "duration": 25873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "964e536f897371e5579180ce760d9610"}], "uid": "b622f700f99c575c72cb1944c3e3c02e"}, {"name": "test_resume_music", "children": [{"name": "TestEllaResumeMusic", "children": [{"name": "测试resume music能正常执行", "uid": "dd8189ee216b6c12", "parentUid": "cd0e4afc91d2a02372d847be235bede4", "status": "passed", "time": {"start": 1755533579539, "stop": 1755533601527, "duration": 21988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd0e4afc91d2a02372d847be235bede4"}], "uid": "b281dddb4da708a18c1bdad0e1402ec1"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set an alarm at 8 am", "uid": "b7c3846ca060a587", "parentUid": "33bfbdda545b50afa47fc22456e93557", "status": "failed", "time": {"start": 1755533615693, "stop": 1755533637438, "duration": 21745}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33bfbdda545b50afa47fc22456e93557"}], "uid": "a3607188923688aad3a2ecec54f9a7c1"}, {"name": "test_stop_playing", "children": [{"name": "TestEllaOpenYoutube", "children": [{"name": "测试stop playing", "uid": "687c67ac43338e6c", "parentUid": "04a0cd56790b683054ed72365f59d2e7", "status": "passed", "time": {"start": 1755533651835, "stop": 1755533675491, "duration": 23656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04a0cd56790b683054ed72365f59d2e7"}], "uid": "8960b75623e553fca190959e7f507f14"}, {"name": "test_take_a_screenshot", "children": [{"name": "TestEllaTakeScreenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "61332991e71deb47", "parentUid": "ac5545075b0e4292eed508b41e08ab8c", "status": "passed", "time": {"start": 1755533689519, "stop": 1755533713980, "duration": 24461}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac5545075b0e4292eed508b41e08ab8c"}], "uid": "0daa64e90efed9ce03db463cca6bb5b1"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn off the 7AM alarm", "uid": "96b1f45ef3f7523", "parentUid": "7badc55fb60a0a664ac693ed5106cf16", "status": "passed", "time": {"start": 1755533727782, "stop": 1755533749866, "duration": 22084}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7badc55fb60a0a664ac693ed5106cf16"}], "uid": "ca54947974933cdcc156785ae159284e"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn off the 8 am alarm", "uid": "3a4693475a14571f", "parentUid": "b239508423aac2a95c8e0b7b6e63f430", "status": "passed", "time": {"start": 1755533764116, "stop": 1755533786554, "duration": 22438}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b239508423aac2a95c8e0b7b6e63f430"}], "uid": "a4642e1a3f32fdb6af62d6449783bb94"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "98d99df8f07af07c", "parentUid": "d228b7163521a3d21a5179f5d0599453", "status": "passed", "time": {"start": 1755533800620, "stop": 1755533822664, "duration": 22044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d228b7163521a3d21a5179f5d0599453"}], "uid": "836d9647d7fb73692aca73e413ef3024"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "ec904da81ea162a6", "parentUid": "de146ab7aca490ae6653437938175de4", "status": "passed", "time": {"start": 1755533836407, "stop": 1755533865439, "duration": 29032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de146ab7aca490ae6653437938175de4"}], "uid": "4a586ea49df80d8628d524f64c639079"}], "uid": "5948c7c27387d214d4b5e1b876d4cb27"}, {"name": "testcases.test_ella.dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试appeler maman能正常执行", "uid": "9e09c78c059d573c", "parentUid": "f8fcd4a1c0a83d1c3db334295a60b72e", "status": "passed", "time": {"start": 1755533879354, "stop": 1755533901182, "duration": 21828}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8fcd4a1c0a83d1c3db334295a60b72e"}], "uid": "97ecf501f8b24237ae07b2178fff9b3f"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "TestEllaBookFlightParis", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "2016852632d1a42f", "parentUid": "d9246a10b1546e33724912523509ec95", "status": "broken", "time": {"start": 1755533915043, "stop": 1755533940339, "duration": 25296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9246a10b1546e33724912523509ec95"}], "uid": "09531ecb34a029327b70c5fe7633b7f3"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "TestEllaCallMomThroughWhatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "c7834e9682ae356d", "parentUid": "b2bb86980dd84d36c5c8df2b8200871f", "status": "passed", "time": {"start": 1755533954607, "stop": 1755533985423, "duration": 30816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2bb86980dd84d36c5c8df2b8200871f"}], "uid": "b093744c9f16724e1a2928dcb99b362b"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "TestEllaCanYouGiveMeCoin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "89372ef1b45d48b4", "parentUid": "4c5d38b6a82090208170044db256ae60", "status": "passed", "time": {"start": 1755533999433, "stop": 1755534024121, "duration": 24688}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c5d38b6a82090208170044db256ae60"}], "uid": "804d60782c903cc7f780a62383e53a73"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "TestEllaCannotLoginGoogleEmailBox", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "939cebe22906f8ea", "parentUid": "9d85ac6932670633773061ce5b33ae53", "status": "passed", "time": {"start": 1755534037938, "stop": 1755534060173, "duration": 22235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d85ac6932670633773061ce5b33ae53"}], "uid": "4a7c1215b1a8ca8a7a3c6f82e99131c2"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "TestEllaCheckStatusUpdatesWhatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "f3c3b82db6e5f74a", "parentUid": "8e354564384b35c7b587be597ea49fad", "status": "passed", "time": {"start": 1755534074311, "stop": 1755534097644, "duration": 23333}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e354564384b35c7b587be597ea49fad"}], "uid": "5ecd3e5cb8ea680501b5eea40dc4f984"}, {"name": "test_close_whatsapp", "children": [{"name": "TestEllaCloseWhatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "be07492f78740106", "parentUid": "c32b137a79c22ae5d9f6dcb3c7a455e4", "status": "passed", "time": {"start": 1755534111697, "stop": 1755534135236, "duration": 23539}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c32b137a79c22ae5d9f6dcb3c7a455e4"}], "uid": "9ab207a1a50592cfc97f93c48ff061e9"}, {"name": "test_continue_playing", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试continue playing能正常执行", "uid": "ce2c76aab15be26", "parentUid": "6dc89c2239281851458ce4bffbd1dcb3", "status": "passed", "time": {"start": 1755534149151, "stop": 1755534172506, "duration": 23355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dc89c2239281851458ce4bffbd1dcb3"}], "uid": "d85510160425cbac31cbff0430da7a08"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "TestEllaCouldYouPleaseSearchAnMe", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "962d28ec069171e0", "parentUid": "05954a2ceae7fd66d1757bbd59b47ed6", "status": "failed", "time": {"start": 1755534186900, "stop": 1755534212350, "duration": 25450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05954a2ceae7fd66d1757bbd59b47ed6"}], "uid": "18eeff7a50c6893d6191e5b7834fb912"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "f6e59a7abb074140", "parentUid": "130a6b251dee0788fb7cea69ff620c68", "status": "passed", "time": {"start": 1755534226372, "stop": 1755534248683, "duration": 22311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "130a6b251dee0788fb7cea69ff620c68"}], "uid": "d6678d1bee0dacb44840d09dee8fdbd2"}, {"name": "test_give_me_some_money", "children": [{"name": "TestEllaGiveMeSomeMoney", "children": [{"name": "测试give me some money能正常执行", "uid": "5efdde9e15b3a8b4", "parentUid": "112c6b7b50b46672fc16704c6fc8c8ea", "status": "passed", "time": {"start": 1755534262782, "stop": 1755534287138, "duration": 24356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "112c6b7b50b46672fc16704c6fc8c8ea"}], "uid": "cef13fbaa76291ab7ffe428fd9b3ff0a"}, {"name": "test_global_gdp_trends", "children": [{"name": "TestEllaGlobalGdpTrends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "36bd15928d9b0905", "parentUid": "831198a93942962c147b23029d2ab9c8", "status": "passed", "time": {"start": 1755534301066, "stop": 1755534326958, "duration": 25892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "831198a93942962c147b23029d2ab9c8"}], "uid": "fff3da9e7aaa932288c845a77dbb22ae"}, {"name": "test_go_on_playing_fm", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试go on playing fm能正常执行", "uid": "7186c09ba4d6ed7b", "parentUid": "7d9d8dc2f60ad69d4996d898c7652245", "status": "passed", "time": {"start": 1755534340663, "stop": 1755534364374, "duration": 23711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d9d8dc2f60ad69d4996d898c7652245"}], "uid": "84a31fbfbc1186641548d4ba45111773"}, {"name": "test_hello_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试hello hello能正常执行", "uid": "6a06414489839fca", "parentUid": "ba652584bc7b97ea54da02ced4645ba5", "status": "passed", "time": {"start": 1755534378496, "stop": 1755534404032, "duration": 25536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba652584bc7b97ea54da02ced4645ba5"}], "uid": "ce7aabb9954dd4026695310de1d225b1"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "TestEllaHelpMeWriteAnEmailMakeAnAppointmentVisit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "febd4965bc5b5d32", "parentUid": "f60519e8aeef426bc3a93755c572d6fa", "status": "passed", "time": {"start": 1755534418034, "stop": 1755534453910, "duration": 35876}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f60519e8aeef426bc3a93755c572d6fa"}], "uid": "e821be54d27a42f20fe55cd5d6203630"}, {"name": "test_hi", "children": [{"name": "TestEllaHi", "children": [{"name": "测试hi能正常执行", "uid": "e31835399ffcc5d7", "parentUid": "1223273ba8f6a08b5ed65920246c9e1b", "status": "passed", "time": {"start": 1755534467838, "stop": 1755534493385, "duration": 25547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1223273ba8f6a08b5ed65920246c9e1b"}], "uid": "29e62e7f369af137801d22a9b259e4ec"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试how is the weather today能正常执行", "uid": "aa9cf13684b28f0c", "parentUid": "34ffa36d6317bc520ea4d2709c2ddf11", "status": "passed", "time": {"start": 1755534507267, "stop": 1755534536333, "duration": 29066}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ffa36d6317bc520ea4d2709c2ddf11"}], "uid": "9cf284f12ed28b2464e982b240e1a734"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "TestEllaHowIsWheatherToday", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "294d0761bd0b35ea", "parentUid": "d29828ee15804f262c19aa832269e66a", "status": "passed", "time": {"start": 1755534550532, "stop": 1755534572689, "duration": 22157}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d29828ee15804f262c19aa832269e66a"}], "uid": "23a13ba389e295288e7459ad4f05d4d2"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "TestEllaHowSWeatherToday", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "3006e468cff1526", "parentUid": "862e57348213f2baefa0f1dca30e6cac", "status": "passed", "time": {"start": 1755534586604, "stop": 1755534615590, "duration": 28986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "862e57348213f2baefa0f1dca30e6cac"}], "uid": "e9f1cfec1579f5ee85e18450ed232a94"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "80c8b496993f5e37", "parentUid": "6278997b6a5cd9b65afdbc6961c1f923", "status": "passed", "time": {"start": 1755534629582, "stop": 1755534658909, "duration": 29327}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6278997b6a5cd9b65afdbc6961c1f923"}], "uid": "0de8d8ddf42930e480591e30731da5d4"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "TestEllaHowSayHelloFrench", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "d993e90b89a322eb", "parentUid": "5667c881ac1997f398af2c0cc0dc170c", "status": "passed", "time": {"start": 1755534672894, "stop": 1755534693038, "duration": 20144}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5667c881ac1997f398af2c0cc0dc170c"}], "uid": "594bdcb49fa434818b399bc139ed191f"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "TestEllaHowSayILoveYouFrench", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "65d99825173c0947", "parentUid": "9966bad905bebb015e3efc2cbe564d9a", "status": "passed", "time": {"start": 1755534707111, "stop": 1755534727518, "duration": 20407}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9966bad905bebb015e3efc2cbe564d9a"}], "uid": "403c67e2055acbe3d575b2a0bb5e3e20"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "TestEllaIWannaBeRich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "b81fc5f7f6d96890", "parentUid": "c7de20f7cd05ebdbc63478eed16102dc", "status": "passed", "time": {"start": 1755534741316, "stop": 1755534765733, "duration": 24417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7de20f7cd05ebdbc63478eed16102dc"}], "uid": "ec547ced2ed20bbbff5a2043b9e6319c"}, {"name": "test_i_want_to_listen_to_fm", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试i want to listen to fm能正常执行", "uid": "cf9f80b2bf9e2831", "parentUid": "3e6dd10d8cde8c1c1a361ba19f1905ce", "status": "passed", "time": {"start": 1755534779500, "stop": 1755534801331, "duration": 21831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e6dd10d8cde8c1c1a361ba19f1905ce"}], "uid": "f8aa7190b362d9bc914b26a879cedfbb"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "TestEllaIWantMakeCall", "children": [{"name": "测试i want to make a call能正常执行", "uid": "b888d3bc51fa3b1b", "parentUid": "503f251f10534ae24ea758298ae39419", "status": "passed", "time": {"start": 1755534815267, "stop": 1755534846296, "duration": 31029}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "503f251f10534ae24ea758298ae39419"}], "uid": "9643231b759e5225f7987ba2484e3f41"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "TestEllaIWantWatchFireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "c3fab8a57bf5eaa5", "parentUid": "a62423648b24979e9d41076cc7fc4719", "status": "passed", "time": {"start": 1755534860314, "stop": 1755534885786, "duration": 25472}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a62423648b24979e9d41076cc7fc4719"}], "uid": "0c36eef7e897f644d53145d2fdea584f"}, {"name": "test_introduce_yourself", "children": [{"name": "TestEllaIntroduceYourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "e062c6b9a8b843fd", "parentUid": "af550fbfe44b78ae5ecace3f2e6c80de", "status": "passed", "time": {"start": 1755534899694, "stop": 1755534924691, "duration": 24997}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af550fbfe44b78ae5ecace3f2e6c80de"}], "uid": "fd2fbebbb7b47e097121b9ff95e07e74"}, {"name": "test_last_channel", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试last channel能正常执行", "uid": "66e2b58373c1c525", "parentUid": "820d0da6e05c44863004684d1091514c", "status": "passed", "time": {"start": 1755534938772, "stop": 1755534961004, "duration": 22232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "820d0da6e05c44863004684d1091514c"}], "uid": "5cda476bfd122ec7b145d2d536137e78"}, {"name": "test_listen_to_fm", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试listen to fm能正常执行", "uid": "ef1df60b7ba75692", "parentUid": "4d5c54fd2e90484a9fcaed24f986c1c5", "status": "passed", "time": {"start": 1755534974838, "stop": 1755534996912, "duration": 22074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d5c54fd2e90484a9fcaed24f986c1c5"}], "uid": "3c594eadada6406a212e1248d8069376"}, {"name": "test_make_a_call", "children": [{"name": "TestEllaMakeCall", "children": [{"name": "测试make a call能正常执行", "uid": "eb676212321caaf6", "parentUid": "41b39c88fd91c3d3a8968837a65fbbfa", "status": "passed", "time": {"start": 1755535011062, "stop": 1755535041885, "duration": 30823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41b39c88fd91c3d3a8968837a65fbbfa"}], "uid": "4c53e2e35617ddca6d4c7ed877b594a7"}, {"name": "test_measure_blood_oxygen", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试measure blood oxygen", "uid": "409e680a2a7a5258", "parentUid": "669b561dda357e0a78cd474e478b760f", "status": "passed", "time": {"start": 1755535055553, "stop": 1755535077198, "duration": 21645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "669b561dda357e0a78cd474e478b760f"}], "uid": "9a1d8b891ab1c921e9aeae6a1fc9e016"}, {"name": "test_measure_heart_rate", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试measure heart rate", "uid": "ddc06db5db20b677", "parentUid": "e711cb3a8726ee47c816f06a51c76226", "status": "passed", "time": {"start": 1755535091232, "stop": 1755535113013, "duration": 21781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e711cb3a8726ee47c816f06a51c76226"}], "uid": "6f248a0bd06ee9fb1f7f8eaff5658509"}, {"name": "test_next_music", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试next music能正常执行", "uid": "59a4ff0abe989014", "parentUid": "0f4777a16fd1c10ac7e9b4ff4f5776d7", "status": "passed", "time": {"start": 1755535127017, "stop": 1755535149023, "duration": 22006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f4777a16fd1c10ac7e9b4ff4f5776d7"}], "uid": "442354186439d371c056740d44f525a3"}, {"name": "test_next_song", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试next song能正常执行", "uid": "817201f101a3fce8", "parentUid": "fefbb85a772569566b1a005564b72b95", "status": "passed", "time": {"start": 1755535162990, "stop": 1755535185150, "duration": 22160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fefbb85a772569566b1a005564b72b95"}], "uid": "b3d0a29f48948dec64e6435937c72d4c"}, {"name": "test_open_app", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "c1f1346361a3976a", "parentUid": "ef90c8fa468d5ca1174d3ecae675d779", "status": "passed", "time": {"start": 1755535199034, "stop": 1755535220932, "duration": 21898}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef90c8fa468d5ca1174d3ecae675d779"}], "uid": "13e5ce3e437fc3eba1f54c55b67226eb"}, {"name": "test_pause_music", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试pause music能正常执行", "uid": "81430c2cbc2daab3", "parentUid": "3c6298ced9b1444e00c409d35e70b995", "status": "passed", "time": {"start": 1755535235001, "stop": 1755535256706, "duration": 21705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3c6298ced9b1444e00c409d35e70b995"}], "uid": "868e42a1d27583a59bfb8781ef8cc13a"}, {"name": "test_play_music_by_VLC", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by VLC", "uid": "bbf40f8013d48bf8", "parentUid": "892ac35f77572784870fff8535230a0f", "status": "passed", "time": {"start": 1755535270790, "stop": 1755535294960, "duration": 24170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "892ac35f77572784870fff8535230a0f"}], "uid": "ce168084d3cfe53cd0aeab22b5c369bb"}, {"name": "test_play_music_by_boomplay", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by boomplay", "uid": "3b0a2a501433e61e", "parentUid": "b9d798156a0215216bcff8fb425c0fe5", "status": "passed", "time": {"start": 1755535308816, "stop": 1755535333056, "duration": 24240}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9d798156a0215216bcff8fb425c0fe5"}], "uid": "1597ecf68cb611082af48367238cc813"}, {"name": "test_play_music_by_visha", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by visha", "uid": "3daf15b7b3653968", "parentUid": "5ed7cbc3b1c131f5e24ae47db3cf43fe", "status": "failed", "time": {"start": 1755535347151, "stop": 1755535385655, "duration": 38504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ed7cbc3b1c131f5e24ae47db3cf43fe"}], "uid": "cf9a003f99c4b7fca315a507d1a23660"}, {"name": "test_play_music_by_yandex_music", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by yandex music", "uid": "1c76bbb443caa181", "parentUid": "983892385ee3abf2e42876954c93858c", "status": "passed", "time": {"start": 1755535399772, "stop": 1755535424407, "duration": 24635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "983892385ee3abf2e42876954c93858c"}], "uid": "df27d434403a0bfe582afda97c08ae38"}, {"name": "test_play_music_on_boomplayer", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music on boomplayer", "uid": "25a1d94359b88fb7", "parentUid": "b9d6ebc551e5cf29ef1d89dac024c76a", "status": "passed", "time": {"start": 1755535438162, "stop": 1755535462677, "duration": 24515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9d6ebc551e5cf29ef1d89dac024c76a"}], "uid": "a1ee256ae2705c004dbfbed36b6ab06c"}, {"name": "test_play_music_on_visha", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music on visha", "uid": "f475b7a1580f4c44", "parentUid": "aa97f2bc6462853bc5721f2b9ce55fb7", "status": "passed", "time": {"start": 1755535476591, "stop": 1755535516009, "duration": 39418}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aa97f2bc6462853bc5721f2b9ce55fb7"}], "uid": "ec00708c40596fda8b236ff83f5f3feb"}, {"name": "test_play_news", "children": [{"name": "TestEllaOpenPlayNews", "children": [{"name": "测试play news", "uid": "e6acbfc61014ddb0", "parentUid": "bfe747e7270f83431a268436bcbe9627", "status": "passed", "time": {"start": 1755535530009, "stop": 1755535557931, "duration": 27922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfe747e7270f83431a268436bcbe9627"}], "uid": "f2cee0c061bba4cacfb9848756bf2374"}, {"name": "test_play_political_news", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play political news", "uid": "70ae52d960b93bbd", "parentUid": "31db74418c8cacb31ff13ffbb519e314", "status": "passed", "time": {"start": 1755535571881, "stop": 1755535599049, "duration": 27168}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31db74418c8cacb31ff13ffbb519e314"}], "uid": "f61bac7c6bed9fe60b44871a005ea908"}, {"name": "test_previous_song", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试previous song能正常执行", "uid": "d454847eee1af6e6", "parentUid": "931f06154cdf36555acbb1e7e387caf6", "status": "passed", "time": {"start": 1755535613034, "stop": 1755535634719, "duration": 21685}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "931f06154cdf36555acbb1e7e387caf6"}], "uid": "6df81e48e262e31ae2ce464fc5aebc7c"}, {"name": "test_remove_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试remove alarms能正常执行", "uid": "613be028b5c3d57", "parentUid": "b15b476338ea94639ce72d4b1f19e87e", "status": "failed", "time": {"start": 1755535648839, "stop": 1755535672450, "duration": 23611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b15b476338ea94639ce72d4b1f19e87e"}], "uid": "55977b9d46dafd95f435ed2c0dd6a4ac"}, {"name": "test_say_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试say hello能正常执行", "uid": "4947f2cef0c48e09", "parentUid": "312e23dd2efc18a08c69b14303b1e4e8", "status": "passed", "time": {"start": 1755535686786, "stop": 1755535712138, "duration": 25352}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "312e23dd2efc18a08c69b14303b1e4e8"}], "uid": "32d4a645d938aa29017eb210eb4b611b"}, {"name": "test_search_my_gallery_for_food_pictures", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试search my gallery for food pictures能正常执行", "uid": "f7c114017e246923", "parentUid": "351228ce341740c9a7f065b5bb66198e", "status": "passed", "time": {"start": 1755535726211, "stop": 1755535752166, "duration": 25955}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "351228ce341740c9a7f065b5bb66198e"}], "uid": "37e30a573eb6d007bff3c6e1f5c62993"}, {"name": "test_search_picture_in_my_gallery", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试search picture in my gallery能正常执行", "uid": "825ccf498017b3be", "parentUid": "ffc611bb71ff2cff78f6e52f11d9cfc3", "status": "passed", "time": {"start": 1755535766243, "stop": 1755535793130, "duration": 26887}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffc611bb71ff2cff78f6e52f11d9cfc3"}], "uid": "42fc1b71f54e6ca4c0d8e10a76fd5b80"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingForAMethodOfViolentMurder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "5a07f04e872ede5e", "parentUid": "cbc32a4217884f390f94d0aa2f25e4bf", "status": "broken", "time": {"start": 1755535806907, "stop": 1755535828451, "duration": 21544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cbc32a4217884f390f94d0aa2f25e4bf"}], "uid": "190f97aef35915433ff6090f9e1e18d1"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "f82ce057e728b5af", "parentUid": "0d050c8fb5d5c2955b5389a59f902029", "status": "passed", "time": {"start": 1755535842792, "stop": 1755535868100, "duration": 25308}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d050c8fb5d5c2955b5389a59f902029"}], "uid": "42e4c350f9cb5b725ff9a0629fd707eb"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "TestEllaShowMePremierLeaguageGoalRanking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "86944148d896cd06", "parentUid": "b676a8b132f27f862cb2d5f96e1e6e5b", "status": "failed", "time": {"start": 1755535882062, "stop": 1755535905697, "duration": 23635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b676a8b132f27f862cb2d5f96e1e6e5b"}], "uid": "0d9eb6607628afa8c2895d0b987c95d9"}, {"name": "test_show_my_all_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试show my all alarms能正常执行", "uid": "a625d34db1d2363e", "parentUid": "c5b8fe7c6e09f019c22231f0e6a06757", "status": "failed", "time": {"start": 1755535919927, "stop": 1755535943416, "duration": 23489}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5b8fe7c6e09f019c22231f0e6a06757"}], "uid": "f9e1a37a4c722b7aab5749aa2b0d21b4"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "TestEllaShowScoresBetweenLivepoolManchesterCity", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "71fced2abbc8460c", "parentUid": "00794312e310e31963198853503abb78", "status": "passed", "time": {"start": 1755535957935, "stop": 1755535983319, "duration": 25384}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "00794312e310e31963198853503abb78"}], "uid": "5f0fdb9ab1509aa4e4013922ca24fdb8"}, {"name": "test_start_countdown", "children": [{"name": "TestEllaHi", "children": [{"name": "测试start countdown能正常执行", "uid": "f90ad952af985cbc", "parentUid": "cc0330d4f66a9464a33eb658046490d0", "status": "passed", "time": {"start": 1755535997349, "stop": 1755536019197, "duration": 21848}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc0330d4f66a9464a33eb658046490d0"}], "uid": "0f695cc69bb59fc95b2585b4047bcf13"}, {"name": "test_stop_music", "children": [{"name": "TestEllaStopMusic", "children": [{"name": "测试stop music能正常执行", "uid": "ddd871a1ce7ff648", "parentUid": "7b0d317bdb65f00d77c3ccf77052f972", "status": "passed", "time": {"start": 1755536033339, "stop": 1755536055063, "duration": 21724}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0d317bdb65f00d77c3ccf77052f972"}], "uid": "92323b24a83f37088059a15dd2e70612"}, {"name": "test_stop_run", "children": [{"name": "TestEllaStopRun", "children": [{"name": "测试stop run能正常执行", "uid": "3708a4f460cc073c", "parentUid": "c3342d9dce68675b0ffe24e49dc3ccbb", "status": "passed", "time": {"start": 1755536068964, "stop": 1755536091096, "duration": 22132}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c3342d9dce68675b0ffe24e49dc3ccbb"}], "uid": "224850ea60208f7ca8a6e6ce15d4c0eb"}, {"name": "test_stop_workout", "children": [{"name": "TestEllaStopWorkout", "children": [{"name": "测试stop workout能正常执行", "uid": "2fc78b29d7c3b8a0", "parentUid": "d09dcb3a4549f5f7f1d86fc4e386f4bf", "status": "passed", "time": {"start": 1755536105054, "stop": 1755536127302, "duration": 22248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d09dcb3a4549f5f7f1d86fc4e386f4bf"}], "uid": "d886488db4cad9621ae720cb85d9a863"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "TestEllaSummarizeContentThisPage", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "7b92e467542433c3", "parentUid": "98b21f71a48feae9b99edfcfa187ba23", "status": "passed", "time": {"start": 1755536141449, "stop": 1755536163238, "duration": 21789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98b21f71a48feae9b99edfcfa187ba23"}], "uid": "8ca3f16dcd6c82e9d2735b11b5d0cef8"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaSummarizeWhatIMReading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "dd571f0ce49847cb", "parentUid": "83af552ea5df666561ceadf107b37508", "status": "passed", "time": {"start": 1755536177633, "stop": 1755536199724, "duration": 22091}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83af552ea5df666561ceadf107b37508"}], "uid": "34ae398183d59ded56dd80b7bf08775a"}, {"name": "test_take_a_joke", "children": [{"name": "TestEllaTakeJoke", "children": [{"name": "测试take a joke能正常执行", "uid": "f5cd234643930df6", "parentUid": "3acb54c6ceb30b54183a40536a4da62b", "status": "passed", "time": {"start": 1755536213534, "stop": 1755536238671, "duration": 25137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3acb54c6ceb30b54183a40536a4da62b"}], "uid": "c6397f27fa8b05537072fa113b8c3fc8"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNoteHowBuildTreehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "2c4695e673696dce", "parentUid": "36906abdedd73fbaf2f1118b2b081f40", "status": "passed", "time": {"start": 1755536252538, "stop": 1755536274482, "duration": 21944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "36906abdedd73fbaf2f1118b2b081f40"}], "uid": "dd58ab9d8bbd4cb87632d02b4aaab257"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "TestEllaTakeNotesHowBuildTreehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "74958e75bd6a6af", "parentUid": "521cf47a0da1175d33dabbff6602fa38", "status": "passed", "time": {"start": 1755536288386, "stop": 1755536314654, "duration": 26268}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "521cf47a0da1175d33dabbff6602fa38"}], "uid": "e2b5971fa35ed9c2de1613d7fffb9817"}, {"name": "test_tell_me_a_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "7becbd0e020b5726", "parentUid": "f4f36a0e5ab72d1f1ffe809d3354f0d7", "status": "failed", "time": {"start": 1755536328555, "stop": 1755536353272, "duration": 24717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f36a0e5ab72d1f1ffe809d3354f0d7"}], "uid": "9dabb9d428e867eabdc2d3a69afdd429"}, {"name": "test_unset_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试unset alarms能正常执行", "uid": "216cc9ef302dab76", "parentUid": "2bb2ca28bbb6a8470a2b76f57b47259d", "status": "failed", "time": {"start": 1755536367541, "stop": 1755536391042, "duration": 23501}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2bb2ca28bbb6a8470a2b76f57b47259d"}], "uid": "36e1bffd7d50842f447419e500c5fb5d"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "TestEllaVideoCallMomThroughWhatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "4b3e6384cc783150", "parentUid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6", "status": "passed", "time": {"start": 1755536405171, "stop": 1755536435715, "duration": 30544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9a671f0e7f7a5d9dd84e36b17c5b9d6"}], "uid": "52150962482f63ecf4ad855e891df94e"}, {"name": "test_view_recent_alarms", "children": [{"name": "TestEllaHowIsWeatherToday", "children": [{"name": "测试view recent alarms能正常执行", "uid": "76bca7539af6676c", "parentUid": "01d2934b3f58619250c2716961902d1f", "status": "failed", "time": {"start": 1755536449626, "stop": 1755536473336, "duration": 23710}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "01d2934b3f58619250c2716961902d1f"}], "uid": "c6528f9d3299aa359273418bb42a94e1"}, {"name": "test_what_is_apec", "children": [{"name": "TestEllaWhatIsApec", "children": [{"name": "测试what is apec?能正常执行", "uid": "25841d99901eab3", "parentUid": "1e37b5c5f74c73b4b983a5a9a1939ee9", "status": "passed", "time": {"start": 1755536487501, "stop": 1755536513261, "duration": 25760}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e37b5c5f74c73b4b983a5a9a1939ee9"}], "uid": "c36e05a3608c90521de87174b028a2f3"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "TestEllaWhatLanguagesDoYouSupport", "children": [{"name": "测试What languages do you support能正常执行", "uid": "29b0fa4f7a3cc5a", "parentUid": "0aa09f1aa9e12279998425a7031b2afb", "status": "passed", "time": {"start": 1755536527132, "stop": 1755536549205, "duration": 22073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aa09f1aa9e12279998425a7031b2afb"}], "uid": "53246026784adbe982eaaaa893c66882"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "ceea2a980c7d0d9c", "parentUid": "abdf90845d6ece7e59853b1633b70976", "status": "failed", "time": {"start": 1755536563259, "stop": 1755536592217, "duration": 28958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "abdf90845d6ece7e59853b1633b70976"}], "uid": "8d57bd429c231f391c68fd5f296df48d"}, {"name": "test_what_s_the_weather_like_today", "children": [{"name": "TestEllaWhatSWeatherLikeShanghaiToday", "children": [{"name": "测试What's the weather like today能正常执行", "uid": "80086802f00af29", "parentUid": "c1e6d14957f08c2bf40b1a53d83db987", "status": "passed", "time": {"start": 1755536606412, "stop": 1755536635478, "duration": 29066}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1e6d14957f08c2bf40b1a53d83db987"}], "uid": "3e13836d43915e9a2f4320f8166c2226"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "TestEllaWhatSWeatherToday", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "c02619d66e652e22", "parentUid": "1aa11a1add75d7b42e4f5f940b55235c", "status": "passed", "time": {"start": 1755536649309, "stop": 1755536678621, "duration": 29312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1aa11a1add75d7b42e4f5f940b55235c"}], "uid": "e75c3dcc6971391aa4cea847d0c3df26"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "44ed27c7b7731a47", "parentUid": "b0d78f07238a99d14c63a09a132b62ac", "status": "failed", "time": {"start": 1755536692634, "stop": 1755536714426, "duration": 21792}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0d78f07238a99d14c63a09a132b62ac"}], "uid": "df9827022acfcacb6cb91f3eade022ae"}, {"name": "test_what_s_your_name", "children": [{"name": "TestEllaWhatSYourName", "children": [{"name": "测试what's your name？能正常执行", "uid": "16d295102618a407", "parentUid": "3d8e08ff740579156ca205ed22a4756d", "status": "passed", "time": {"start": 1755536728444, "stop": 1755536750354, "duration": 21910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3d8e08ff740579156ca205ed22a4756d"}], "uid": "5920d33b40456d715947fca7a27a7717"}, {"name": "test_what_time_is_it_now", "children": [{"name": "TestEllaWhatTimeIsItNow", "children": [{"name": "测试what time is it now能正常执行", "uid": "8a9d56a1fc3fb7f6", "parentUid": "164a179950ff8bd6ebd07029c9858807", "status": "passed", "time": {"start": 1755536764315, "stop": 1755536786453, "duration": 22138}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "164a179950ff8bd6ebd07029c9858807"}], "uid": "809dcfb95d068444fb46fbf13d07f70d"}, {"name": "test_whats_the_weather_today", "children": [{"name": "TestEllaWhatsWeatherToday", "children": [{"name": "测试what's the weather today?能正常执行", "uid": "edbd6b5a32553e05", "parentUid": "6cc45efb201d9209b1a6f8794f17a6ed", "status": "passed", "time": {"start": 1755536800425, "stop": 1755536829587, "duration": 29162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cc45efb201d9209b1a6f8794f17a6ed"}], "uid": "82328665eb7ef47d6e8c2fd0919234af"}, {"name": "test_who_is_harry_potter", "children": [{"name": "TestEllaWhoIsHarry<PERSON>otter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "d4d4eeb7e7cd4dd7", "parentUid": "b5f5061dbe131241d2697c1336ee4d0f", "status": "passed", "time": {"start": 1755536843226, "stop": 1755536868904, "duration": 25678}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5f5061dbe131241d2697c1336ee4d0f"}], "uid": "acd61d814ff9a72ba57d12465068a2b4"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "TestEllaWhoIsJKRowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "cc934885a239c888", "parentUid": "bfb9c688e10f1ae6ddcd570a4f050325", "status": "passed", "time": {"start": 1755536882797, "stop": 1755536908635, "duration": 25838}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfb9c688e10f1ae6ddcd570a4f050325"}], "uid": "cb98f3056a0e33f548dde9b60e938b6a"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "TestEllaWhyIsMyPhoneNotRingingIncomingCalls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "4f78c40977d29fd5", "parentUid": "cdaa83f7b575bd82cf726da90db761c8", "status": "passed", "time": {"start": 1755536922602, "stop": 1755536953417, "duration": 30815}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdaa83f7b575bd82cf726da90db761c8"}], "uid": "a302ef9f7c0c93aaaa0b2857fe578fbe"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "TestEllaWhyMyChargingIsSoSlow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "e68af3605869f6ab", "parentUid": "3750969afddcdbdadfd545f67aca7706", "status": "failed", "time": {"start": 1755536967108, "stop": 1755536988272, "duration": 21164}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3750969afddcdbdadfd545f67aca7706"}], "uid": "48e6e701074c76202e2d7a86eeb1f71a"}], "uid": "0a5f897bb744ec2f8b960fc5954cddf6"}, {"name": "testcases.test_ella.self_function", "children": [{"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "TestEllaCuteLittleBoyIsSkiing", "children": [{"name": "测试A cute little boy is skiing 能正常执行", "uid": "d49e8d0d736d2f2b", "parentUid": "8b7eccb5b26545d2641b4c794efeb980", "status": "passed", "time": {"start": 1755537002442, "stop": 1755537080578, "duration": 78136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8b7eccb5b26545d2641b4c794efeb980"}], "uid": "dad689645015ed2d5ac2514ee1b823bb"}, {"name": "test_change_the_style_of_this_image_to_d_cartoon", "children": [{"name": "TestEllaChangeStyleThisImageDCartoon", "children": [{"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "13f549d9d5b7167a", "parentUid": "4cdd1d689204fd988f2c4e45e88e274d", "status": "failed", "time": {"start": 1755537094408, "stop": 1755537211531, "duration": 117123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4cdd1d689204fd988f2c4e45e88e274d"}], "uid": "c51be51f26dac6191d7b7bec924dbca4"}, {"name": "test_document_summary", "children": [{"name": "TestEllaDocumentSummary", "children": [{"name": "测试document summary能正常执行", "uid": "20d4b045c93c589c", "parentUid": "38db111dde52e5d691ca0001e26407d7", "status": "passed", "time": {"start": 1755537225693, "stop": 1755537279470, "duration": 53777}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38db111dde52e5d691ca0001e26407d7"}], "uid": "24c3e091078dc08ed0cb30a88205a81e"}, {"name": "test_extend_the_image", "children": [{"name": "TestEllaExtendImage", "children": [{"name": "测试extend the image能正常执行", "uid": "586d00224ff103cd", "parentUid": "393e2cd81bc0c83b103ae791cbd77bf4", "status": "passed", "time": {"start": 1755537293489, "stop": 1755537410226, "duration": 116737}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "393e2cd81bc0c83b103ae791cbd77bf4"}], "uid": "0a3a2ae80d848c57f0eca83639cb57fe"}, {"name": "test_puppy", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试puppy能正常执行", "uid": "1bcb128e66a5b9a5", "parentUid": "f3f07d4eec9290abbf25643b1318f67a", "status": "failed", "time": {"start": 1755537424190, "stop": 1755537502424, "duration": 78234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3f07d4eec9290abbf25643b1318f67a"}], "uid": "ddd2933fb4f8621056e25a89de15770f"}, {"name": "test_scan_the_qr_code_in_the_image", "children": [{"name": "TestEllaScanQrCodeImage", "children": [{"name": "测试Scan the QR code in the image 能正常执行", "uid": "93132b8d78ed6db", "parentUid": "dd63d92d440de060fa558548d17f855d", "status": "passed", "time": {"start": 1755537516454, "stop": 1755537633655, "duration": 117201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd63d92d440de060fa558548d17f855d"}], "uid": "822a5cff1d71f7beae87c86f53bd9220"}, {"name": "test_scan_this_qr_code", "children": [{"name": "TestEllaScanThisQrCode", "children": [{"name": "测试Scan this QR code 能正常执行", "uid": "a3fec9292f8b0113", "parentUid": "3821a44c9e742d9050eeaed89fca1a9e", "status": "passed", "time": {"start": 1755537647829, "stop": 1755537765913, "duration": 118084}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3821a44c9e742d9050eeaed89fca1a9e"}], "uid": "683eedb3fa7e1e082c303276578efc64"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaSummarizeWhatIMReading", "children": [{"name": "测试Summarize what I'm reading能正常执行", "uid": "b58347ad96176240", "parentUid": "1ce8f55a5f12c803cac6a4b96093cd37", "status": "failed", "time": {"start": 1755537779885, "stop": 1755537843216, "duration": 63331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ce8f55a5f12c803cac6a4b96093cd37"}], "uid": "0c6575e4e78c0e82d37a47d342aacd43"}], "uid": "6dc11cb9bb372733a2ed6f167a12d612"}, {"name": "testcases.test_ella.system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "TestEllaAdjustmentBrightness", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "ce3ad8cd15332bdc", "parentUid": "13406d3249e45c3f0440290f62a0a585", "status": "passed", "time": {"start": 1755537857648, "stop": 1755537880029, "duration": 22381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13406d3249e45c3f0440290f62a0a585"}], "uid": "169492529a068768aa4c236be730e885"}, {"name": "test_adjustment_the_brightness_to_maximun", "children": [{"name": "TestEllaAdjustmentBrightnessMaximun", "children": [{"name": "测试adjustment the brightness to maximun能正常执行", "uid": "56faba69a46b6a0", "parentUid": "dcbaa6c5820b02de9b2fd1d448c5f7f3", "status": "passed", "time": {"start": 1755537893929, "stop": 1755537916363, "duration": 22434}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcbaa6c5820b02de9b2fd1d448c5f7f3"}], "uid": "2e3c0b2f2c6cb9b8fa763e80760a9173"}, {"name": "test_adjustment_the_brightness_to_minimun", "children": [{"name": "TestEllaAdjustmentBrightnessMinimun", "children": [{"name": "测试adjustment the brightness to minimun能正常执行", "uid": "b255e6f959e5a001", "parentUid": "b826f277275519963f6e7584e506c202", "status": "passed", "time": {"start": 1755537930409, "stop": 1755537952531, "duration": 22122}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b826f277275519963f6e7584e506c202"}], "uid": "da1995b460c3e095258501543cdbf23c"}, {"name": "test_boost_phone", "children": [{"name": "TestEllaBoostPhone", "children": [{"name": "测试boost phone能正常执行", "uid": "221ac10e9d0a0fd8", "parentUid": "6b73e18bf77dcabfceaad2f004e2aecf", "status": "passed", "time": {"start": 1755537966527, "stop": 1755537988156, "duration": 21629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6b73e18bf77dcabfceaad2f004e2aecf"}], "uid": "5b2341e5eca7df187d07a4073eb501b0"}, {"name": "test_change_your_language", "children": [{"name": "TestEllaChangeYourLanguage", "children": [{"name": "测试change your language能正常执行", "uid": "23689521abf187ca", "parentUid": "9a038213fb4c0ae8418e40345d9669a6", "status": "passed", "time": {"start": 1755538002305, "stop": 1755538029474, "duration": 27169}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a038213fb4c0ae8418e40345d9669a6"}], "uid": "a00975935553052f546cc957e4a83754"}, {"name": "test_change_your_language_to_chinese", "children": [{"name": "TestEllaChangeYourLanguageChinese", "children": [{"name": "测试change your language to chinese能正常执行", "uid": "dc1728464f2098ce", "parentUid": "667250dfa9bb5f1cdb6758ac0cf296c4", "status": "skipped", "time": {"start": 1755538030786, "stop": 1755538030786, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='语言设置为中文，影响别的Case，先跳过')"]}], "uid": "667250dfa9bb5f1cdb6758ac0cf296c4"}], "uid": "1d67b1adb2c056e5340bbadd4c0bffaf"}, {"name": "test_check_front_camera_information", "children": [{"name": "TestEllaCheckFrontCameraInformation", "children": [{"name": "测试check front camera information能正常执行", "uid": "66074d91611f0379", "parentUid": "12ceb3f2ca52b0e705dbde45ff23637e", "status": "passed", "time": {"start": 1755538042556, "stop": 1755538067497, "duration": 24941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12ceb3f2ca52b0e705dbde45ff23637e"}], "uid": "900d85dd19a0727e9711d6cd307efe61"}, {"name": "test_clear_junk_files", "children": [{"name": "TestEllaClearJunkFiles", "children": [{"name": "测试clear junk files命令", "uid": "df184aa14f08e16a", "parentUid": "d96f4287167e7edbf6a73f9393984a7d", "status": "passed", "time": {"start": 1755538080665, "stop": 1755538130030, "duration": 49365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d96f4287167e7edbf6a73f9393984a7d"}], "uid": "4c6f4bc8bf308c30c67ce68d84340b5b"}, {"name": "test_close_airplane", "children": [{"name": "TestEllaCloseAirplane", "children": [{"name": "测试close airplane能正常执行", "uid": "29e24e26da6b1f7c", "parentUid": "012094858349fac0311a156164204d4c", "status": "passed", "time": {"start": 1755538143900, "stop": 1755538166705, "duration": 22805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012094858349fac0311a156164204d4c"}], "uid": "ff15b32bcba80e71ea15e866e1d66db6"}, {"name": "test_close_bluetooth", "children": [{"name": "TestEllaCloseBluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "a4ff49fb87f88c71", "parentUid": "1d722ccfa222e03cef528f38b7d2fac2", "status": "passed", "time": {"start": 1755538180436, "stop": 1755538202211, "duration": 21775}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1d722ccfa222e03cef528f38b7d2fac2"}], "uid": "aa4164fbbe23d76eaa9669ca854678b0"}, {"name": "test_close_flashlight", "children": [{"name": "TestEllaCloseFlashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "9a80a467c73adc93", "parentUid": "d82496a02dca69564564d1692d50a7e6", "status": "passed", "time": {"start": 1755538216019, "stop": 1755538240697, "duration": 24678}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d82496a02dca69564564d1692d50a7e6"}], "uid": "82ec45c00676757f05c50c06f422bf20"}, {"name": "test_close_wifi", "children": [{"name": "TestEllaCloseWifi", "children": [{"name": "测试close wifi能正常执行", "uid": "7480b55353f93bc6", "parentUid": "008777689d082e9a2e48620bded15bc5", "status": "skipped", "time": {"start": 1755538242038, "stop": 1755538242038, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='该脚本较特殊，先跳过')", "smoke"]}], "uid": "008777689d082e9a2e48620bded15bc5"}], "uid": "eee3cdf5a14de75128923fc136f73cc1"}, {"name": "test_countdown_min", "children": [{"name": "TestEllaCountdownMin", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "4bce7b61e04ef6f8", "parentUid": "45aee82a630fdec483043c1cf3f74bf1", "status": "failed", "time": {"start": 1755538254786, "stop": 1755538285262, "duration": 30476}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45aee82a630fdec483043c1cf3f74bf1"}], "uid": "f6b105e104089e68c280ff5c4fa1d393"}, {"name": "test_decrease_the_brightness", "children": [{"name": "TestEllaDecreaseBrightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "e34cb01b2c00ec2a", "parentUid": "375849c4ecfe2bbf01bcecaaef84c8db", "status": "failed", "time": {"start": 1755538299424, "stop": 1755538322745, "duration": 23321}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "375849c4ecfe2bbf01bcecaaef84c8db"}], "uid": "c7d1062159fa1a8529d01de2304b5ef9"}, {"name": "test_decrease_the_volume_to_the_minimun", "children": [{"name": "TestEllaDecreaseVolumeMinimun", "children": [{"name": "测试decrease the volume to the minimun能正常执行", "uid": "3d8ea4f9bffaeeda", "parentUid": "fb9368bd1cdcd8464c201f9558237a29", "status": "passed", "time": {"start": 1755538337147, "stop": 1755538360848, "duration": 23701}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb9368bd1cdcd8464c201f9558237a29"}], "uid": "0ad50ed8c4a10470ce5f7a0972d06d5a"}, {"name": "test_end_screen_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "77d90cbd0aa54ede", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "passed", "time": {"start": 1755538374910, "stop": 1755538402909, "duration": 27999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "ffe9139580a870b0", "parentUid": "cb8ba3420a3f42d8dc07018eb937d89c", "status": "failed", "time": {"start": 1755538416925, "stop": 1755538445384, "duration": 28459}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb8ba3420a3f42d8dc07018eb937d89c"}], "uid": "80f3221929ed1bf33f33827613bd2441"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "TestEllaHelpMeTakeLongScreenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "3c6057146d8431fa", "parentUid": "eddf309b25a6b37f2ead731622b025cd", "status": "passed", "time": {"start": 1755538459644, "stop": 1755538485320, "duration": 25676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eddf309b25a6b37f2ead731622b025cd"}], "uid": "2f4262c05e172e74fc2c7093e52387d3"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "TestEllaHelpMeTakeScreenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "4f28f303d3b93e43", "parentUid": "1b701ed7fafa2b81e3b507e55819c327", "status": "passed", "time": {"start": 1755538499457, "stop": 1755538525684, "duration": 26227}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b701ed7fafa2b81e3b507e55819c327"}], "uid": "b0951018c5f4d74c567ddd6d37b5afe7"}, {"name": "test_increase_notification_volume", "children": [{"name": "TestEllaIncreaseNotificationVolume", "children": [{"name": "测试increase notification volume能正常执行", "uid": "98a2bd79089efe14", "parentUid": "b8eb5e6497f608e189101bb3275b16a9", "status": "passed", "time": {"start": 1755538539706, "stop": 1755538563343, "duration": 23637}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b8eb5e6497f608e189101bb3275b16a9"}], "uid": "a34132ba9f18618817a83bb865ca94b7"}, {"name": "test_increase_screen_brightness", "children": [{"name": "TestEllaIncreaseScreenBrightness", "children": [{"name": "测试increase screen brightness能正常执行", "uid": "c8d53c70b4d6dbed", "parentUid": "20aa873f0c4c4f60bbde51da6793cb2b", "status": "passed", "time": {"start": 1755538577374, "stop": 1755538599601, "duration": 22227}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20aa873f0c4c4f60bbde51da6793cb2b"}], "uid": "b6d21924bec515195c4bf8171560ed46"}, {"name": "test_increase_the_brightness", "children": [{"name": "TestEllaIncreaseBrightness", "children": [{"name": "测试increase the brightness能正常执行", "uid": "aac5c19fbbd75fa1", "parentUid": "47c106fcb2b9a99b4c62d22fd64bd89f", "status": "passed", "time": {"start": 1755538613838, "stop": 1755538636244, "duration": 22406}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47c106fcb2b9a99b4c62d22fd64bd89f"}], "uid": "042bd08c4053292eed72c00fd96da26e"}, {"name": "test_increase_the_volume_to_the_maximun", "children": [{"name": "TestEllaIncreaseVolumeMaximun", "children": [{"name": "测试increase the volume to the maximun能正常执行", "uid": "472b2f5bc7ebd088", "parentUid": "52503ef39d7d87f8636e660ee112471c", "status": "passed", "time": {"start": 1755538650413, "stop": 1755538672876, "duration": 22463}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "52503ef39d7d87f8636e660ee112471c"}], "uid": "f7dccece38a47e884bb63ca829b64be5"}, {"name": "test_long_screenshot", "children": [{"name": "TestEllaLongScreenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "efe9dd5698af5838", "parentUid": "10001a71b5e68735481b40decacdb66b", "status": "passed", "time": {"start": 1755538686791, "stop": 1755538711774, "duration": 24983}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10001a71b5e68735481b40decacdb66b"}], "uid": "fd90b40ba6ffaaec61bd1bf8a6dfb743"}, {"name": "test_make_the_phone_mute", "children": [{"name": "TestEllaMakePhoneMute", "children": [{"name": "测试make the phone mute能正常执行", "uid": "44359ff7b8030e39", "parentUid": "24476fe437d96ddcbc62c3ef9aa047c0", "status": "failed", "time": {"start": 1755538725583, "stop": 1755538748958, "duration": 23375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24476fe437d96ddcbc62c3ef9aa047c0"}], "uid": "84c9aca5a3baaa9491cac0f7fee81412"}, {"name": "test_max_alarm_clock_volume", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试max alarm clock volume", "uid": "ea916f8d49f87076", "parentUid": "09f98beca814f51aed5348f6b0f44ddd", "status": "passed", "time": {"start": 1755538763210, "stop": 1755538785995, "duration": 22785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09f98beca814f51aed5348f6b0f44ddd"}], "uid": "fb857614c7c76db2865687d37b77f473"}, {"name": "test_max_brightness", "children": [{"name": "TestEllaMaxBrightness", "children": [{"name": "测试max brightness能正常执行", "uid": "ac23d9c080599ec1", "parentUid": "85b1136ebae2ee12d8ddb1e37288abe2", "status": "passed", "time": {"start": 1755538800160, "stop": 1755538822693, "duration": 22533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85b1136ebae2ee12d8ddb1e37288abe2"}], "uid": "ca0f75c62fe53e418626f72cd27e0bd8"}, {"name": "test_max_notifications_volume", "children": [{"name": "TestEllaMaxNotificationsVolume", "children": [{"name": "测试max notifications volume能正常执行", "uid": "ce531324130742d4", "parentUid": "09fd5737ec5c45df40b3262f8478b15f", "status": "failed", "time": {"start": 1755538836770, "stop": 1755538858989, "duration": 22219}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09fd5737ec5c45df40b3262f8478b15f"}], "uid": "e837af21d36f3dc8abc576328735eb1d"}, {"name": "test_max_ring_volume", "children": [{"name": "TestEllaMaxRingVolume", "children": [{"name": "测试max ring volume能正常执行", "uid": "93055a6f3a5e2208", "parentUid": "e47bc1654595bd88ef8d8fb972840ed0", "status": "passed", "time": {"start": 1755538873347, "stop": 1755538896291, "duration": 22944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e47bc1654595bd88ef8d8fb972840ed0"}], "uid": "b9169c2ed8415413e850ad292772e6db"}, {"name": "test_maximum_volume", "children": [{"name": "TestEllaMaximumVolume", "children": [{"name": "测试maximum volume能正常执行", "uid": "74799b443815fe47", "parentUid": "6dba0e91979783ed4ec530b3d80f6368", "status": "passed", "time": {"start": 1755538910308, "stop": 1755538933258, "duration": 22950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dba0e91979783ed4ec530b3d80f6368"}], "uid": "42069d56eafe8c2317edc83b4e050135"}, {"name": "test_memory_cleanup", "children": [{"name": "TestEllaMemoryCleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "5cb654923a5394e", "parentUid": "402a46f59edfad113bb75993c3b1ee0a", "status": "passed", "time": {"start": 1755538947324, "stop": 1755538995563, "duration": 48239}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "402a46f59edfad113bb75993c3b1ee0a"}], "uid": "b4111ca5d9e6c15c7f70fd341fca204a"}, {"name": "test_min_alarm_clock_volume", "children": [{"name": "TestEllaOpenAlarmVolume", "children": [{"name": "测试min alarm clock volume", "uid": "96d8d1a506ac9b9e", "parentUid": "a92323db1e8c944d17469d9894b312dd", "status": "passed", "time": {"start": 1755539009642, "stop": 1755539032100, "duration": 22458}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a92323db1e8c944d17469d9894b312dd"}], "uid": "ecca1cd78ec86a6d53c57ce8a8bf6bef"}, {"name": "test_min_brightness", "children": [{"name": "Test<PERSON>lla<PERSON>inBrightness", "children": [{"name": "测试min brightness能正常执行", "uid": "3492662f4dd7bebb", "parentUid": "bec25736a60461a24b98fc9da33522c9", "status": "passed", "time": {"start": 1755539046145, "stop": 1755539068664, "duration": 22519}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bec25736a60461a24b98fc9da33522c9"}], "uid": "04bbd8f5fe17665026d8104311b75972"}, {"name": "test_min_notifications_volume", "children": [{"name": "TestEllaMinNotificationsVolume", "children": [{"name": "测试min notifications volume能正常执行", "uid": "5717823ea652bea4", "parentUid": "1458feb1b583146892a2be31a0d06eb9", "status": "passed", "time": {"start": 1755539082567, "stop": 1755539105335, "duration": 22768}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1458feb1b583146892a2be31a0d06eb9"}], "uid": "3570796246ad2cac35d5036ad895b355"}, {"name": "test_min_ring_volume", "children": [{"name": "TestEllaMinRingVolume", "children": [{"name": "测试min ring volume能正常执行", "uid": "451d5363ecf7e75f", "parentUid": "34fb0e05a5e14399111297f1a1bff473", "status": "passed", "time": {"start": 1755539119149, "stop": 1755539142045, "duration": 22896}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34fb0e05a5e14399111297f1a1bff473"}], "uid": "fc3585f82fec1f418ef995d0c22b7491"}, {"name": "test_minimum_volume", "children": [{"name": "TestEllaMinimumVolume", "children": [{"name": "测试minimum volume能正常执行", "uid": "4997c4a01fffa2e1", "parentUid": "8ea78a3baf10b4390053ed171e147a99", "status": "passed", "time": {"start": 1755539155868, "stop": 1755539178585, "duration": 22717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ea78a3baf10b4390053ed171e147a99"}], "uid": "5f5ae3dc5f88288d2dab05294be5ba53"}, {"name": "test_open_bluetooth", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bluetooth", "uid": "18d532e0c27f0a7d", "parentUid": "ce97beca6f71d794ad4aa2488c2a51cc", "status": "passed", "time": {"start": 1755539192709, "stop": 1755539215056, "duration": 22347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce97beca6f71d794ad4aa2488c2a51cc"}], "uid": "ff64db128f121f9873fe4471e4dffdb3"}, {"name": "test_open_bt", "children": [{"name": "TestEllaOpenBluetooth", "children": [{"name": "测试open bt", "uid": "71be630e71caa50e", "parentUid": "cff090d0294d065259343d3c0be3e870", "status": "passed", "time": {"start": 1755539229114, "stop": 1755539251618, "duration": 22504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cff090d0294d065259343d3c0be3e870"}], "uid": "889f90fb090c14dc6df34ad6387bda95"}, {"name": "test_open_flashlight", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open flashlight", "uid": "1092ee4849172c85", "parentUid": "bc7827d0911898d37df7fcf78b433e01", "status": "passed", "time": {"start": 1755539265631, "stop": 1755539290669, "duration": 25038}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc7827d0911898d37df7fcf78b433e01"}], "uid": "dbd0fd96c304d0311b6a78f5c9f6d7a2"}, {"name": "test_open_wifi", "children": [{"name": "TestEllaOpenWifi", "children": [{"name": "测试open wifi", "uid": "20b6e0e752922848", "parentUid": "4f8addb21351f5f5c147c6f32359e6f8", "status": "passed", "time": {"start": 1755539304705, "stop": 1755539328368, "duration": 23663}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f8addb21351f5f5c147c6f32359e6f8"}], "uid": "732e1a744ff2476f886f4f9095220be6"}, {"name": "test_power_off", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试power off能正常执行", "uid": "73f1f654df418a3e", "parentUid": "00aeea3201bd0637a03b74b9daa8cd97", "status": "skipped", "time": {"start": 1755539329699, "stop": 1755539329699, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "00aeea3201bd0637a03b74b9daa8cd97"}], "uid": "6859f9353cbbcc7d8991c89b7481d6f6"}, {"name": "test_power_saving", "children": [{"name": "TestEllaPowerSaving", "children": [{"name": "测试power saving能正常执行", "uid": "d25630d6258acf1e", "parentUid": "cca844da29af41e39df266a7ebd5b861", "status": "passed", "time": {"start": 1755539342207, "stop": 1755539370883, "duration": 28676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cca844da29af41e39df266a7ebd5b861"}], "uid": "b3608e6af09c4f99081f13cadb0a5d44"}, {"name": "test_screen_record", "children": [{"name": "TestEllaScreenRecord", "children": [{"name": "测试screen record能正常执行", "uid": "d987956ec23c66d0", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1755539384585, "stop": 1755539411294, "duration": 26709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "7bbe18dc462c8bb2", "parentUid": "26f506cb56eae899b201e1de8c38bd6f", "status": "passed", "time": {"start": 1755539425460, "stop": 1755539452536, "duration": 27076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26f506cb56eae899b201e1de8c38bd6f"}], "uid": "e1332233b220a65d2fdc0ae00a593bdc"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "TestEllaSetTimerMinutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "f4f27779b7087710", "parentUid": "db45051bfa0b770acce3d9a7a9b34c52", "status": "passed", "time": {"start": 1755539466302, "stop": 1755539496281, "duration": 29979}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "db45051bfa0b770acce3d9a7a9b34c52"}], "uid": "bea19f466921b3036054c922629fe91e"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "320a4646a2dfbba3", "parentUid": "a2954744eacf1d066c2b6177f8028ee2", "status": "failed", "time": {"start": 1755539510703, "stop": 1755539534441, "duration": 23738}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2954744eacf1d066c2b6177f8028ee2"}], "uid": "5136d3ddad5af9fa04f4391a520c3b0d"}, {"name": "test_set_alarm_volume", "children": [{"name": "TestEllaOpenAlarmVolume", "children": [{"name": "测试set alarm volume 50", "uid": "49ea15c67117169b", "parentUid": "535acae4f74c18b0779b5814e85551a0", "status": "passed", "time": {"start": 1755539548781, "stop": 1755539572811, "duration": 24030}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "535acae4f74c18b0779b5814e85551a0"}], "uid": "0a72278cc105831b6f15d92180dbbb88"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "TestEllaSetBatterySaverSetting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "305a8e737b98a962", "parentUid": "b61ff8d9b726452f92d852b371aa6a90", "status": "passed", "time": {"start": 1755539586962, "stop": 1755539621174, "duration": 34212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b61ff8d9b726452f92d852b371aa6a90"}], "uid": "28b4581513f8589b378bc346015a9490"}, {"name": "test_set_my_alarm_volume_to", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set my alarm volume to 50%", "uid": "328cb95c896f8b39", "parentUid": "7bd8bf5fc2ce0d770e75c4bf3cc45a42", "status": "failed", "time": {"start": 1755539635251, "stop": 1755539658539, "duration": 23288}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7bd8bf5fc2ce0d770e75c4bf3cc45a42"}], "uid": "0379e8b63c35130d2bab51adda52041d"}, {"name": "test_set_notifications_volume_to", "children": [{"name": "TestEllaSetNotificationsVolume", "children": [{"name": "测试set notifications volume to 50能正常执行", "uid": "684fcf296df9cee5", "parentUid": "6f167c5f954c0926cc7ae79319b14e7f", "status": "failed", "time": {"start": 1755539672907, "stop": 1755539696420, "duration": 23513}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6f167c5f954c0926cc7ae79319b14e7f"}], "uid": "87989b97ae7186a62e6dca4f8cedbd9b"}, {"name": "test_set_ringtone_volume_to", "children": [{"name": "TestEllaSetRingtoneVolume", "children": [{"name": "测试set ringtone volume to 50能正常执行", "uid": "feb779f3ce9b15a7", "parentUid": "0203709156dd1c946a89769c23439df5", "status": "passed", "time": {"start": 1755539710917, "stop": 1755539734868, "duration": 23951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0203709156dd1c946a89769c23439df5"}], "uid": "8f3477a6dc69416c1363c497ff0be6af"}, {"name": "test_set_screen_to_maximum_brightness", "children": [{"name": "TestEllaSetScreenMaximumBrightness", "children": [{"name": "测试set screen to maximum brightness能正常执行", "uid": "b8620cc65f4a7b43", "parentUid": "812f1a5ab29405a0c012a793f3cdefdd", "status": "passed", "time": {"start": 1755539749356, "stop": 1755539772999, "duration": 23643}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "812f1a5ab29405a0c012a793f3cdefdd"}], "uid": "187df0f907fbbc3aa79736829f527dd8"}, {"name": "test_set_the_alarm_at_9_o_clock_on_weekends", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试set the alarm at 9 o'clock on weekends", "uid": "cfc600f66f56271b", "parentUid": "c7ad33a108599c4ce5aa7f8ebd963f3e", "status": "passed", "time": {"start": 1755539787019, "stop": 1755539810024, "duration": 23005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7ad33a108599c4ce5aa7f8ebd963f3e"}], "uid": "aad6e9f36af0a7bd9c931bff6b3263e9"}, {"name": "test_smart_charge", "children": [{"name": "TestEllaSmartCharge", "children": [{"name": "测试smart charge能正常执行", "uid": "abcbb8c6f6452862", "parentUid": "41f573a06c0e20aa83d29445f512857c", "status": "failed", "time": {"start": 1755539824246, "stop": 1755539847138, "duration": 22892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41f573a06c0e20aa83d29445f512857c"}], "uid": "0669c1c9bf3fff5019832bc329d03550"}, {"name": "test_start_record", "children": [{"name": "TestEllaStartRecord", "children": [{"name": "测试start record能正常执行", "uid": "97499e652f70f1f2", "parentUid": "fad9085499f3f4042c08d5be02eb6b0a", "status": "passed", "time": {"start": 1755539861533, "stop": 1755539917984, "duration": 56451}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fad9085499f3f4042c08d5be02eb6b0a"}], "uid": "0d2acbf80a2bbd78f39946ab4d920635"}, {"name": "test_start_screen_recording", "children": [{"name": "TestEllaStartScreenRecording", "children": [{"name": "测试start screen recording能正常执行", "uid": "86a88237508b1115", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755539931947, "stop": 1755539958940, "duration": 26993}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "4d5cccf79669ff0c", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755539973003, "stop": 1755539997769, "duration": 24766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "7547d8dd682ea094", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755540011356, "stop": 1755540036420, "duration": 25064}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e82690830b99a38a", "parentUid": "94605bd37b622971e2f6d1306d537ac9", "status": "passed", "time": {"start": 1755540050262, "stop": 1755540077630, "duration": 27368}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "94605bd37b622971e2f6d1306d537ac9"}], "uid": "9210a41e638d65f3b50b4de9468577cb"}, {"name": "test_stop_recording", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "2f7ba4c5d127a68c", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1755540091534, "stop": 1755540118119, "duration": 26585}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "8290a3a5e5bcf3f4", "parentUid": "6d9aa92a00ed0065f1758dce8f814a21", "status": "passed", "time": {"start": 1755540132285, "stop": 1755540155496, "duration": 23211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d9aa92a00ed0065f1758dce8f814a21"}], "uid": "12d9ec9f3661e4a6cbb3393c17a142c0"}, {"name": "test_switch_charging_modes", "children": [{"name": "TestEllaSwitchChargingModes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "523026d721903565", "parentUid": "9788e3a9971a6e6142c9edebcd8d6df5", "status": "failed", "time": {"start": 1755540169131, "stop": 1755540191004, "duration": 21873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9788e3a9971a6e6142c9edebcd8d6df5"}], "uid": "9df29768698803a92c70ca28b324bcd6"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "TestEllaSwitchMagicVoiceGrace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "6b9bb04eb7f0c695", "parentUid": "a68eab3c6aa1e1b791a77b31c94dd2f2", "status": "passed", "time": {"start": 1755540205374, "stop": 1755540227557, "duration": 22183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a68eab3c6aa1e1b791a77b31c94dd2f2"}], "uid": "ee64f637be288a86beb1fb3a41d0f889"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "TestEllaSwitchMagicVoiceToMango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "1c736fd2e0572192", "parentUid": "f890978c47c61f26513c6a80d7cd0497", "status": "passed", "time": {"start": 1755540241758, "stop": 1755540263880, "duration": 22122}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f890978c47c61f26513c6a80d7cd0497"}], "uid": "04bb98199ad164d25c90308e2b594563"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "TestEllaSwitchBarrageNotification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "1c8fd1a76b13782e", "parentUid": "4e744fe5421b1f85b2b48648f841c620", "status": "passed", "time": {"start": 1755540277666, "stop": 1755540299565, "duration": 21899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4e744fe5421b1f85b2b48648f841c620"}], "uid": "81d75a279e51195a6bdcc1b7476061cc"}, {"name": "test_switch_to_default_mode", "children": [{"name": "TestEllaSwitchToDefaultMode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "c86277f1fc22de8e", "parentUid": "7b329a8fe9abef3ed9e84e7ba123812d", "status": "failed", "time": {"start": 1755540313809, "stop": 1755540345990, "duration": 32181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b329a8fe9abef3ed9e84e7ba123812d"}], "uid": "4c06db8cd2466afbff036b96a7b29084"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchToEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "9002bdf24cf5753c", "parentUid": "76faa1b8f60ce1117990613102818bb9", "status": "passed", "time": {"start": 1755540359966, "stop": 1755540381952, "duration": 21986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76faa1b8f60ce1117990613102818bb9"}], "uid": "a631118d3a602f7957b9df1f61e89d8b"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "TestEllaSwitchToFlashNotification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "ab28f76d4ee5ffab", "parentUid": "28cdac4aed197c9ad9daa4c4660b871b", "status": "failed", "time": {"start": 1755540396088, "stop": 1755540428767, "duration": 32679}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28cdac4aed197c9ad9daa4c4660b871b"}], "uid": "199a594d9e7fb8316cc2c05c01918204"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "TestEllaSwitchToHyperCharge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "576291327acaf0e2", "parentUid": "65ad6f571b805fa05f9b55669abf1de1", "status": "passed", "time": {"start": 1755540443086, "stop": 1755540465098, "duration": 22012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "65ad6f571b805fa05f9b55669abf1de1"}], "uid": "231bfdf13683acaff012f9cc475bdd12"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "TestEllaSwitchToLowtempCharge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "faf45958c476966a", "parentUid": "c943e664fb1e3d8f2e7feabc9d26cf9b", "status": "passed", "time": {"start": 1755540479232, "stop": 1755540501365, "duration": 22133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c943e664fb1e3d8f2e7feabc9d26cf9b"}], "uid": "f274d94636e10c97050c0bced04829d1"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchToPowerSavingMode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "a0520ca561c270a8", "parentUid": "fa491a2fccbb92902b5fea6a0125d0dd", "status": "passed", "time": {"start": 1755540515305, "stop": 1755540537560, "duration": 22255}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fa491a2fccbb92902b5fea6a0125d0dd"}], "uid": "276d2d0724438fcbfe51444d46cecee8"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "TestEllaSwitchToSmartCharge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "55991e123833109c", "parentUid": "f0ac9e1d670781c7273288548b5e912c", "status": "failed", "time": {"start": 1755540551537, "stop": 1755540573104, "duration": 21567}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0ac9e1d670781c7273288548b5e912c"}], "uid": "5dbf89ce49106a865cad1e16016f18aa"}, {"name": "test_switched_to_data_mode", "children": [{"name": "TestEllaSwitchedDataMode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "e25e8aa73ba38521", "parentUid": "ee2a0ba313d9f55972191c0fc58a643b", "status": "passed", "time": {"start": 1755540587325, "stop": 1755540611357, "duration": 24032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee2a0ba313d9f55972191c0fc58a643b"}], "uid": "7f43f619728469165153ad2076591cb3"}, {"name": "test_take_a_photo", "children": [{"name": "TestEllaTakePhoto", "children": [{"name": "测试take a photo能正常执行", "uid": "75e3e5fe8cdf881b", "parentUid": "491b8997a74c0d96f03949ae2b53cb5a", "status": "passed", "time": {"start": 1755540625588, "stop": 1755540668510, "duration": 42922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "491b8997a74c0d96f03949ae2b53cb5a"}], "uid": "cf46440006651ab12729bd35922e17b6"}, {"name": "test_take_a_selfie", "children": [{"name": "TestEllaTakeSelfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "a02a9851e8f97808", "parentUid": "4605cd6537361d8c73216c0b79b6bdcd", "status": "passed", "time": {"start": 1755540682283, "stop": 1755540724540, "duration": 42257}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4605cd6537361d8c73216c0b79b6bdcd"}], "uid": "458be1e68e81f7e0a5f24b2b8e711037"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "TestEllaBatteryMobilePhoneIsTooLow", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "d83d0ba4b2e5fcc3", "parentUid": "e9e63c79cb2e897c6c2ba74546e55ab8", "status": "passed", "time": {"start": 1755540738840, "stop": 1755540766433, "duration": 27593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9e63c79cb2e897c6c2ba74546e55ab8"}], "uid": "7fec87272bec0f0286d76fc5e499fde9"}, {"name": "test_turn_down_alarm_clock_volume", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn down alarm clock volume", "uid": "e21f057e4d8226ef", "parentUid": "7e0c9461d23a06cd9aa28527904d1c88", "status": "passed", "time": {"start": 1755540780248, "stop": 1755540803102, "duration": 22854}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e0c9461d23a06cd9aa28527904d1c88"}], "uid": "588ec2ce29d18bbaaaa1a5253cb13e96"}, {"name": "test_turn_down_notifications_volume", "children": [{"name": "TestEllaTurnDownNotificationsVolume", "children": [{"name": "测试turn down notifications volume能正常执行", "uid": "9b668cc2394b5bc4", "parentUid": "edf5af7735521ef7862883fb4a916d84", "status": "passed", "time": {"start": 1755540817193, "stop": 1755540839637, "duration": 22444}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "edf5af7735521ef7862883fb4a916d84"}], "uid": "4cb442df6e9a3d0d1994106adb6fd6d7"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "TestEllaTurnDownRingVolume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "6179f1c471bf7b0", "parentUid": "c650a0dd4c418a43693046a1806d4660", "status": "passed", "time": {"start": 1755540853327, "stop": 1755540875951, "duration": 22624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c650a0dd4c418a43693046a1806d4660"}], "uid": "b8d3945e930e3eeebfc2c83a67a45522"}, {"name": "test_turn_down_the_brightness_to_the_min", "children": [{"name": "TestEllaTurnDownBrightnessMin", "children": [{"name": "测试turn down the brightness to the min能正常执行", "uid": "3e1ef9c9539919e2", "parentUid": "3f254857babc69f7678dda5d0e62e204", "status": "passed", "time": {"start": 1755540890128, "stop": 1755540912248, "duration": 22120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f254857babc69f7678dda5d0e62e204"}], "uid": "d0625ffcef34d9d50a229be4c254d554"}, {"name": "test_turn_off_adaptive_brightness", "children": [{"name": "TestEllaTurnOffAdaptiveBrightness", "children": [{"name": "测试turn off adaptive brightness能正常执行", "uid": "ce4bd0701fd3c3fd", "parentUid": "d9e598c2a2b064d69495870cd847b2b3", "status": "passed", "time": {"start": 1755540926281, "stop": 1755540949515, "duration": 23234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e598c2a2b064d69495870cd847b2b3"}], "uid": "69e5db1a1760a63aafa8296f1d05bfa4"}, {"name": "test_turn_off_auto_rotate_screen", "children": [{"name": "TestEllaTurnOffAutoRotateScreen", "children": [{"name": "测试turn off auto rotate screen能正常执行", "uid": "75fa29c2105e19b3", "parentUid": "d1e2e0480b55d89e14304cac0cca577a", "status": "passed", "time": {"start": 1755540963765, "stop": 1755540986553, "duration": 22788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d1e2e0480b55d89e14304cac0cca577a"}], "uid": "577eb2cd9ec851e31856e504e0bd1722"}, {"name": "test_turn_off_flashlight", "children": [{"name": "TestEllaTurnOffFlashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "4eadf7aaf6065e14", "parentUid": "11b19c6407e932f014698c24c4c16b7b", "status": "passed", "time": {"start": 1755541000559, "stop": 1755541024984, "duration": 24425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11b19c6407e932f014698c24c4c16b7b"}], "uid": "83abfdb472ff182fcf9c9f70441da7db"}, {"name": "test_turn_off_light_theme", "children": [{"name": "TestEllaTurnOffLightTheme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "bf3e0d5d8588af5b", "parentUid": "7d565ca90fb003f1876217d97fc59c20", "status": "passed", "time": {"start": 1755541038774, "stop": 1755541060765, "duration": 21991}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d565ca90fb003f1876217d97fc59c20"}], "uid": "2907e63ef40baa3266bf04ea46042c06"}, {"name": "test_turn_off_nfc", "children": [{"name": "TestEllaTurnOffNfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "5c1299382f028a5e", "parentUid": "140cc3d324fc5f756712663d6db76c28", "status": "passed", "time": {"start": 1755541074574, "stop": 1755541099089, "duration": 24515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "140cc3d324fc5f756712663d6db76c28"}], "uid": "47184378bab69b86d9722f52ee466ee3"}, {"name": "test_turn_off_smart_reminder", "children": [{"name": "TestEllaTurnOffSmartReminder", "children": [{"name": "测试turn off smart reminder能正常执行", "uid": "ce0cbaec22f70255", "parentUid": "e7b8db2dc50016e560cf1ae045e75639", "status": "passed", "time": {"start": 1755541113139, "stop": 1755541135920, "duration": 22781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e7b8db2dc50016e560cf1ae045e75639"}], "uid": "9c2fb62f5a3a109c1d35edda86e2a528"}, {"name": "test_turn_on_adaptive_brightness", "children": [{"name": "TestEllaTurnAdaptiveBrightness", "children": [{"name": "测试turn on adaptive brightness能正常执行", "uid": "774b232eb8b4279", "parentUid": "18e349660100e06c9a3418f109d515b3", "status": "failed", "time": {"start": 1755541150238, "stop": 1755541173024, "duration": 22786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18e349660100e06c9a3418f109d515b3"}], "uid": "a2e8262a15b67dbb0d1c8ff085d2cea3"}, {"name": "test_turn_on_airplane_mode", "children": [{"name": "TestEllaTurnAirplaneMode", "children": [{"name": "测试turn on airplane mode能正常执行", "uid": "155df7699608c58e", "parentUid": "3939d2640f0007e7c5a07e19c0047830", "status": "skipped", "time": {"start": 1755541174560, "stop": 1755541174560, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='airplane mode 会导致设备断开网络，先跳过')"]}], "uid": "3939d2640f0007e7c5a07e19c0047830"}], "uid": "d230c62813c90fd78a5c44bcf0813b36"}, {"name": "test_turn_on_auto_rotate_screen", "children": [{"name": "TestEllaTurnAutoRotateScreen", "children": [{"name": "测试turn on auto rotate screen能正常执行", "uid": "ef8f8d42edcb4f66", "parentUid": "6d818f22aed2045062e8e87f5e1a7455", "status": "failed", "time": {"start": 1755541186998, "stop": 1755541209769, "duration": 22771}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d818f22aed2045062e8e87f5e1a7455"}], "uid": "2e1501efc6a3c64c1060caa80eaa6607"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "TestEllaTurnBluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "eeaa4130f4971e69", "parentUid": "50b13d10c6f48bcd3b710698be278545", "status": "passed", "time": {"start": 1755541224153, "stop": 1755541246179, "duration": 22026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50b13d10c6f48bcd3b710698be278545"}], "uid": "73521b2a406c05e4ea6775f8ac550b11"}, {"name": "test_turn_on_brightness_to_80", "children": [{"name": "TestEllaTurnBrightness", "children": [{"name": "测试turn on brightness to 80能正常执行", "uid": "d0217fa5fc8a4a81", "parentUid": "34d72bbcd3a4fe638c7b4bc56fe3e3af", "status": "passed", "time": {"start": 1755541260399, "stop": 1755541282499, "duration": 22100}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34d72bbcd3a4fe638c7b4bc56fe3e3af"}], "uid": "be633c533b133d5847b41035b5320d37"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "TestEllaTurnDoNotDisturbMode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "baa21503fd9a96ca", "parentUid": "b24c19041d3913a347b45ddb7794d996", "status": "passed", "time": {"start": 1755541296337, "stop": 1755541319475, "duration": 23138}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b24c19041d3913a347b45ddb7794d996"}], "uid": "25300102bed6a3f35a171590779f5760"}, {"name": "test_turn_on_light_theme", "children": [{"name": "TestEllaTurnLightTheme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "7d0c5927796b6392", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1755541333638, "stop": 1755541355918, "duration": 22280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "3c7c9f887c88cd32", "parentUid": "33d9ea5586f4fc177aea2dcfffa56adf", "status": "passed", "time": {"start": 1755541369911, "stop": 1755541391953, "duration": 22042}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33d9ea5586f4fc177aea2dcfffa56adf"}], "uid": "491955b9c16082a7c444ca2d26b7637c"}, {"name": "test_turn_on_location_services", "children": [{"name": "TestEllaTurnLocationServices", "children": [{"name": "测试turn on location services能正常执行", "uid": "95216e3dbb2b6a8e", "parentUid": "b0fdf160b2ecb52332e6867a9d6becde", "status": "passed", "time": {"start": 1755541406362, "stop": 1755541429561, "duration": 23199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0fdf160b2ecb52332e6867a9d6becde"}], "uid": "cf19f0ebd19888cf46f82f4e0bd4bfac"}, {"name": "test_turn_on_nfc", "children": [{"name": "TestEllaTurnNfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "eb294921beeb20c3", "parentUid": "125b66f5b4fa0498985a1eb2ee60c6e7", "status": "failed", "time": {"start": 1755541443841, "stop": 1755541468196, "duration": 24355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "125b66f5b4fa0498985a1eb2ee60c6e7"}], "uid": "5208de3eaa24aeb389307a172f7857eb"}, {"name": "test_turn_on_smart_reminder", "children": [{"name": "TestEllaTurnSmartReminder", "children": [{"name": "测试turn on smart reminder能正常执行", "uid": "bd361d609157db", "parentUid": "5c2a01e86a7808eb62821ab670dc1654", "status": "failed", "time": {"start": 1755541482387, "stop": 1755541505404, "duration": 23017}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c2a01e86a7808eb62821ab670dc1654"}], "uid": "d3a8bf8b5d3c108f49bd07343856e0a3"}, {"name": "test_turn_on_the_7am_alarm", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn on the 7AM alarm", "uid": "4b737429b4e03ee3", "parentUid": "406e19372ea231bcfb9f9bf7eee96e74", "status": "passed", "time": {"start": 1755541519387, "stop": 1755541541302, "duration": 21915}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "406e19372ea231bcfb9f9bf7eee96e74"}], "uid": "ff509714e5ac2f6372d3841cb9423f82"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "TestEllaTurnFlashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "563422272498211e", "parentUid": "a46ae606b16354d85bcc730dcdcbe96e", "status": "passed", "time": {"start": 1755541555184, "stop": 1755541580044, "duration": 24860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a46ae606b16354d85bcc730dcdcbe96e"}], "uid": "5ff8e41b3404a9b3f6cce0bee037eb96"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "TestEllaTurnScreenRecord", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "bcecb343f6bed949", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "passed", "time": {"start": 1755541594412, "stop": 1755541621649, "duration": 27237}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "9b5c88885de7c800", "parentUid": "678c4f3bd7c3cae1c7216386baae6d8b", "status": "failed", "time": {"start": 1755541635822, "stop": 1755541662964, "duration": 27142}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "678c4f3bd7c3cae1c7216386baae6d8b"}], "uid": "62efa7d4bd969e35ac386aa7097579c5"}, {"name": "test_turn_on_wifi", "children": [{"name": "TestEllaTurnWifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "937a6a1b80540fdf", "parentUid": "6628c4e31fae719ad94c679a246d743e", "status": "passed", "time": {"start": 1755541676941, "stop": 1755541700497, "duration": 23556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6628c4e31fae719ad94c679a246d743e"}], "uid": "8a973098444298c6ff004df8ce42b561"}, {"name": "test_turn_up_alarm_clock_volume", "children": [{"name": "<PERSON><PERSON>lla<PERSON><PERSON>", "children": [{"name": "测试turn up alarm clock volume", "uid": "78d354cf604a4076", "parentUid": "1fd57584780ed00e02e485b64ab749a0", "status": "passed", "time": {"start": 1755541714266, "stop": 1755541736705, "duration": 22439}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1fd57584780ed00e02e485b64ab749a0"}], "uid": "5550d272d18d8c3628b89d44f867786a"}, {"name": "test_turn_up_notifications_volume", "children": [{"name": "TestEllaTurnUpNotificationsVolume", "children": [{"name": "测试turn up notifications volume能正常执行", "uid": "15453db92b625b22", "parentUid": "a0f5108760ffcb32a71bed828c69ed5b", "status": "passed", "time": {"start": 1755541750629, "stop": 1755541773466, "duration": 22837}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0f5108760ffcb32a71bed828c69ed5b"}], "uid": "ad092db19588044c41fa1bcfe3390ec4"}, {"name": "test_turn_up_ring_volume", "children": [{"name": "TestEllaTurnUpRingVolume", "children": [{"name": "测试turn up ring volume能正常执行", "uid": "981e04ed084bd6b0", "parentUid": "a503cc046614ff4a66a4e07f8b13e370", "status": "passed", "time": {"start": 1755541787396, "stop": 1755541810217, "duration": 22821}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a503cc046614ff4a66a4e07f8b13e370"}], "uid": "a719872171e528ab034126de6c2a6628"}, {"name": "test_turn_up_the_brightness_to_the_max", "children": [{"name": "TestEllaTurnUpBrightnessMax", "children": [{"name": "测试turn up the brightness to the max能正常执行", "uid": "2bb3ce4693c21548", "parentUid": "82bbd77195f12ce6b645cd49594ab92a", "status": "passed", "time": {"start": 1755541823865, "stop": 1755541846352, "duration": 22487}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82bbd77195f12ce6b645cd49594ab92a"}], "uid": "22a701d2a2e514a3be7d5ed569e7a767"}, {"name": "test_turn_up_the_volume_to_the_max", "children": [{"name": "TestEllaTurnUpVolumeMax", "children": [{"name": "测试turn up the volume to the max能正常执行", "uid": "acf343c73d15018b", "parentUid": "dff7e9ecb9b310d1c8c562cc8438cb43", "status": "passed", "time": {"start": 1755541859998, "stop": 1755541882503, "duration": 22505}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dff7e9ecb9b310d1c8c562cc8438cb43"}], "uid": "ab410675942805398e84ee27463212f4"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "TestEllaWakeMeUpAmTomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "a7eef724e114d433", "parentUid": "a95ef973249db47161b0248e62a09176", "status": "failed", "time": {"start": 1755541896311, "stop": 1755541918389, "duration": 22078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a95ef973249db47161b0248e62a09176"}], "uid": "19346142b5a8f43c02ff8d5890db9067"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "TestEllaWhereIsCarlcareServiceOutlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "245590744fd1760c", "parentUid": "ca0e45e31e627eb4f9d8c39d6a6bf138", "status": "passed", "time": {"start": 1755541932633, "stop": 1755541964641, "duration": 32008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0e45e31e627eb4f9d8c39d6a6bf138"}], "uid": "7d4ef22a8f804451209374dfe8bf64ad"}], "uid": "4159dc35ce06d1422bb1b7c5665d834a"}, {"name": "testcases.test_ella.third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "TestEllaDownloadApp", "children": [{"name": "测试download app能正常执行", "uid": "4ed39439d861cebf", "parentUid": "3437a290da1a678bac85f4666839d881", "status": "passed", "time": {"start": 1755541978279, "stop": 1755542002141, "duration": 23862}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3437a290da1a678bac85f4666839d881"}], "uid": "ddb6cf5770aedc759f08823b3e6d8b65"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball能正常执行", "uid": "5a0f62687113a756", "parentUid": "588cc0c6328e2e8a712130edc3aed897", "status": "failed", "time": {"start": 1755542015938, "stop": 1755542039422, "duration": 23484}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "588cc0c6328e2e8a712130edc3aed897"}], "uid": "1d4ceb2f76f4a7e0f4095f5c1f138613"}, {"name": "test_download_qq", "children": [{"name": "TestEllaDownloadQq", "children": [{"name": "测试download qq能正常执行", "uid": "cf4417d6abdc10b1", "parentUid": "510be9da11cf469597d4e78dac5a719b", "status": "passed", "time": {"start": 1755542053420, "stop": 1755542086701, "duration": 33281}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "510be9da11cf469597d4e78dac5a719b"}], "uid": "e47ca49712e495f1dd67541fd74682ac"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "TestEllaFindRestaurantNearMe", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "354bfa2b8e5a4d90", "parentUid": "2a2a6349a33f526f9659f59faee83c46", "status": "passed", "time": {"start": 1755542100645, "stop": 1755542138165, "duration": 37520}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a2a6349a33f526f9659f59faee83c46"}], "uid": "6ecd9aa1e47da9fc316c168ef75730c9"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "TestEllaNavigateFromBeijingShanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "d621004ece966b52", "parentUid": "d7122eb175471ed23416d503cb147f88", "status": "passed", "time": {"start": 1755542152419, "stop": 1755542185515, "duration": 33096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7122eb175471ed23416d503cb147f88"}], "uid": "7b053180829d8e59af204f55867fbbbe"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "TestEllaNavigateFromRedSquare", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "467ff75891f341fb", "parentUid": "e6c951f1c462cc23dddd03e98f8d3a27", "status": "passed", "time": {"start": 1755542199306, "stop": 1755542229004, "duration": 29698}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6c951f1c462cc23dddd03e98f8d3a27"}], "uid": "f52e70464a63502b53d063b021cf198a"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "TestEllaNavigateShanghaiDisneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "5f18b39a20f5d622", "parentUid": "5ae1d6975695489eb2d69ce256cf164f", "status": "passed", "time": {"start": 1755542242740, "stop": 1755542271847, "duration": 29107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ae1d6975695489eb2d69ce256cf164f"}], "uid": "1dbef048a8c2c8a81b66d201495c6825"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "TestEllaNavigationToTheLucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "32df01cfab8864af", "parentUid": "719ca28cb458f784122e2efc4135056f", "status": "passed", "time": {"start": 1755542285440, "stop": 1755542316964, "duration": 31524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "719ca28cb458f784122e2efc4135056f"}], "uid": "de0db7b40aca346332d2b0972719957a"}, {"name": "test_open_facebook", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试open facebook能正常执行", "uid": "41a258194b47f5ba", "parentUid": "7b0b3aea4bca28c8189ca6a4be130237", "status": "passed", "time": {"start": 1755542330966, "stop": 1755542360458, "duration": 29492}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0b3aea4bca28c8189ca6a4be130237"}], "uid": "e4224d3534251a2780344938a85b3c06"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "2e458485db602770", "parentUid": "e65b3469b422d0d9909fee2c8bfa0960", "status": "passed", "time": {"start": 1755542374615, "stop": 1755542398281, "duration": 23666}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e65b3469b422d0d9909fee2c8bfa0960"}], "uid": "5aeebca39d474445d6f5ba2265081895"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaCommandConcise", "children": [{"name": "测试order a burger能正常执行", "uid": "190a2c7d515274c1", "parentUid": "43a31fc74a106d981123a4cb94e40290", "status": "failed", "time": {"start": 1755542412322, "stop": 1755542433838, "duration": 21516}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43a31fc74a106d981123a4cb94e40290"}], "uid": "3e2cc02d0935d06e8fb6f72bae4c8c83"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderATakeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "4bb0f3490eb7fd10", "parentUid": "d9e81a960347bb8bb7bfbcc70b663c4b", "status": "failed", "time": {"start": 1755542448115, "stop": 1755542469919, "duration": 21804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9e81a960347bb8bb7bfbcc70b663c4b"}], "uid": "24271799b32debb7d5f05bce65733a10"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "TestEllaOpenPlsNewestWhatsappActivity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "68e268cc4fc1b311", "parentUid": "5022c6b03e034f068f4ddbc7533f4c29", "status": "passed", "time": {"start": 1755542483785, "stop": 1755542507616, "duration": 23831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5022c6b03e034f068f4ddbc7533f4c29"}], "uid": "9b7c89d46b9b721d197765453f1675de"}, {"name": "test_whatsapp", "children": [{"name": "TestEllaW<PERSON>sapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "6e8ca94f901f3575", "parentUid": "edf22212c64c387b5f0b05436a17000b", "status": "passed", "time": {"start": 1755542521457, "stop": 1755542545051, "duration": 23594}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "edf22212c64c387b5f0b05436a17000b"}], "uid": "0438f804905f59a497b4803048f51319"}], "uid": "2660f6320a566ad526d6ea679fb2528f"}, {"name": "testcases.test_ella.unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "b9851214b3a35736", "parentUid": "433b944b4500addcf62906018e50a6af", "status": "passed", "time": {"start": 1755542559339, "stop": 1755542581522, "duration": 22183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "433b944b4500addcf62906018e50a6af"}], "uid": "3d0102c3a98807edcea83109224019cc"}, {"name": "test_Language_List", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Language List", "uid": "372bf679db832d71", "parentUid": "e250a4f9b681f22448fa4597aa4785c6", "status": "passed", "time": {"start": 1755542595818, "stop": 1755542617831, "duration": 22013}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e250a4f9b681f22448fa4597aa4785c6"}], "uid": "cd62e30e09154e5270f2e561d5a6d682"}, {"name": "test_a_clear_and_pink_crystal_necklace_in_the_water", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试a clear and pink crystal necklace in the water", "uid": "bde5993ab80214c5", "parentUid": "b04da96ca2c5ffb3e178b6044ed98663", "status": "passed", "time": {"start": 1755542631858, "stop": 1755542657387, "duration": 25529}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b04da96ca2c5ffb3e178b6044ed98663"}], "uid": "dd8dabc47df69c7ff6925525834e6b5d"}, {"name": "test_a_clear_glass_cup", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试a clear glass cup", "uid": "f87fb922cd22cb5e", "parentUid": "a703f3440f4f5dc5b77cb183f8f6cc33", "status": "passed", "time": {"start": 1755542671215, "stop": 1755542697241, "duration": 26026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a703f3440f4f5dc5b77cb183f8f6cc33"}], "uid": "9ecdd746f41031bcebbe8fcee2412b97"}, {"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A cute little boy is skiing", "uid": "bd35946baa6a9c7c", "parentUid": "f895c42cd72820018e87de547b49f01b", "status": "passed", "time": {"start": 1755542711300, "stop": 1755542736578, "duration": 25278}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f895c42cd72820018e87de547b49f01b"}], "uid": "ad855646f44d184e26182c391db68429"}, {"name": "test_a_cute_little_girl_with_long_hair", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "70a5f4ae04e6558e", "parentUid": "792bccff1df1231fa08787f8c68c7def", "status": "failed", "time": {"start": 1755542750283, "stop": 1755542772092, "duration": 21809}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "792bccff1df1231fa08787f8c68c7def"}], "uid": "19cc2704894932f7b7b1883c73fe3eaf"}, {"name": "test_a_furry_little_monkey", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A furry little monkey", "uid": "77992e69d66afef9", "parentUid": "66f57b2040dff8d94d6ac334f412f4d6", "status": "failed", "time": {"start": 1755542786715, "stop": 1755542811669, "duration": 24954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66f57b2040dff8d94d6ac334f412f4d6"}], "uid": "cea6f1c7ff4800312176bb95faa46b88"}, {"name": "test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "30209a062596ef82", "parentUid": "812414d61916005d6c107a49d6ad0d1c", "status": "failed", "time": {"start": 1755542825673, "stop": 1755542852185, "duration": 26512}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "812414d61916005d6c107a49d6ad0d1c"}], "uid": "73eb730ca2fadb0d731cb550db1dcb8d"}, {"name": "test_a_little_raccoon_walks_on_a_forest_meadow", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "f1406a88d312092e", "parentUid": "440f43d7ca93715102a4152c367beb85", "status": "failed", "time": {"start": 1755542869874, "stop": 1755542891488, "duration": 21614}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "440f43d7ca93715102a4152c367beb85"}], "uid": "762e5eeb5b55d5b1b3ad62536c184335"}, {"name": "test_a_photo_of_a_transparent_glass_cup", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A photo of a transparent glass cup ", "uid": "fae32e11992942f2", "parentUid": "f6966cfdf0f4a39d70c6a79f72fbd7a8", "status": "failed", "time": {"start": 1755542905521, "stop": 1755542927616, "duration": 22095}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6966cfdf0f4a39d70c6a79f72fbd7a8"}], "uid": "1930ae6709e88717ab36f53803600401"}, {"name": "test_a_sports_car_is_parked_on_the_street_side", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A sports car is parked on the street side", "uid": "ee55ab8579ed7a97", "parentUid": "82cf8bf878579c226c80fc451c912eb2", "status": "passed", "time": {"start": 1755542941770, "stop": 1755542967243, "duration": 25473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82cf8bf878579c226c80fc451c912eb2"}], "uid": "3d50309a5a87be1cb2c1d3d7f9fe365c"}, {"name": "test_call_mom", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试call mom", "uid": "8be6bece5fd1af0d", "parentUid": "77b1d17992bf49b776313e0563c2514f", "status": "failed", "time": {"start": 1755542980983, "stop": 1755543012019, "duration": 31036}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77b1d17992bf49b776313e0563c2514f"}], "uid": "12a9c3dc55db996f79320d1f35dc1770"}, {"name": "test_call_number_by_whatsapp", "children": [{"name": "TestEllaCallNumberWhatsapp", "children": [{"name": "测试call number by whatsapp能正常执行", "uid": "a36e4a54b6cc5d0", "parentUid": "6fc746e24aa09c4f4b982efdbdb53b0a", "status": "passed", "time": {"start": 1755543026002, "stop": 1755543057161, "duration": 31159}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6fc746e24aa09c4f4b982efdbdb53b0a"}], "uid": "d372067d12100f89f325e18e4635c766"}, {"name": "test_can_u_check_the_notebook", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试can u check the notebook", "uid": "dad0ac13e605048a", "parentUid": "25272118128dbe8d4265e3373095afa7", "status": "passed", "time": {"start": 1755543071377, "stop": 1755543106052, "duration": 34675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25272118128dbe8d4265e3373095afa7"}], "uid": "1292c0e244c7c6fca5019f141d34ac19"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "TestEllaChangeFemaleToneNameVoice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "3e0c9fe5dca6e346", "parentUid": "b565d06b86fa223901766faddeda207a", "status": "passed", "time": {"start": 1755543119889, "stop": 1755543142147, "duration": 22258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b565d06b86fa223901766faddeda207a"}], "uid": "66562456c81152cc1fa9d09032ae884d"}, {"name": "test_change_man_voice", "children": [{"name": "TestEllaChangeManVoice", "children": [{"name": "测试change man voice能正常执行", "uid": "4e20f74cb4938ea2", "parentUid": "9211e5358ec84e06d14d678e7d922690", "status": "passed", "time": {"start": 1755543156187, "stop": 1755543177815, "duration": 21628}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9211e5358ec84e06d14d678e7d922690"}], "uid": "2d491290e2846a176599f1eae33a3ad3"}, {"name": "test_change_your_voice", "children": [{"name": "TestEllaChangeYourVoice", "children": [{"name": "测试change your voice能正常执行", "uid": "753d92772d675cae", "parentUid": "0d45916b91412c59944b54b906868410", "status": "passed", "time": {"start": 1755543191647, "stop": 1755543214228, "duration": 22581}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d45916b91412c59944b54b906868410"}], "uid": "99d1138eba2f3e2cb20c8824270684bb"}, {"name": "test_check_battery_information", "children": [{"name": "TestEllaCheckBatteryInformation", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "6325a8bfe82ee58", "parentUid": "d28e0e4ff0e4838a6027dc47e3b3a670", "status": "passed", "time": {"start": 1755543228368, "stop": 1755543250570, "duration": 22202}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d28e0e4ff0e4838a6027dc47e3b3a670"}], "uid": "4c906a8c1578240415aebe31f410f234"}, {"name": "test_check_contact", "children": [{"name": "TestEllaCheckContact", "children": [{"name": "测试check contact能正常执行", "uid": "130b6c368ae3f475", "parentUid": "7840d5d29bd3ef68fd5883a60ba4d483", "status": "passed", "time": {"start": 1755543264398, "stop": 1755543295558, "duration": 31160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7840d5d29bd3ef68fd5883a60ba4d483"}], "uid": "26722106422710fc718c70034be7a8c2"}, {"name": "test_check_contacts", "children": [{"name": "TestEllaCheckContacts", "children": [{"name": "测试check contacts能正常执行", "uid": "6966801bd55088f5", "parentUid": "7b0c7a58c89c6e54945f60f9f70c7cd2", "status": "passed", "time": {"start": 1755543309236, "stop": 1755543340429, "duration": 31193}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0c7a58c89c6e54945f60f9f70c7cd2"}], "uid": "52118859006a30586241bd2a4a26847c"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "TestEllaCheckMobileDataBalanceSim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "858c22d7630a2a7d", "parentUid": "2e50c8269684c4a73bb5b020614c2221", "status": "passed", "time": {"start": 1755543354491, "stop": 1755543376726, "duration": 22235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e50c8269684c4a73bb5b020614c2221"}], "uid": "ff56d23044bd5d499190e5ca173df4d3"}, {"name": "test_check_model_information", "children": [{"name": "TestEllaCheckModelInformation", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "5d13ab9424ca7076", "parentUid": "f7e3ca8a9343a9ed054471fe3e0356b4", "status": "passed", "time": {"start": 1755543390467, "stop": 1755543412287, "duration": 21820}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f7e3ca8a9343a9ed054471fe3e0356b4"}], "uid": "bf2cc0fa355876e3be500d0c5ba7a5f1"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "TestEllaCheckMyBalanceSim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "2488d85e6c4e33d8", "parentUid": "f9995b28dafd2b1ed8762fedcb92c132", "status": "passed", "time": {"start": 1755543426204, "stop": 1755543448737, "duration": 22533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9995b28dafd2b1ed8762fedcb92c132"}], "uid": "ffd79a31b3c86841ac0cc6d5fa40f471"}, {"name": "test_check_my_to_do_list", "children": [{"name": "TestEllaCheckMyDoList", "children": [{"name": "测试check my to-do list能正常执行", "uid": "11fc47f08ff73cab", "parentUid": "43310d5383f9f3391eb3ba14577fd4d7", "status": "passed", "time": {"start": 1755543462596, "stop": 1755543484509, "duration": 21913}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43310d5383f9f3391eb3ba14577fd4d7"}], "uid": "20383a2eb3e29a6c6ccd53f0bce07fe1"}, {"name": "test_check_ram_information", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试check ram information", "uid": "18dcfed9cffe3fc1", "parentUid": "c46ca58f39fd573de3f84a18c7b2e1e9", "status": "passed", "time": {"start": 1755543498357, "stop": 1755543520294, "duration": 21937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c46ca58f39fd573de3f84a18c7b2e1e9"}], "uid": "91aea13b936df1ab314bc65e1546585f"}, {"name": "test_check_rear_camera_information", "children": [{"name": "TestEllaCheckRearCameraInformation", "children": [{"name": "测试check rear camera information能正常执行", "uid": "59ce51639a430d6d", "parentUid": "89f05870118719c157701595fa592ca6", "status": "passed", "time": {"start": 1755543534411, "stop": 1755543556865, "duration": 22454}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89f05870118719c157701595fa592ca6"}], "uid": "3e2d1f7b0da0ab237b35604822208008"}, {"name": "test_check_system_update", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试check system update", "uid": "ecc71f2f493e1262", "parentUid": "6c6a21d7737144a2d795582971e816d5", "status": "failed", "time": {"start": 1755543571254, "stop": 1755543593013, "duration": 21759}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6c6a21d7737144a2d795582971e816d5"}], "uid": "68e3fa15aac7bb6f77f9d22e4bb16c45"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "TestEllaCloseEquilibriumMode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "d5c602fd9982fe2", "parentUid": "2ca6423e5b9b0595a667d74916b87ae8", "status": "passed", "time": {"start": 1755543607155, "stop": 1755543628894, "duration": 21739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ca6423e5b9b0595a667d74916b87ae8"}], "uid": "cf0d3a245849cc3d2fb49b5919a2ca70"}, {"name": "test_close_performance_mode", "children": [{"name": "TestEllaClosePerformanceMode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "4b4b33da4101679c", "parentUid": "bfc98a3e31cf71b24d2c2664874d8f35", "status": "passed", "time": {"start": 1755543642589, "stop": 1755543664873, "duration": 22284}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfc98a3e31cf71b24d2c2664874d8f35"}], "uid": "921c03c2af17c0dd8bb300d05ebc1435"}, {"name": "test_close_power_saving_mode", "children": [{"name": "TestEllaClosePowerSavingMode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "859cd1ac4096dca4", "parentUid": "d7c06ff8b4a17dd9ae62ae957d96f702", "status": "passed", "time": {"start": 1755543678926, "stop": 1755543701149, "duration": 22223}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7c06ff8b4a17dd9ae62ae957d96f702"}], "uid": "de4693d49200ef3897dfa7e2a5aa8dbc"}, {"name": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "uid": "6e8cfebab1b113de", "parentUid": "2d1979bb5d67b6016ab6089eae182085", "status": "passed", "time": {"start": 1755543714952, "stop": 1755543736798, "duration": 21846}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d1979bb5d67b6016ab6089eae182085"}], "uid": "d6f0130a0822a0d9e083c98b99701be9"}, {"name": "test_dial_the_number_on_the_screen", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Dial the number on the screen", "uid": "e6a76f74a137ce40", "parentUid": "25b184d1594691843e5a3d7441f02e73", "status": "passed", "time": {"start": 1755543750696, "stop": 1755543773049, "duration": 22353}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25b184d1594691843e5a3d7441f02e73"}], "uid": "72e0dd751330ef0df21d89f181051bb1"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "TestEllaDisableAccelerateDialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "ab36b5e97a9a304d", "parentUid": "a12fb87a3ef6191f6c5649ce65173837", "status": "passed", "time": {"start": 1755543787128, "stop": 1755543809387, "duration": 22259}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a12fb87a3ef6191f6c5649ce65173837"}], "uid": "afbbdecfafcd893c4f7104365ff8935e"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "TestEllaDisableAllAiMagicBoxFeatures", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "15de6c1c1a1bde89", "parentUid": "696197cde92ea407d7be5d0ee1a43b1a", "status": "passed", "time": {"start": 1755543823441, "stop": 1755543845293, "duration": 21852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "696197cde92ea407d7be5d0ee1a43b1a"}], "uid": "2b54914538eb635e492ddf923151d357"}, {"name": "test_disable_auto_pickup", "children": [{"name": "TestEllaDisableAutoPickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "2c81a4a04cdc1071", "parentUid": "c20ac19fa762a9db14606dd531e82b8f", "status": "passed", "time": {"start": 1755543859540, "stop": 1755543881464, "duration": 21924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c20ac19fa762a9db14606dd531e82b8f"}], "uid": "bf014ddab53633d98f8104639ce1bc1b"}, {"name": "test_disable_brightness_locking", "children": [{"name": "TestEllaDisableBrightnessLocking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "cd7d712956ba0f25", "parentUid": "858fef1ffc1503318aa70e9c72d7b438", "status": "passed", "time": {"start": 1755543895172, "stop": 1755543917265, "duration": 22093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "858fef1ffc1503318aa70e9c72d7b438"}], "uid": "66c800c4edcde7ca93c0c123e93d1914"}, {"name": "test_disable_call_rejection", "children": [{"name": "TestEllaDisableCallRejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "f47927df2a6b5ce3", "parentUid": "3f640e07903d6f89a09553a61d6690c3", "status": "passed", "time": {"start": 1755543931278, "stop": 1755543962526, "duration": 31248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f640e07903d6f89a09553a61d6690c3"}], "uid": "021f51382efc25258ec8ca7ef3573495"}, {"name": "test_disable_hide_notifications", "children": [{"name": "TestEllaDisableHideNotifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "d3dea9e9d4c15593", "parentUid": "9f132b3849693b61c2ca479bcafef282", "status": "passed", "time": {"start": 1755543976460, "stop": 1755543998487, "duration": 22027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9f132b3849693b61c2ca479bcafef282"}], "uid": "7030498aad7e0e3d901754313db42a5c"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "de12e238457e041e", "parentUid": "04c072a77ad52d90193c2ec6bc53a021", "status": "passed", "time": {"start": 1755544012434, "stop": 1755544034816, "duration": 22382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04c072a77ad52d90193c2ec6bc53a021"}], "uid": "6f0070b209221eadb3d6425a33ecbff1"}, {"name": "test_disable_network_enhancement", "children": [{"name": "TestEllaDisableNetworkEnhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "ae0d0bc69c535867", "parentUid": "fe480ad7296777a27bc8f2d53aa0fccf", "status": "passed", "time": {"start": 1755544049042, "stop": 1755544070936, "duration": 21894}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fe480ad7296777a27bc8f2d53aa0fccf"}], "uid": "afc5376e9464fb8921d2fbcf9e0d3bbc"}, {"name": "test_disable_running_lock", "children": [{"name": "TestEllaDisableRunningLock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "1c93ac7f69b05022", "parentUid": "8fe90804f66731578ca4d4ba9283a64c", "status": "passed", "time": {"start": 1755544085000, "stop": 1755544108381, "duration": 23381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fe90804f66731578ca4d4ba9283a64c"}], "uid": "ad391be6683f94da1f0d53eaeb12f8a3"}, {"name": "test_disable_touch_optimization", "children": [{"name": "TestEllaDisableTouchOptimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "d93759f5df6d71b2", "parentUid": "a61e1b3af122a2fe511826a7e99c2e1b", "status": "passed", "time": {"start": 1755544122605, "stop": 1755544144741, "duration": 22136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a61e1b3af122a2fe511826a7e99c2e1b"}], "uid": "8d875ca58978089e7102b409d8215741"}, {"name": "test_disable_unfreeze", "children": [{"name": "TestEllaDisableUnfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "72118dc54e25e056", "parentUid": "1e21c84af5bdef90eb5dee6bd5cf2635", "status": "passed", "time": {"start": 1755544158549, "stop": 1755544180596, "duration": 22047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e21c84af5bdef90eb5dee6bd5cf2635"}], "uid": "b1772b071366ab2c77148d9716c8dcd9"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "TestEllaDisableZonetouchMaster", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "15cdef69b8beed3e", "parentUid": "21d3787e184cbd6bdfc49c456af0ad04", "status": "passed", "time": {"start": 1755544194307, "stop": 1755544216422, "duration": 22115}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "21d3787e184cbd6bdfc49c456af0ad04"}], "uid": "f5c774dd2222acf0e4aa8f7b201f12b3"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "878b77da97742d88", "parentUid": "98ae41f5cdd53a2d20bf8172efd26083", "status": "failed", "time": {"start": 1755544230502, "stop": 1755544252918, "duration": 22416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98ae41f5cdd53a2d20bf8172efd26083"}], "uid": "314bfe9c667cd809cb0cc507bfcd14fc"}, {"name": "test_download_in_play_store", "children": [{"name": "TestEllaOpenGooglePlaystore", "children": [{"name": "测试download in play store", "uid": "20d5bddf727c0fba", "parentUid": "aaf0d4f406e1ee7e1d9281b54bf64677", "status": "passed", "time": {"start": 1755544267541, "stop": 1755544294143, "duration": 26602}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aaf0d4f406e1ee7e1d9281b54bf64677"}], "uid": "492783bc91711ae1967d7743b73a2b1c"}, {"name": "test_download_in_playstore", "children": [{"name": "TestEllaOpenGooglePlaystore", "children": [{"name": "测试download in playstore", "uid": "8f694e90876ac004", "parentUid": "a7ddb4033678917acec14cb846bb3cb9", "status": "passed", "time": {"start": 1755544308198, "stop": 1755544335028, "duration": 26830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7ddb4033678917acec14cb846bb3cb9"}], "uid": "abd982c695d6752ee4dc9bc4239152c6"}, {"name": "test_download_whatsapp", "children": [{"name": "TestEllaDownloadWhatsapp", "children": [{"name": "测试download whatsapp能正常执行", "uid": "ef7a15af380820c6", "parentUid": "798b400fa18fd45acb82f54257cf6c35", "status": "passed", "time": {"start": 1755544348925, "stop": 1************, "duration": 27972}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "798b400fa18fd45acb82f54257cf6c35"}], "uid": "9d36aebc605ca5348c0fe133f762df22"}, {"name": "test_driving_mode", "children": [{"name": "TestEllaDrivingMode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "e99caa00c608f900", "parentUid": "ef0bd4110e3386c3ad0f74515d6f200e", "status": "failed", "time": {"start": 1755544390869, "stop": 1755544413086, "duration": 22217}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef0bd4110e3386c3ad0f74515d6f200e"}], "uid": "514aae0515740321638cc56a86d0bdc5"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "TestEllaEnableAccelerateDialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "61c1dc608e36883c", "parentUid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7", "status": "passed", "time": {"start": 1755544427265, "stop": 1755544449022, "duration": 21757}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7"}], "uid": "8c56ee95835e673fa28b34154afb4628"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "TestEllaEnableAllAiMagicBoxFeatures", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "e01919838c9e2688", "parentUid": "8a587c12d6caed0b409b8971d49025ab", "status": "passed", "time": {"start": 1755544463027, "stop": 1755544485067, "duration": 22040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8a587c12d6caed0b409b8971d49025ab"}], "uid": "68d379dd7f74dd5275f913b7eee03079"}, {"name": "test_enable_auto_pickup", "children": [{"name": "TestEllaEnableAutoPickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c28179de4e531562", "parentUid": "97d17abb9dc1ccc8f46a34e06a32340c", "status": "passed", "time": {"start": 1755544499399, "stop": 1755544521558, "duration": 22159}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97d17abb9dc1ccc8f46a34e06a32340c"}], "uid": "8c4650098d6069190cfba3c757ba45db"}, {"name": "test_enable_brightness_locking", "children": [{"name": "TestEllaEnableBrightnessLocking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "dc8175669360e0", "parentUid": "c400a86ee772ae2bb87a4d299fcffde3", "status": "passed", "time": {"start": 1755544535387, "stop": 1755544557375, "duration": 21988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c400a86ee772ae2bb87a4d299fcffde3"}], "uid": "0e14ad27fc644f4f62c72d254e5d2d00"}, {"name": "test_enable_call_on_hold", "children": [{"name": "TestEllaEnableCallHold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "361a48d619f8b474", "parentUid": "6d79c2b8de7e0682e35d7e5c727c1ff8", "status": "passed", "time": {"start": 1755544571427, "stop": 1755544602971, "duration": 31544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d79c2b8de7e0682e35d7e5c727c1ff8"}], "uid": "de334526c374af0073eb9eb6ddb0997a"}, {"name": "test_enable_call_rejection", "children": [{"name": "TestEllaEnableCallRejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "abd00a5f90b2dab6", "parentUid": "e26b43428a954a94928f7a25e76abac9", "status": "passed", "time": {"start": 1755544616887, "stop": 1755544647919, "duration": 31032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e26b43428a954a94928f7a25e76abac9"}], "uid": "366922e4cbf42ddefd029f732cdd77bf"}, {"name": "test_enable_network_enhancement", "children": [{"name": "TestEllaEnableNetworkEnhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "7c313c685873fd0c", "parentUid": "c12a817d493d41c56a647cb659a7bb8c", "status": "passed", "time": {"start": 1755544662095, "stop": 1755544684324, "duration": 22229}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c12a817d493d41c56a647cb659a7bb8c"}], "uid": "040b928633962c521baae26a6f7ac7c5"}, {"name": "test_enable_running_lock", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "3f740db0cef2375b", "parentUid": "e1c78a96555072edad6f93b7ff33937c", "status": "passed", "time": {"start": 1755544698511, "stop": 1755544721984, "duration": 23473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c78a96555072edad6f93b7ff33937c"}], "uid": "26ee37ce6707d6284177cfa0f1cae12c"}, {"name": "test_enable_touch_optimization", "children": [{"name": "TestEllaEnableTouchOptimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "a69cd11544f75120", "parentUid": "d84bfce90dcde3b576c695692fca0fa8", "status": "passed", "time": {"start": 1755544736044, "stop": 1755544757995, "duration": 21951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d84bfce90dcde3b576c695692fca0fa8"}], "uid": "76d94ee4c89c6674e70da8c74116bad6"}, {"name": "test_enable_unfreeze", "children": [{"name": "TestEllaEnableUnfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "409d638ac1bedc2", "parentUid": "c801d69cec54af644c884ba704111151", "status": "passed", "time": {"start": 1755544772103, "stop": 1755544794095, "duration": 21992}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c801d69cec54af644c884ba704111151"}], "uid": "63e313e7547561b2fa987da4d6072437"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "TestEllaEnableZonetouchMaster", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "5b707a563124442", "parentUid": "99a67c39634063a502862411f9efdb5c", "status": "passed", "time": {"start": 1755544808173, "stop": 1755544830192, "duration": 22019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99a67c39634063a502862411f9efdb5c"}], "uid": "796eef53680203e2ca4133277e86230b"}, {"name": "test_end_exercising", "children": [{"name": "TestEllaEndExercising", "children": [{"name": "测试end exercising能正常执行", "uid": "efbf1ae95846c87f", "parentUid": "115b3d23b2b26d5aeb94233ec9b50278", "status": "passed", "time": {"start": 1755544844031, "stop": 1755544866050, "duration": 22019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "115b3d23b2b26d5aeb94233ec9b50278"}], "uid": "9e440169e9a1bdfad60618870de9c8fe"}, {"name": "test_extend_the_image", "children": [{"name": "TestEllaExtendImage", "children": [{"name": "测试extend the image能正常执行", "uid": "d5ebc6c67f353f03", "parentUid": "58d8f715c93e890833f08e611da6c0f8", "status": "failed", "time": {"start": 1755544880054, "stop": 1755544901864, "duration": 21810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58d8f715c93e890833f08e611da6c0f8"}], "uid": "600af0308ad3a3f35324296fc9a30562"}, {"name": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "uid": "a216829f25a48ac0", "parentUid": "0c92e72df5f2847a61bf235a8cbfb696", "status": "passed", "time": {"start": 1755544915793, "stop": 1755544937936, "duration": 22143}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0c92e72df5f2847a61bf235a8cbfb696"}], "uid": "e4095cc936373937b75adee961d8e38b"}, {"name": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "uid": "dc42dd62a92d5d19", "parentUid": "7ba63c9f0831876e7868658118b379e0", "status": "passed", "time": {"start": 1755544951816, "stop": 1755544973951, "duration": 22135}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7ba63c9f0831876e7868658118b379e0"}], "uid": "59ed61d309053c170ff49b9a0b0a0cf9"}, {"name": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "uid": "282267417455ce5d", "parentUid": "94d579da046850e9a9f4099b86418446", "status": "passed", "time": {"start": 1755544988234, "stop": 1755545010487, "duration": 22253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "94d579da046850e9a9f4099b86418446"}], "uid": "4cbd0eaf1466a2337d2988e40aa92879"}, {"name": "test_generate_a_landscape_painting_image_for_me", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a landscape painting image for me", "uid": "4b2de58234c4451e", "parentUid": "ee0382687cbc2480f8fb56bfdc6988b5", "status": "failed", "time": {"start": 1755545024642, "stop": 1755545046581, "duration": 21939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee0382687cbc2480f8fb56bfdc6988b5"}], "uid": "4a89b2d6e05cb420b3e45d39e3e11873"}, {"name": "test_generate_a_picture_in_the_night_forest_for_me", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a picture in the night forest for me", "uid": "14dee738f79101ae", "parentUid": "29bcbb379958b5ef79b813fb44a1326d", "status": "passed", "time": {"start": 1755545061038, "stop": 1755545082942, "duration": 21904}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29bcbb379958b5ef79b813fb44a1326d"}], "uid": "5539864d890598bd6045117e5164ff8c"}, {"name": "test_generate_a_picture_of_a_jungle_stream_for_me", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate a picture of a jungle stream for me", "uid": "3409f70c757d998f", "parentUid": "64efa8e92443733dc5129597edc54be6", "status": "passed", "time": {"start": 1755545096743, "stop": 1755545118876, "duration": 22133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "64efa8e92443733dc5129597edc54be6"}], "uid": "f50105097da8bcb12b8f5dedaf0bad03"}, {"name": "test_generate_an_image_of_a_chubby_orange_cat_chef", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "uid": "bad169110b617eec", "parentUid": "4c964ed913dcaffe60107b228a13db00", "status": "passed", "time": {"start": 1755545132693, "stop": 1755545154594, "duration": 21901}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c964ed913dcaffe60107b228a13db00"}], "uid": "289f3b5c438a3754f1a28cd5c558a3e4"}, {"name": "test_go_home", "children": [{"name": "TestEllaGoHome", "children": [{"name": "测试go home能正常执行", "uid": "b082323455bd01a3", "parentUid": "0be9216ae99d7aa91cd2e638009124bc", "status": "passed", "time": {"start": 1755545168500, "stop": 1755545190680, "duration": 22180}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0be9216ae99d7aa91cd2e638009124bc"}], "uid": "16160dbe85fde9f730f84128fbb2c0e8"}, {"name": "test_go_to_office", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试go to office", "uid": "dcfc63b23aff804b", "parentUid": "7028d2f3448cec282ca8f3105e4c8f33", "status": "passed", "time": {"start": 1755545204884, "stop": 1755545227157, "duration": 22273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7028d2f3448cec282ca8f3105e4c8f33"}], "uid": "504bbde356e3f9fa2a477910d2015eb0"}, {"name": "test_gold_coin_rain", "children": [{"name": "TestEllaGoldCoinRain", "children": [{"name": "测试gold coin rain能正常执行", "uid": "7a71af2e1e53eaf4", "parentUid": "598fb3f60bb3b81f588b4e3deb106ffb", "status": "passed", "time": {"start": 1755545241022, "stop": 1755545265610, "duration": 24588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "598fb3f60bb3b81f588b4e3deb106ffb"}], "uid": "1457f71492e606ab225ab159042c5411"}, {"name": "test_hamster_mascot", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "397b1501c16d5e75", "parentUid": "89e8e9868beeeb07fc0e0fbe2be4a9d0", "status": "failed", "time": {"start": 1755545279615, "stop": 1755545304641, "duration": 25026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "89e8e9868beeeb07fc0e0fbe2be4a9d0"}], "uid": "d3bf8265accab30a46b7d75c686f2a31"}, {"name": "test_happy_new_year", "children": [{"name": "TestEllaHappyNewYear", "children": [{"name": "测试happy new year能正常执行", "uid": "9cecfbff8b58d513", "parentUid": "451251d367048f9c53b98847c2e447f2", "status": "passed", "time": {"start": 1755545319005, "stop": 1755545343640, "duration": 24635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "451251d367048f9c53b98847c2e447f2"}], "uid": "20bc3f1296c161aa14a0b88cee103de3"}, {"name": "test_hello_hello", "children": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [{"name": "测试hello hello能正常执行", "uid": "204d2c40a3295f49", "parentUid": "c4bd0dc493936c3118cffb708b15bf23", "status": "passed", "time": {"start": 1755545357716, "stop": 1755545383139, "duration": 25423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c4bd0dc493936c3118cffb708b15bf23"}], "uid": "1dcfccb9f26aec07115507bd73d282ca"}, {"name": "test_help_generate_a_picture_of_ancient_city", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试A furry little monkey", "uid": "626820f2f317f7d6", "parentUid": "617ef942b40bce41f954f46e9fb75b28", "status": "passed", "time": {"start": 1755545397220, "stop": 1755545422399, "duration": 25179}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "617ef942b40bce41f954f46e9fb75b28"}], "uid": "badc3ee28f810a31dc67eac99bd95563"}, {"name": "test_help_me_generate_a_3D_rendered_picture_of_a_Song_style_palace_surrounded_by_auspicious_clouds_and_with_delicate_colors", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "uid": "d98e6b072f4e3cec", "parentUid": "a4f75e5d41a94e3d63ed48a31ea2fceb", "status": "passed", "time": {"start": 1755545436695, "stop": 1755545458640, "duration": 21945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a4f75e5d41a94e3d63ed48a31ea2fceb"}], "uid": "c71bb8bf79c76ba6e92b8198dd78783b"}, {"name": "test_help_me_generate_a_photo_of_a_fully_nude_boy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Help me generate a photo of a fully nude boy", "uid": "81efefb346d1b1cf", "parentUid": "46eb01687f7194f0aa1ede4453b7af2f", "status": "passed", "time": {"start": 1755545472694, "stop": 1755545495044, "duration": 22350}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46eb01687f7194f0aa1ede4453b7af2f"}], "uid": "21455ed3a8678b7521db5034330e1df0"}, {"name": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of a bamboo forest stream", "uid": "3177d325c06c0ced", "parentUid": "7235901e69e3e99f7e0269602086d57b", "status": "passed", "time": {"start": 1755545509327, "stop": 1755545531209, "duration": 21882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7235901e69e3e99f7e0269602086d57b"}], "uid": "432a7b9e3eb1e42d010197fa3a645d7d"}, {"name": "test_help_me_generate_a_picture_of_a_puppy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of a puppy", "uid": "3ad46c4247b74393", "parentUid": "86e19d5e96892c826e84d8b4f165e88b", "status": "passed", "time": {"start": 1755545545131, "stop": 1755545567100, "duration": 21969}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "86e19d5e96892c826e84d8b4f165e88b"}], "uid": "74d2cae50a42f7145e4e4b11e8372631"}, {"name": "test_help_me_generate_a_picture_of_a_white_facial_cleanser", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of a white facial cleanser product advertisement", "uid": "29e79e9a23da4107", "parentUid": "04904c74713fbbad0495c2bb13fcd7bc", "status": "passed", "time": {"start": 1755545581193, "stop": 1755545603404, "duration": 22211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04904c74713fbbad0495c2bb13fcd7bc"}], "uid": "ed4a6606acb5889ffcaccac061b5ec70"}, {"name": "test_help_me_generate_a_picture_of_an_airplane", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of an airplane", "uid": "c8a10778742f62b7", "parentUid": "350cc6026a63aa12d21138a55d4a944e", "status": "passed", "time": {"start": 1755545617350, "stop": 1755545639385, "duration": 22035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350cc6026a63aa12d21138a55d4a944e"}], "uid": "ce69731a5b7520de7d6d0d235ca73019"}, {"name": "test_help_me_generate_a_picture_of_an_elegant_girl", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of an elegant girl", "uid": "f034caa98e78f0aa", "parentUid": "c76f0161c593c58a77f29aa535b848f6", "status": "passed", "time": {"start": 1755545653284, "stop": 1755545675171, "duration": 21887}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c76f0161c593c58a77f29aa535b848f6"}], "uid": "8cfa3a7cbedd07867ed7e67051d61ea1"}, {"name": "test_help_me_generate_a_picture_of_blue_and_gold_landscape", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of blue and gold landscape", "uid": "6fceb27a8985fb17", "parentUid": "12bdb84f77f4cd269a0edb704bb2c41e", "status": "passed", "time": {"start": 1755545689268, "stop": 1755545711354, "duration": 22086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12bdb84f77f4cd269a0edb704bb2c41e"}], "uid": "27911ac7aa937e69af3ae5cec3f495ad"}, {"name": "test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "uid": "dfd3fac825d35b2e", "parentUid": "b95077add041816039776c9c79f08dab", "status": "passed", "time": {"start": 1755545725296, "stop": 1755545747340, "duration": 22044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b95077add041816039776c9c79f08dab"}], "uid": "25729f449eba0009ff64c9a935b1d90a"}, {"name": "test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "uid": "a764398584dbd337", "parentUid": "8cda819f631bec98cb0554769e15d776", "status": "passed", "time": {"start": 1755545761411, "stop": 1755545783561, "duration": 22150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8cda819f631bec98cb0554769e15d776"}], "uid": "7f1f01d7c47311473fedb469caac8d5b"}, {"name": "test_help_me_write_an_email", "children": [{"name": "TestEllaHelpMeWriteAnEmail", "children": [{"name": "测试help me write an email能正常执行", "uid": "4b6b0366475ba56c", "parentUid": "649615446ede0148c5ff8678d523d0aa", "status": "passed", "time": {"start": 1755545797449, "stop": 1755545821691, "duration": 24242}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649615446ede0148c5ff8678d523d0aa"}], "uid": "4c50426c37e2427cf10466a61f135134"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "TestEllaHelpMeWriteAnThanksEmail", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "a5616c43d872369b", "parentUid": "532261b0e09523c5a2a4e366e160f5fd", "status": "failed", "time": {"start": 1755545835689, "stop": 1755545861121, "duration": 25432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "532261b0e09523c5a2a4e366e160f5fd"}], "uid": "e38c35bd53b03a201c55a53b3d55b4b0"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "TestEllaHelpMeWriteAnThanksLetter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "2cf73ad699817a37", "parentUid": "3755340db0d802a9e3906271e22e82ca", "status": "passed", "time": {"start": 1755545875017, "stop": 1755545900251, "duration": 25234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3755340db0d802a9e3906271e22e82ca"}], "uid": "407f54fb38fda3eea5b61bfdc186311a"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "TestEllaHowSetScreenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "1e00a46747da8d89", "parentUid": "4862cd77fa6584ab1cd401dd18e576cf", "status": "passed", "time": {"start": 1755545914505, "stop": 1755545936269, "duration": 21764}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4862cd77fa6584ab1cd401dd18e576cf"}], "uid": "3af4e120133a0419124a1c704c7d3199"}, {"name": "test_i_am_your_voice_assistant", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试i am your voice assistant", "uid": "6933b6fb12ae346d", "parentUid": "5d2ab4c795e8e992902489a882020682", "status": "failed", "time": {"start": 1755545950033, "stop": 1755545975296, "duration": 25263}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d2ab4c795e8e992902489a882020682"}], "uid": "f320a30996f91af0099875f8919f7787"}, {"name": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "children": [{"name": "TestEllaIThinkScreenIsBitDarkNowCouldYouPleaseHelpMeBrightenItUp", "children": [{"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "uid": "5b7b43a0a224ef94", "parentUid": "a80246cd874d1d57e931db7753af0377", "status": "passed", "time": {"start": 1755545989614, "stop": 1755546011887, "duration": 22273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a80246cd874d1d57e931db7753af0377"}], "uid": "fb4bb829272da3d339886c8bd1b5ae5e"}, {"name": "test_i_wanna_use_sim", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open settings", "uid": "10d2f5628c8cbadc", "parentUid": "2a419ab3371bc38f5b2db4be2fd53c93", "status": "passed", "time": {"start": 1755546025986, "stop": 1755546061293, "duration": 35307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a419ab3371bc38f5b2db4be2fd53c93"}], "uid": "fdf2c14fc9f40378813bb3e4136ca94e"}, {"name": "test_i_want_make_a_video_call_to", "children": [{"name": "TestEllaIWantMakeVideoCall", "children": [{"name": "测试i want make a video call to能正常执行", "uid": "1a5d810bd216a785", "parentUid": "3f01df61d110583696a88a3a43661649", "status": "failed", "time": {"start": 1755546074978, "stop": 1755546105867, "duration": 30889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f01df61d110583696a88a3a43661649"}], "uid": "0c92e589d91f05a13b14f2396a36bdde"}, {"name": "test_i_want_to_hear_a_joke", "children": [{"name": "TestEllaIWantHearJoke", "children": [{"name": "测试i want to hear a joke能正常执行", "uid": "3ff29417a9ae6010", "parentUid": "83ddf078970bc87273baff7e50a7c081", "status": "passed", "time": {"start": 1755546120102, "stop": 1755546145733, "duration": 25631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83ddf078970bc87273baff7e50a7c081"}], "uid": "ab7735228fa80d7187cbe6c8dc24d15e"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "TestEllaIncreaseSettingsSpecialFunctions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "ff5d7bb4f38f240e", "parentUid": "47b335b99ed8a1ee8475ca4a6834e17a", "status": "passed", "time": {"start": 1755546159919, "stop": 1755546182369, "duration": 22450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47b335b99ed8a1ee8475ca4a6834e17a"}], "uid": "c3601014aaa999aff69c5872e32743b7"}, {"name": "test_install_whatsapp", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试install whatsapp", "uid": "4c621b6aa0134d82", "parentUid": "4bb5c66d7902d2096048251bb5599fd1", "status": "passed", "time": {"start": 1755546196098, "stop": 1755546224342, "duration": 28244}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4bb5c66d7902d2096048251bb5599fd1"}], "uid": "1050a857b984dfc8f5dd02e17ced12e6"}, {"name": "test_it_wears_a_red_leather_collar", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试it wears a red leather collar", "uid": "bf798a6e3d70daa5", "parentUid": "a67d56cab2fca2f70165326d952b1b1f", "status": "passed", "time": {"start": 1755546238351, "stop": 1755546264045, "duration": 25694}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a67d56cab2fca2f70165326d952b1b1f"}], "uid": "d0f916c130abce5deccb76c29a9cf970"}, {"name": "test_it_wears_a_yellow_leather_collar", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试it wears a yellow leather collar", "uid": "5dee92077c27d125", "parentUid": "0e4f5983194e64cacb58a2a70581c832", "status": "passed", "time": {"start": 1755546278113, "stop": 1755546303687, "duration": 25574}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0e4f5983194e64cacb58a2a70581c832"}], "uid": "288c4bf4d90e6c97e65f53a7b6e8ca68"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "TestEllaJumpAdaptiveBrightnessSettings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "344438313a655b16", "parentUid": "f92d66f2cfb1510e3614f32014b90cdf", "status": "passed", "time": {"start": 1755546317774, "stop": 1755546339813, "duration": 22039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f92d66f2cfb1510e3614f32014b90cdf"}], "uid": "eda1255cba29f83ff1461094841f0f40"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "TestEllaJumpAiWallpaperGeneratorSettings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "303d6a001e695137", "parentUid": "508a56999eea40b3b6eae200090d9ef7", "status": "passed", "time": {"start": 1755546353628, "stop": 1755546375896, "duration": 22268}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "508a56999eea40b3b6eae200090d9ef7"}], "uid": "952ea4e1b05a29ce2f17032769862fd2"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "TestEllaJumpAutoRotateScreenSettings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "97dab4c826b8251a", "parentUid": "923f4dfcc1ff430310299406df4a76ad", "status": "passed", "time": {"start": 1755546389967, "stop": 1755546411968, "duration": 22001}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "923f4dfcc1ff430310299406df4a76ad"}], "uid": "2885405d4e301681acf1f9073b75196b"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "TestEllaJumpBatteryPowerSaving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "11c915e41556cea3", "parentUid": "7b0af410a8bc7729423b66dbd86394a1", "status": "passed", "time": {"start": 1755546425673, "stop": 1755546447711, "duration": 22038}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0af410a8bc7729423b66dbd86394a1"}], "uid": "5e4b9eee9fae8ca49a205b0e08671105"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "TestEllaJumpBatteryUsage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "f005f18c11305148", "parentUid": "e977b1d095b1f986aff6226776ded3c3", "status": "passed", "time": {"start": 1755546461408, "stop": 1755546483763, "duration": 22355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e977b1d095b1f986aff6226776ded3c3"}], "uid": "379854b415d26ca0952c7565049546e2"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "TestEllaJumpCallNotifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "8fbb8cd52efa5bba", "parentUid": "e1d634c784c8a3eaf84c787e520a993c", "status": "passed", "time": {"start": 1755546497907, "stop": 1755546528865, "duration": 30958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1d634c784c8a3eaf84c787e520a993c"}], "uid": "0d6d5e9fa27b112c5e4a5c892fa26625"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "TestEllaJumpHighBrightnessModeSettings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "644d8cb140f62e0a", "parentUid": "968c23cffad77650f73adb57ed35e44e", "status": "passed", "time": {"start": 1755546542657, "stop": 1755546564878, "duration": 22221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968c23cffad77650f73adb57ed35e44e"}], "uid": "f96efa82b36a271078aa933d212ff1e1"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "8abf1f1d22e91cdf", "parentUid": "6e6a65b648407dd3ea18fb96b915dace", "status": "passed", "time": {"start": 1755546578584, "stop": 1755546601052, "duration": 22468}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e6a65b648407dd3ea18fb96b915dace"}], "uid": "a57e12d4c6edf0c5f1e60f981492cd0f"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试jump to nfc settings", "uid": "715c7de84e96b864", "parentUid": "400a6941e3389b1b48d77dfa263077fe", "status": "passed", "time": {"start": 1755546614903, "stop": 1755546643114, "duration": 28211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "400a6941e3389b1b48d77dfa263077fe"}], "uid": "364aacfa906bc91f673d917736ae37e5"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "TestEllaJumpNotificationsStatusBarSettings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "8948c2eff36606c6", "parentUid": "5891939ff40959b2d8e13bfa76678a88", "status": "passed", "time": {"start": 1755546657406, "stop": 1755546679914, "duration": 22508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5891939ff40959b2d8e13bfa76678a88"}], "uid": "843f8e4d40a46af764e0186551ea2eab"}, {"name": "test_kill_whatsapp", "children": [{"name": "TestEllaKillWhatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "cf36fb78559bcbb3", "parentUid": "1c8d4c3e4504945ca2c7aa8aa790b4ab", "status": "passed", "time": {"start": 1755546694143, "stop": 1755546717481, "duration": 23338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c8d4c3e4504945ca2c7aa8aa790b4ab"}], "uid": "f5b7dfc982ee4029db9ba755d4b0c849"}, {"name": "test_kinkaku_ji", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Kinkaku-ji", "uid": "887f67141ee95c72", "parentUid": "8cf457f2ecb53ef87b31773bfc3fbc6b", "status": "passed", "time": {"start": 1755546731984, "stop": 1755546757835, "duration": 25851}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8cf457f2ecb53ef87b31773bfc3fbc6b"}], "uid": "b1bedc63289670232d638d0d405ce266"}, {"name": "test_make_a_call_by_whatsapp", "children": [{"name": "TestEllaMakeCallWhatsapp", "children": [{"name": "测试make a call by whatsapp能正常执行", "uid": "9890369f3896e4c", "parentUid": "6ae16d98ea4a978463257ee627b456ad", "status": "passed", "time": {"start": 1755546772083, "stop": 1755546803639, "duration": 31556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ae16d98ea4a978463257ee627b456ad"}], "uid": "6db9b6e7242da372327bdb47a2414f75"}, {"name": "test_make_a_call_on_whatsapp_to_a", "children": [{"name": "TestEllaMakeCallWhatsapp", "children": [{"name": "测试make a call on whatsapp to a能正常执行", "uid": "673fedbb294a6f1c", "parentUid": "593d06fe46d94082261aada55e48e76f", "status": "passed", "time": {"start": 1755546817824, "stop": 1755546849811, "duration": 31987}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "593d06fe46d94082261aada55e48e76f"}], "uid": "f51881f7a51f8cf69974b186fe0415ac"}, {"name": "test_make_a_phone_call_to_17621905233", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试make a phone call to 17621905233", "uid": "2665f4e98abb9b17", "parentUid": "8d299e7d03e17d084f401cd981238376", "status": "skipped", "time": {"start": 1755546851254, "stop": 1755546851254, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化')", "smoke"]}], "uid": "8d299e7d03e17d084f401cd981238376"}], "uid": "1f67773c8fb683ce8f601ccdb67d2731"}, {"name": "test_merry_christmas", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试merry christmas", "uid": "32a4642822207cf0", "parentUid": "47285da26e24914dcbdc0cc4da76f94b", "status": "failed", "time": {"start": 1755546864527, "stop": 1755546889045, "duration": 24518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47285da26e24914dcbdc0cc4da76f94b"}], "uid": "830feedd6312aaf2ef31fc5a483ef07f"}, {"name": "test_modify_grape_timbre", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "e10b6bdd9f53c2ea", "parentUid": "79b5d194845308e1383e35ab63ba394b", "status": "passed", "time": {"start": 1755546903675, "stop": 1755546925888, "duration": 22213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b5d194845308e1383e35ab63ba394b"}], "uid": "ff7fe79e49919f1d7ccc98346b480d21"}, {"name": "test_more_settings", "children": [{"name": "TestEllaMoreSettings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "a009973ed07360d8", "parentUid": "5158adca762a1833ee225b49be689b4b", "status": "passed", "time": {"start": 1755546940225, "stop": 1755546965932, "duration": 25707}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5158adca762a1833ee225b49be689b4b"}], "uid": "2678afdbcf3cd1c2f7304aab68dff13e"}, {"name": "test_navigate_to_the_address_on_the_screen", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Navigate to the address on the screen", "uid": "bff11b2846cb0bf2", "parentUid": "923f005ebf7c4a5e973a745babc94137", "status": "passed", "time": {"start": 1755546980454, "stop": 1755547012622, "duration": 32168}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "923f005ebf7c4a5e973a745babc94137"}], "uid": "a2fb8e15f5cae8757959e8482bc4755d"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "TestEllaNavigationAddressTheImage", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "c4a9a1ad677f0098", "parentUid": "bdbc8f989cddecd7a3ff8b9229b6e0b7", "status": "failed", "time": {"start": 1755547027098, "stop": 1755547058930, "duration": 31832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdbc8f989cddecd7a3ff8b9229b6e0b7"}], "uid": "2ea8810e61f48227b2b12d8788c78cfa"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "TestEllaNavigationFirstAddressImage", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "f252f9db319d4be8", "parentUid": "fc6a08afad859df9f5da8d1611e05a0e", "status": "broken", "time": {"start": 1755547073652, "stop": 1755547105068, "duration": 31416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fc6a08afad859df9f5da8d1611e05a0e"}], "uid": "f052d105eebf03aacc6d59b029ca356c"}, {"name": "test_new_year_wishes", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试new year wishes", "uid": "8dd63573fd58dbfa", "parentUid": "6d707f3e108bd7a51974e4e2397afde8", "status": "passed", "time": {"start": 1755547119803, "stop": 1755547144285, "duration": 24482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d707f3e108bd7a51974e4e2397afde8"}], "uid": "961bda73d2be820987dfe720dbe06f68"}, {"name": "test_new_year_wishs", "children": [{"name": "TestEllaNewYearWishs", "children": [{"name": "测试new year wishs能正常执行", "uid": "b8b38e493df50885", "parentUid": "73768b6c43f36d7ad5edde91f8c1e415", "status": "passed", "time": {"start": 1755547158710, "stop": 1755547183523, "duration": 24813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73768b6c43f36d7ad5edde91f8c1e415"}], "uid": "95ff601119aea5204c63d1095bfb4652"}, {"name": "test_open_camera", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open camera", "uid": "7f62336971efe5f8", "parentUid": "1b9691270eaeab1dddf2a5166c7d6276", "status": "passed", "time": {"start": 1755547197681, "stop": 1755547238974, "duration": 41293}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b9691270eaeab1dddf2a5166c7d6276"}], "uid": "3d80518374ca8633acca9dc138f42448"}, {"name": "test_open_font_family_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "68aca0b52f3a2e5e", "parentUid": "3abee97ea9125b112c9768709b230d6c", "status": "passed", "time": {"start": 1755547253335, "stop": 1755547275740, "duration": 22405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3abee97ea9125b112c9768709b230d6c"}], "uid": "c37be023aa1668dd5137d98115ed22ee"}, {"name": "test_open_maps", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open maps", "uid": "a1c4309236a8966a", "parentUid": "9b2b0152a79f484cf7ff400da9bf0224", "status": "passed", "time": {"start": 1755547289709, "stop": 1755547320995, "duration": 31286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b2b0152a79f484cf7ff400da9bf0224"}], "uid": "a1ed2a41018e67d39dd589a4ec2c1056"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "a6508523a9440c5d", "parentUid": "1547bf4ebc0f2064b90de7ee9d4b0d9a", "status": "passed", "time": {"start": 1755547335049, "stop": 1755547357658, "duration": 22609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1547bf4ebc0f2064b90de7ee9d4b0d9a"}], "uid": "e9bb1812204e529485c6767282c66ee4"}, {"name": "test_open_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open settings", "uid": "c76bb8231c514058", "parentUid": "1798b34a6d68e964f689776e603b40ae", "status": "passed", "time": {"start": 1755547371995, "stop": 1755547403917, "duration": 31922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1798b34a6d68e964f689776e603b40ae"}], "uid": "6fec943db24a17ffc89cb8435dbd34c7"}, {"name": "test_open_the_settings", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试open the settings", "uid": "87e4c8157a7c0b0a", "parentUid": "27526f65718c6d32e2ddafe8beec70f3", "status": "passed", "time": {"start": 1755547417706, "stop": 1755547450006, "duration": 32300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27526f65718c6d32e2ddafe8beec70f3"}], "uid": "8a0151ef426a4ec4845e49443ec2a111"}, {"name": "test_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试open whatsapp", "uid": "d907a6931b9bab45", "parentUid": "a86f67524fc901b9e239b41a55bcd168", "status": "failed", "time": {"start": 1755547463774, "stop": 1755547487298, "duration": 23524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a86f67524fc901b9e239b41a55bcd168"}], "uid": "134b51cd09c0f3f3f5664bfe08db4bfb"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaOrderBurger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "a12388ce0112ec8b", "parentUid": "caf4b95867e28da5919f2eca0a324167", "status": "failed", "time": {"start": 1755547501236, "stop": 1755547523724, "duration": 22488}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "caf4b95867e28da5919f2eca0a324167"}], "uid": "5866702b17345a70f73c15e82c35a889"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderTakeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "15dbd978c9e9ff3c", "parentUid": "bee4551900f0d6522500f0c802a47f62", "status": "passed", "time": {"start": 1755547537971, "stop": 1755547559923, "duration": 21952}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bee4551900f0d6522500f0c802a47f62"}], "uid": "d94ef1da6fdbed2a2a316ba39ec7f816"}, {"name": "test_parking_space", "children": [{"name": "TestEllaParkingSpace", "children": [{"name": "测试parking space能正常执行", "uid": "c862ac6b8bc8ba94", "parentUid": "8002e65cdc68fa89adcbace4cab353af", "status": "passed", "time": {"start": 1755547574100, "stop": 1755547596332, "duration": 22232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8002e65cdc68fa89adcbace4cab353af"}], "uid": "91df28ab9c563eb71b7c27ebe03bd2a9"}, {"name": "test_play_carpenters_video", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play carpenters'video", "uid": "91c1ec3779acbc46", "parentUid": "c9ef97a1b6f2b67ae34377997b3e40a6", "status": "passed", "time": {"start": 1755547610797, "stop": 1755547637613, "duration": 26816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c9ef97a1b6f2b67ae34377997b3e40a6"}], "uid": "f47f53fad8ed96a4309bb6db6e7ae1ca"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play football video by youtube", "uid": "8daac15c9f9ea1a8", "parentUid": "f50015233729c567f6411101e9d7507e", "status": "passed", "time": {"start": 1755547651841, "stop": 1755547680054, "duration": 28213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f50015233729c567f6411101e9d7507e"}], "uid": "288230e0b3a90da164068733d1ce2f50"}, {"name": "test_play_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play love sotry", "uid": "78003ba8ba5d066c", "parentUid": "2eafce82b79f0069cbd0ea652014bbfb", "status": "passed", "time": {"start": 1755547694222, "stop": 1755547737272, "duration": 43050}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2eafce82b79f0069cbd0ea652014bbfb"}], "uid": "395956abc96532bb04cf74abc316a3ba"}, {"name": "test_play_music_by_Audiomack", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play music by Audiomack", "uid": "afa51a402f294990", "parentUid": "7f7895de67b743adcacabc0317321439", "status": "passed", "time": {"start": 1755547751251, "stop": 1755547776030, "duration": 24779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f7895de67b743adcacabc0317321439"}], "uid": "fee074ac550e0b9b4e5bea39a6ac4f8e"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "e61d8722a56d0e37", "parentUid": "369a78d30a3bb706690dc0f41716f29f", "status": "passed", "time": {"start": 1755547790139, "stop": 1755547831409, "duration": 41270}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "369a78d30a3bb706690dc0f41716f29f"}], "uid": "fee77f8c40bb5ae6f21bab13e0798786"}, {"name": "test_play_the_album", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play the album", "uid": "b0188f89da3d109f", "parentUid": "1cfc5343c5c88745c65dc82ee560dd78", "status": "passed", "time": {"start": 1755547845246, "stop": 1755547885666, "duration": 40420}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cfc5343c5c88745c65dc82ee560dd78"}], "uid": "58b703424f3437f68fd423d41eccfd46"}, {"name": "test_play_video", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video", "uid": "23aff1aca63900f9", "parentUid": "6e35a63e6264545436bbb6f84204721d", "status": "passed", "time": {"start": 1755547899549, "stop": 1755547927800, "duration": 28251}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e35a63e6264545436bbb6f84204721d"}], "uid": "0a6d47b585a70d59df4457bfeb32ef4b"}, {"name": "test_play_video_by_youtube", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试play video by youtube", "uid": "cf9f2c5a2ad8b9eb", "parentUid": "87ac2f2511f41462750c26dcbc17f7d7", "status": "passed", "time": {"start": 1755547941987, "stop": 1755547970029, "duration": 28042}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "87ac2f2511f41462750c26dcbc17f7d7"}], "uid": "fe310a2b7e3c25a7a2ac071656c5ef12"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "TestEllaPleaseShowMeWhereIAm", "children": [{"name": "测试please show me where i am能正常执行", "uid": "79010a10fe7f461d", "parentUid": "9fd87d8d098fefc38dbf9094b5c04473", "status": "failed", "time": {"start": 1755547984334, "stop": 1755548008569, "duration": 24235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9fd87d8d098fefc38dbf9094b5c04473"}], "uid": "476b6def0b2277a73bf0dfbcd5b6047b"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "TestEllaOpenWhatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "1d166cd5bb89f599", "parentUid": "f606de62a17219d1049f95130d391334", "status": "failed", "time": {"start": 1755548023055, "stop": 1755548046986, "duration": 23931}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f606de62a17219d1049f95130d391334"}], "uid": "71d89bf7be06a8672fe794f8b289d664"}, {"name": "test_power_off_my_phone", "children": [{"name": "TestEllaPowerOffMyPhone", "children": [{"name": "测试power off my phone能正常执行", "uid": "ecbca05a49966cd4", "parentUid": "669f2c441081b8047f110fce6da0af5b", "status": "skipped", "time": {"start": 1755548048698, "stop": 1755548048698, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "669f2c441081b8047f110fce6da0af5b"}], "uid": "9ed6b03efdd1270108dae1c5824cfbcd"}, {"name": "test_privacy_policy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试privacy policy", "uid": "304d1991e7b7338d", "parentUid": "2d4923b35199be73f82b3fc099b5a6ce", "status": "passed", "time": {"start": 1755548061813, "stop": 1755548084125, "duration": 22312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d4923b35199be73f82b3fc099b5a6ce"}], "uid": "4031d737f80326aa10894ca653ad541f"}, {"name": "test_puppy", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试puppy", "uid": "e1f427e407268742", "parentUid": "0d8eb9f405316ff1fca2e6ef8f4175bb", "status": "passed", "time": {"start": 1755548098626, "stop": 1755548124639, "duration": 26013}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d8eb9f405316ff1fca2e6ef8f4175bb"}], "uid": "db40a72c20905024ab55fba25dd6e0e5"}, {"name": "test_reboot_my_phone", "children": [{"name": "TestEllaRebootMyPhone", "children": [{"name": "测试reboot my phone能正常执行", "uid": "474f128d73576157", "parentUid": "074f07bf2e66f00df5ce600265963bda", "status": "skipped", "time": {"start": 1755548126145, "stop": 1755548126145, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='reboot 会导致设备断开，先跳过')", "smoke"]}], "uid": "074f07bf2e66f00df5ce600265963bda"}], "uid": "f04e46f8e511a670eda47336976c0900"}, {"name": "test_redial", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试redial", "uid": "110bca580595cc26", "parentUid": "73134362df5e82df9dbacec436fd51cc", "status": "skipped", "time": {"start": 1755548126150, "stop": 1755548126150, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='redial命令需要先拨打电话，才能重拨，无法通用化')", "smoke"]}], "uid": "73134362df5e82df9dbacec436fd51cc"}], "uid": "ad6624cb14424d8194382f0f58d7848c"}, {"name": "test_remember_the_parking_lot", "children": [{"name": "TestEllaRememberParkingLot", "children": [{"name": "测试remember the parking lot能正常执行", "uid": "f0e19a1d22249e58", "parentUid": "1bd4a56d7fec08386ad6aab127a34043", "status": "passed", "time": {"start": 1755548139197, "stop": 1755548161261, "duration": 22064}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1bd4a56d7fec08386ad6aab127a34043"}], "uid": "46f3ca535feecdbe2f37236d2c699570"}, {"name": "test_remember_the_parking_space", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试remember the parking space", "uid": "bc289b8805ed6f47", "parentUid": "d9577f276870a2e1accd8b1252b061f4", "status": "failed", "time": {"start": 1755548175424, "stop": 1755548197727, "duration": 22303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9577f276870a2e1accd8b1252b061f4"}], "uid": "39d9463583101a130b62d5abf9ec5ec6"}, {"name": "test_remove_the_people_from_the_image", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试remove the people from the image", "uid": "617d260b52b35e2d", "parentUid": "b7dbc6442cf1571b90e4e077de3d9dae", "status": "passed", "time": {"start": 1755548212267, "stop": 1755548239506, "duration": 27239}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7dbc6442cf1571b90e4e077de3d9dae"}], "uid": "bc43d87bf1e9e65f9dbdebcea5354a16"}, {"name": "test_reset_phone", "children": [{"name": "TestEllaResetPhone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "c0bcef3f87616eb5", "parentUid": "cac15668a449dfa5284ac8751a8bc088", "status": "skipped", "time": {"start": 1755548240955, "stop": 1755548240955, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='reset phone 会导致设备断开，先跳过')"]}], "uid": "cac15668a449dfa5284ac8751a8bc088"}], "uid": "20f77fb14c171a00344aefe112819e8f"}, {"name": "test_restart_my_phone", "children": [{"name": "TestEllaRestartMyPhone", "children": [{"name": "测试restart my phone能正常执行", "uid": "ec3f00031eaf4371", "parentUid": "8e6b8a6f5b7ace7e38fe72173c083641", "status": "skipped", "time": {"start": 1755548240961, "stop": 1755548240961, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "8e6b8a6f5b7ace7e38fe72173c083641"}], "uid": "37ab7708b333614b029b8e7dbb1544ed"}, {"name": "test_restart_the_phone", "children": [{"name": "TestEllaRestartPhone", "children": [{"name": "测试restart the phone能正常执行", "uid": "9bc8f2567daac5d0", "parentUid": "3774bd71fb27e3ff5098f87b6107b67c", "status": "skipped", "time": {"start": 1755548240965, "stop": 1755548240965, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "3774bd71fb27e3ff5098f87b6107b67c"}], "uid": "8520ec26c958caa18d4558c5d16d58cd"}, {"name": "test_running_on_the_grass", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试running on the grass", "uid": "15b8865ebef84c4", "parentUid": "44a906216f4c6a2414f6344601024496", "status": "failed", "time": {"start": 1755548253869, "stop": 1755548281183, "duration": 27314}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44a906216f4c6a2414f6344601024496"}], "uid": "e5f3c6c2323a2333a15185788e22874b"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "TestEllaSearchAddressesScreen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "c7c9715779136ab0", "parentUid": "4c52013229be65cab9dbe8706e15af5a", "status": "passed", "time": {"start": 1755548295766, "stop": 1755548321953, "duration": 26187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c52013229be65cab9dbe8706e15af5a"}], "uid": "c2898d22ab26a5d42aa6bad2edebff1b"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "TestEllaSearchAddressImage", "children": [{"name": "测试search the address in the image能正常执行", "uid": "7d126021ffb45811", "parentUid": "c189e03b4adbf1ffd08ccde0c003cbf1", "status": "passed", "time": {"start": 1755548336283, "stop": 1755548362375, "duration": 26092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c189e03b4adbf1ffd08ccde0c003cbf1"}], "uid": "18c4911f1477f7e8b52e7c52b7206918"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "TestEllaSearchWhatsappMe", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "aafe14e472f370a3", "parentUid": "cd5851bdca5beda41724955a20608d69", "status": "passed", "time": {"start": 1755548376434, "stop": 1755548404737, "duration": 28303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd5851bdca5beda41724955a20608d69"}], "uid": "000d1f465ed860bdf1ed56e67ee1ae93"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "TestEllaSearchingMethodViolentMurder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "a978d43a69fdb4a4", "parentUid": "ce100ec49c328fc7bcaa8647cea22b48", "status": "passed", "time": {"start": 1755548418679, "stop": 1755548444543, "duration": 25864}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ce100ec49c328fc7bcaa8647cea22b48"}], "uid": "be41a396acb219f73e244b463a77b9e4"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "TestEllaSendMyRecentPhotosMomThroughWhatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "bc58b8085ad08e08", "parentUid": "7a8b4afc50f4eff1f1bfb333ef0634a3", "status": "passed", "time": {"start": 1755548458658, "stop": 1755548481293, "duration": 22635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7a8b4afc50f4eff1f1bfb333ef0634a3"}], "uid": "a815543064692fc926dec55bba6ffd8f"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "TestEllaSetAppAutoRotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "2aaf5347dbc01d8b", "parentUid": "5ec31d73ee5a8cbd16b741e2a0c12725", "status": "passed", "time": {"start": 1755548495332, "stop": 1755548517734, "duration": 22402}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ec31d73ee5a8cbd16b741e2a0c12725"}], "uid": "c4e1b97dbe16d5e25caab90c80f85213"}, {"name": "test_set_app_notifications", "children": [{"name": "TestEllaSetAppNotifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "454a02f184d6e886", "parentUid": "e2b37df150554a2656df5f000b5f3877", "status": "passed", "time": {"start": 1755548531993, "stop": 1755548554414, "duration": 22421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2b37df150554a2656df5f000b5f3877"}], "uid": "c477cad1f43ef2333b7849edf5ce05ed"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "TestEllaSetBatterySaverSettings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "7fb3652513992510", "parentUid": "c420f6bca101c62ba94f9e8e7f31a573", "status": "passed", "time": {"start": 1755548568717, "stop": 1755548591450, "duration": 22733}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c420f6bca101c62ba94f9e8e7f31a573"}], "uid": "8b4c47455f601b3d7d6f1e100772a44a"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "TestEllaSetCallBackLastUsedSim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "fe76b8e3bc1750b5", "parentUid": "4c0f190c6f4aee497792a6d856a0035a", "status": "passed", "time": {"start": 1755548605576, "stop": 1755548637601, "duration": 32025}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c0f190c6f4aee497792a6d856a0035a"}], "uid": "7cea4873dfe6764b25d4b6368eec9cfb"}, {"name": "test_set_color_style", "children": [{"name": "TestEllaSetColorStyle", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "8e7863fa9e0516bc", "parentUid": "a93fd41464aaeca8c5187ded9998a8cf", "status": "passed", "time": {"start": 1755548651804, "stop": 1755548674233, "duration": 22429}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93fd41464aaeca8c5187ded9998a8cf"}], "uid": "ba0686e84222e058748253df8047a50b"}, {"name": "test_set_compatibility_mode", "children": [{"name": "TestEllaSetCompatibilityMode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "9f87c3103add02e5", "parentUid": "3b4b879cb25f4ec1c9e5f56f7b979c37", "status": "passed", "time": {"start": 1755548688485, "stop": 1755548710878, "duration": 22393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b4b879cb25f4ec1c9e5f56f7b979c37"}], "uid": "1909cdae019ead080ae02a578b4d7316"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "TestEllaSetCoverScreenApps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "f0bc2546728b57be", "parentUid": "37a4a9b94837c06f3d0d560b86bbe29b", "status": "passed", "time": {"start": 1755548725100, "stop": 1755548747846, "duration": 22746}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "37a4a9b94837c06f3d0d560b86bbe29b"}], "uid": "5df35bd6f80d4c6657cbae8b27064aab"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "TestEllaSetCustomizedCoverScreen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "7d88c44af202bec", "parentUid": "4d3fece58e4f70510cd292befe33b96a", "status": "passed", "time": {"start": 1755548761809, "stop": 1755548784471, "duration": 22662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d3fece58e4f70510cd292befe33b96a"}], "uid": "3fa52a12d7acae5645ea3aa75477d44b"}, {"name": "test_set_date_time", "children": [{"name": "TestEllaSetDateTime", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "70ef09b5d0578ff", "parentUid": "9d950448d0ff30907a7af780f114a130", "status": "passed", "time": {"start": 1755548798623, "stop": 1755548820833, "duration": 22210}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d950448d0ff30907a7af780f114a130"}], "uid": "8c30598a8b1f5940667edde71b30791c"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "TestEllaSetEdgeMistouchPrevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "9671c04e652145d5", "parentUid": "dada83ec1ab33079d5f86d4ac7ee1f04", "status": "passed", "time": {"start": 1755548835228, "stop": 1755548857548, "duration": 22320}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dada83ec1ab33079d5f86d4ac7ee1f04"}], "uid": "a30de810322f2776f8c899c615c4be77"}, {"name": "test_set_flex_still_mode", "children": [{"name": "TestEllaSetFlexStillMode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "6463f34280457263", "parentUid": "a51d316b425964f68fef53807195a46f", "status": "passed", "time": {"start": 1755548872001, "stop": 1755548894307, "duration": 22306}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a51d316b425964f68fef53807195a46f"}], "uid": "0100b1b2aa4c49467859f43e6353bedb"}, {"name": "test_set_flip_case_feature", "children": [{"name": "TestEllaSetFlipCaseFeature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "9d79c15c7aa3b791", "parentUid": "d5b01634d272a358f06ff35ca4ca4a23", "status": "passed", "time": {"start": 1755548908478, "stop": 1755548930430, "duration": 21952}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5b01634d272a358f06ff35ca4ca4a23"}], "uid": "659c6f22cfed2953c4ba8d4b8dd7294a"}, {"name": "test_set_floating_windows", "children": [{"name": "TestEllaSetFloatingWindows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "83c1baf55df42d77", "parentUid": "e15e2e981396bcf4d57ba068a480c5b1", "status": "passed", "time": {"start": 1755548944420, "stop": 1755548966779, "duration": 22359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e15e2e981396bcf4d57ba068a480c5b1"}], "uid": "1dc2787970a8d9c589a8ae300f462858"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "TestEllaSetFoldingScreenZone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "86b9e6f5cf0f3680", "parentUid": "47c139cd34b0a780094ffb98e1812572", "status": "passed", "time": {"start": 1755548980760, "stop": 1755549003220, "duration": 22460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47c139cd34b0a780094ffb98e1812572"}], "uid": "45d2d7d6fad9b4da39cc6dac0c4fa337"}, {"name": "test_set_font_size", "children": [{"name": "TestEllaSetFontSize", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "cd2d1addc7608d8a", "parentUid": "80b6fcd56edd3e33d66094a25cc5d31b", "status": "passed", "time": {"start": 1755549017205, "stop": 1755549039403, "duration": 22198}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80b6fcd56edd3e33d66094a25cc5d31b"}], "uid": "9a3389ccf2eca5621def99072303502f"}, {"name": "test_set_gesture_navigation", "children": [{"name": "TestEllaSetGestureNavigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "4873813c4a892c87", "parentUid": "5a5a307b4aee14702733cda564e2594e", "status": "passed", "time": {"start": 1755549053787, "stop": 1755549081626, "duration": 27839}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a5a307b4aee14702733cda564e2594e"}], "uid": "f45d715ad98a0594e567c013238fb055"}, {"name": "test_set_languages", "children": [{"name": "TestEllaSetLanguages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "ea971f8afc8ac3d8", "parentUid": "3e28dcd9a9cdb251320f0e3b7e655a4d", "status": "passed", "time": {"start": 1755549095869, "stop": 1755549118203, "duration": 22334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e28dcd9a9cdb251320f0e3b7e655a4d"}], "uid": "eedc9f2088f9e7ed345a41dcf938b3aa"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "TestEllaSetLockscreenPasswords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "7bcfa670f3717f3c", "parentUid": "41dd05458efc06498e30ee8a35ffd98f", "status": "passed", "time": {"start": 1755549132314, "stop": 1755549154625, "duration": 22311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41dd05458efc06498e30ee8a35ffd98f"}], "uid": "62cf905659379d62ce72a50cba40e0f8"}, {"name": "test_set_my_fonts", "children": [{"name": "TestEllaSetMyFonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "f8218991466f15e9", "parentUid": "3feb62634faa8c2680759a67a0f0b2df", "status": "passed", "time": {"start": 1755549168756, "stop": 1755549191000, "duration": 22244}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3feb62634faa8c2680759a67a0f0b2df"}], "uid": "64427e107c50bb3a09e1b67c0bf1e141"}, {"name": "test_set_my_themes", "children": [{"name": "TestEllaSetMyThemes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "c98d6e623d24e658", "parentUid": "b86797063b9d865d350ec21ac7112b5f", "status": "passed", "time": {"start": 1755549205169, "stop": 1755549227729, "duration": 22560}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b86797063b9d865d350ec21ac7112b5f"}], "uid": "716eecd28b7d4b5ede25b5df0e5507c2"}, {"name": "test_set_nfc_tag", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试set nfc tag", "uid": "df6140718ba46854", "parentUid": "6762d305cbfc537c83db06768a9c4d2d", "status": "passed", "time": {"start": 1755549241914, "stop": 1755549271257, "duration": 29343}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6762d305cbfc537c83db06768a9c4d2d"}], "uid": "0e88e754c9726ef600c72680272f7029"}, {"name": "test_set_off_a_firework", "children": [{"name": "TestEllaSetOffFirework", "children": [{"name": "测试set off a firework能正常执行", "uid": "967ae3d31576809e", "parentUid": "021acb4eaaacdcf72d568b2312fa9bfc", "status": "passed", "time": {"start": 1755549285581, "stop": 1755549310614, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "021acb4eaaacdcf72d568b2312fa9bfc"}], "uid": "8425a95213c647a6dbc3c547777d6228"}, {"name": "test_set_parallel_windows", "children": [{"name": "TestEllaSetParallelWindows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "41af1b410102153b", "parentUid": "e9b410d4bf477a3418fde54f839d7e6d", "status": "passed", "time": {"start": 1755549324908, "stop": 1755549347327, "duration": 22419}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9b410d4bf477a3418fde54f839d7e6d"}], "uid": "55c6cd519ecfe75eb797fa2c66d0d75f"}, {"name": "test_set_personal_hotspot", "children": [{"name": "TestEllaSetPersonalHotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "b3fde257da3ffa32", "parentUid": "b3579038871378a6fdcc9bb20849bdf1", "status": "passed", "time": {"start": 1755549361790, "stop": 1755549384169, "duration": 22379}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b3579038871378a6fdcc9bb20849bdf1"}], "uid": "b482fd86e6dab754c48a5bf619de5afb"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "TestEllaSetPhantomVPen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "3c1d99d953a261c6", "parentUid": "7c5353b9ef36136e92a92708b7e45bb3", "status": "passed", "time": {"start": 1755549398624, "stop": 1755549421021, "duration": 22397}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c5353b9ef36136e92a92708b7e45bb3"}], "uid": "107e8fe279ca1fd2b153352fda86fb4a"}, {"name": "test_set_phone_number", "children": [{"name": "TestEllaSetPhoneNumber", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "c76ab9470c51fe56", "parentUid": "96b22c407c943460e8486fc825169a80", "status": "passed", "time": {"start": 1755549435535, "stop": 1755549457878, "duration": 22343}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96b22c407c943460e8486fc825169a80"}], "uid": "e9cd1fa5db40d2ec604808d9c497fadb"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "TestEllaSetScheduledPowerOffRestart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "dea6a036feb878e0", "parentUid": "58b82848dae0adadd98c95016e0b8711", "status": "passed", "time": {"start": 1755549472460, "stop": 1755549495150, "duration": 22690}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58b82848dae0adadd98c95016e0b8711"}], "uid": "e6b0ed51c0d597afdb5bf8c4e6af53c3"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "TestEllaSetScreenRefreshRate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "d7139503c14d5b2d", "parentUid": "3e9009cde7fd47070b3529a63de695c3", "status": "passed", "time": {"start": 1755549509614, "stop": 1755549531578, "duration": 21964}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e9009cde7fd47070b3529a63de695c3"}], "uid": "c9de8a50bcaa3dc3fd1b3a9409aa8ba0"}, {"name": "test_set_screen_relay", "children": [{"name": "TestEllaSetScreenRelay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "2b5c5da56c75ff4", "parentUid": "61fe1e0731921d580561f77ccf2de7a2", "status": "passed", "time": {"start": 1755549546096, "stop": 1755549568423, "duration": 22327}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "61fe1e0731921d580561f77ccf2de7a2"}], "uid": "7ebda1dd475915217421f499f80df8c5"}, {"name": "test_set_screen_timeout", "children": [{"name": "TestEllaSetScreenTimeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "e1839257fa3f6667", "parentUid": "3f7e6a161af6de02774a99275b95e1d7", "status": "passed", "time": {"start": 1755549582878, "stop": 1755549605151, "duration": 22273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f7e6a161af6de02774a99275b95e1d7"}], "uid": "3ad3475cd36fab63233e05c3002bb8c5"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "TestEllaSetScreenMinimumBrightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "6eb43be3ca854b83", "parentUid": "70cd5196189a628ba70f4e189ab3ed2a", "status": "passed", "time": {"start": 1755549619480, "stop": 1755549641974, "duration": 22494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "70cd5196189a628ba70f4e189ab3ed2a"}], "uid": "ccff2bc4a244de015f51207ad9fb9973"}, {"name": "test_set_sim_ringtone", "children": [{"name": "TestEllaSetSimRingtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "29f8078765071560", "parentUid": "b540694383c716efd22be6cf332cff66", "status": "passed", "time": {"start": 1755549656313, "stop": 1755549678793, "duration": 22480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b540694383c716efd22be6cf332cff66"}], "uid": "f521b50083932bc320d541b59f4fa12e"}, {"name": "test_set_smart_hub", "children": [{"name": "TestEllaSetSmartHub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "16fa33ac9f4fc28a", "parentUid": "9982a7cb76e7edde540c33f77bcb4131", "status": "passed", "time": {"start": 1755549693192, "stop": 1755549715674, "duration": 22482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9982a7cb76e7edde540c33f77bcb4131"}], "uid": "9159365ae1f13aa7202a2ecabae352cd"}, {"name": "test_set_smart_panel", "children": [{"name": "TestEllaSetSmartPanel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "9401f2aafc72cbb6", "parentUid": "8186149225876d2c7f48b35cdec99dd3", "status": "passed", "time": {"start": 1755549729979, "stop": 1755549752409, "duration": 22430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8186149225876d2c7f48b35cdec99dd3"}], "uid": "6d2c866f4f10ac8e5f2e0399839eca9f"}, {"name": "test_set_special_function", "children": [{"name": "TestEllaSetSpecialFunction", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "d4033ac84f856cd7", "parentUid": "a485e9dc82964f874c4ca66999bcf15a", "status": "passed", "time": {"start": 1755549766920, "stop": 1755549789346, "duration": 22426}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a485e9dc82964f874c4ca66999bcf15a"}], "uid": "1385459e098e23ca404584fba544f87b"}, {"name": "test_set_split_screen_apps", "children": [{"name": "TestEllaSetSplitScreenApps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "b0523e40835680c9", "parentUid": "e704a416305aeb7b8861f78da3d399fd", "status": "passed", "time": {"start": 1755549803696, "stop": 1755549826120, "duration": 22424}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e704a416305aeb7b8861f78da3d399fd"}], "uid": "18aa5f1d09e55e245cdba84af89bdb4f"}, {"name": "test_set_timer", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试set timer", "uid": "1ee3a2d6d4b65ad", "parentUid": "709eb7cf5d71d5a191206f8c5fde3417", "status": "passed", "time": {"start": 1755549840641, "stop": 1755549867778, "duration": 27137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "709eb7cf5d71d5a191206f8c5fde3417"}], "uid": "048485ad7321fdc8f5337562645b0668"}, {"name": "test_set_timezone", "children": [{"name": "TestEllaSetTimezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "19693adbe0f7f612", "parentUid": "f68e969f0dc6fa17bc3cbb1c76e0468d", "status": "passed", "time": {"start": 1755549882185, "stop": 1755549904488, "duration": 22303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f68e969f0dc6fa17bc3cbb1c76e0468d"}], "uid": "6b5e2d316297d46c06b128e02645ba18"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "TestEllaSetUltraPowerSaving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "dde17385f415640f", "parentUid": "c853a846bf2e6118074af640a3bdfded", "status": "passed", "time": {"start": 1755549918832, "stop": 1755549941568, "duration": 22736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c853a846bf2e6118074af640a3bdfded"}], "uid": "516f9e12e9fe59099bceb66c299b4b06"}, {"name": "test_start_boosting_phone", "children": [{"name": "TestEllaStartBoostingPhone", "children": [{"name": "测试start boosting phone能正常执行", "uid": "685452b2ba4f9a89", "parentUid": "ea72904cc48cdab04b6a32328bcff936", "status": "passed", "time": {"start": 1755549955814, "stop": 1755549977337, "duration": 21523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ea72904cc48cdab04b6a32328bcff936"}], "uid": "4dfd3dff348daec46a2840ec95737d64"}, {"name": "test_start_running", "children": [{"name": "TestEllaStartRunning", "children": [{"name": "测试start running能正常执行", "uid": "711abadef0237b83", "parentUid": "a24fb40874550e9588bb7a1e759255e9", "status": "passed", "time": {"start": 1755549991489, "stop": 1755550021349, "duration": 29860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a24fb40874550e9588bb7a1e759255e9"}], "uid": "d46bf7e5557e0738a865ecc07e2d584c"}, {"name": "test_start_walking", "children": [{"name": "TestEllaStartWalking", "children": [{"name": "测试start walking能正常执行", "uid": "8b77159b32025374", "parentUid": "c6ec8ec07b5dd2102a991e8a20680e71", "status": "skipped", "time": {"start": 1755550022696, "stop": 1755550022696, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='start walking命令都会开Health应用，才能执行，无法通用化')", "smoke"]}], "uid": "c6ec8ec07b5dd2102a991e8a20680e71"}], "uid": "a4e7eb3a60d774ac67cd2cc111cb9845"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试summarize content on this page", "uid": "a8d288b13eea8d3e", "parentUid": "0c1645a4da645d7a1d71b140a4cd79e2", "status": "passed", "time": {"start": 1755550035242, "stop": 1755550057418, "duration": 22176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0c1645a4da645d7a1d71b140a4cd79e2"}], "uid": "3973a80f2c9dc60eba1128908a3b9a37"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Summarize what I'm reading", "uid": "b1915364bac38f76", "parentUid": "cb427ad26aa4429b88871b40f64cfe5f", "status": "passed", "time": {"start": 1755550071657, "stop": 1755550093619, "duration": 21962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb427ad26aa4429b88871b40f64cfe5f"}], "uid": "b6c93448ab1517917d641f5403c05e57"}, {"name": "test_switch_to_davido_voice", "children": [{"name": "TestEllaSwitchDavidoVoice", "children": [{"name": "测试Switch to davido voice能正常执行", "uid": "565d3a68eadd14f6", "parentUid": "597cb599122b854d604c01a270b1a8d0", "status": "passed", "time": {"start": 1755550107536, "stop": 1755550129785, "duration": 22249}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "597cb599122b854d604c01a270b1a8d0"}], "uid": "16aca23b24bc10dfd8554c557da8b449"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "8bb36711edb0df7f", "parentUid": "0bfb9433b5ffe1874f0d5456da733833", "status": "passed", "time": {"start": 1755550143772, "stop": 1755550165334, "duration": 21562}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bfb9433b5ffe1874f0d5456da733833"}], "uid": "6dfd47afc83ade6b7e64a599884aa82b"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "TestEllaSwitchPerformanceMode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "ff50a601a62b5563", "parentUid": "e8f8d6344d1896b53428623c96ae9737", "status": "passed", "time": {"start": 1755550179343, "stop": 1755550201447, "duration": 22104}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8f8d6344d1896b53428623c96ae9737"}], "uid": "660b4eae367594166e98133010b58c55"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchPowerSavingMode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "d356792bb10c9dcb", "parentUid": "69d2b561fc70829d0f0c3bcbc22bd24b", "status": "passed", "time": {"start": 1755550215198, "stop": 1755550237680, "duration": 22482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69d2b561fc70829d0f0c3bcbc22bd24b"}], "uid": "89fed13c0557d811b5a98a81c18249c0"}, {"name": "test_switching_charging_speed", "children": [{"name": "TestEllaSwitchingChargingSpeed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "9a3290f96fe7666b", "parentUid": "364923db55b7a4749bd0f0a3d56b478a", "status": "passed", "time": {"start": 1755550251513, "stop": 1755550273578, "duration": 22065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "364923db55b7a4749bd0f0a3d56b478a"}], "uid": "2bc0a442c3b51933b3e020072f001bf8"}, {"name": "test_take_notes", "children": [{"name": "TestEllaTakeNotes", "children": [{"name": "测试take notes能正常执行", "uid": "2fb5de1bf6466d20", "parentUid": "44b041dc5921837d99b03ee251fe9a80", "status": "failed", "time": {"start": 1755550287593, "stop": 1755550313863, "duration": 26270}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44b041dc5921837d99b03ee251fe9a80"}], "uid": "9ce4c8d89aae56754ff0ae0d7cf834cc"}, {"name": "test_tell_me_a_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "1701645e26679093", "parentUid": "972f4768d4110bc9be4051d713f5f1d4", "status": "passed", "time": {"start": 1755550328224, "stop": 1755550350425, "duration": 22201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "972f4768d4110bc9be4051d713f5f1d4"}], "uid": "3febd52b6aecbf31d119447b90331a44"}, {"name": "test_tell_me_joke", "children": [{"name": "TestEllaTellMeJoke", "children": [{"name": "测试tell me joke能正常执行", "uid": "c554cc0509ed2c17", "parentUid": "7d17397dac8db6170e4185f194ee95e9", "status": "failed", "time": {"start": 1755550364424, "stop": 1755550389686, "duration": 25262}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d17397dac8db6170e4185f194ee95e9"}], "uid": "eeee5549335b4d4ecf3fac8dfda2e40d"}, {"name": "test_the_mobile_phone_is_very_hot", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试the mobile phone is very hot", "uid": "3be2292986414830", "parentUid": "3bfb94cf2cc472b22c0df52f847f1f62", "status": "passed", "time": {"start": 1755550404270, "stop": 1755550427074, "duration": 22804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3bfb94cf2cc472b22c0df52f847f1f62"}], "uid": "644948ffc4704bfb385cb254e8d9a71f"}, {"name": "test_there_are_many_yellow_sunflowers_on_the_ground", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试there are many yellow sunflowers on the ground", "uid": "70ea41509f71ae68", "parentUid": "6a020ec60475fc27227dfd7d129c0666", "status": "passed", "time": {"start": 1755550440995, "stop": 1755550466694, "duration": 25699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a020ec60475fc27227dfd7d129c0666"}], "uid": "4b7ffea1eaf84fdb04dd8c191f0a34a7"}, {"name": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试There are transparent, glowing multicolored soap bubbles around it", "uid": "789a9ec2b9f26c16", "parentUid": "1ac3a98fe00494d546e4e19879238daa", "status": "passed", "time": {"start": 1755550480853, "stop": 1755550507042, "duration": 26189}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ac3a98fe00494d546e4e19879238daa"}], "uid": "4abc82e646169b13fe4bfdca3b0227bf"}, {"name": "test_there_is_a_colorful_butterfly_beside_it", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试there is a colorful butterfly beside it", "uid": "10ed7fd6efa81c6a", "parentUid": "ea0cb2908e9182f02032e5a6ed562f29", "status": "passed", "time": {"start": 1755550520777, "stop": 1755550546193, "duration": 25416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ea0cb2908e9182f02032e5a6ed562f29"}], "uid": "d28054bcefb0ae260c73232793dd123b"}, {"name": "test_three_little_pigs", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试Three Little Pigs", "uid": "f35ab6662635c7df", "parentUid": "781eb57a3919119cd50bf17689fab4a1", "status": "passed", "time": {"start": 1755550560264, "stop": 1755550585695, "duration": 25431}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "781eb57a3919119cd50bf17689fab4a1"}], "uid": "a385499ab54be564b0a879c66033c6d0"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "TestEllaTurnOffDrivingMode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "915a979c1ab3f826", "parentUid": "24ecd251ff3a56a4c2b20fac8ab55f2f", "status": "passed", "time": {"start": 1755550599676, "stop": 1755550621782, "duration": 22106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24ecd251ff3a56a4c2b20fac8ab55f2f"}], "uid": "ca08b2eb3eff172bf5d579af818a108f"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "TestEllaTurnOffShowBatteryPercentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "94a260d39cc61397", "parentUid": "a93695196bd86c4de1585781fedbe080", "status": "passed", "time": {"start": 1755550635665, "stop": 1755550658012, "duration": 22347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93695196bd86c4de1585781fedbe080"}], "uid": "492b5eb198aa5b8127740d2701bc4490"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "TestEllaTurnDrivingMode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "7cc6c01c614625cd", "parentUid": "6160fde1e3814087ef49c469733a5e59", "status": "passed", "time": {"start": 1755550672216, "stop": 1755550698097, "duration": 25881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6160fde1e3814087ef49c469733a5e59"}], "uid": "7dbd97d94de7ac9fc92ce34bec95b544"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "TestEllaTurnHighBrightnessMode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "c0a498b834afb8f6", "parentUid": "afa769cc76891525f7fca4f496a4aa9e", "status": "passed", "time": {"start": 1755550712201, "stop": 1755550734473, "duration": 22272}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afa769cc76891525f7fca4f496a4aa9e"}], "uid": "9751732cdae9d7a072c84060577699cd"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "TestEllaTurnShowBatteryPercentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "1209d5cb1fb232f6", "parentUid": "9a14e86a2195f01365f057ce6c9b9e71", "status": "passed", "time": {"start": 1755550748640, "stop": 1755550771005, "duration": 22365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a14e86a2195f01365f057ce6c9b9e71"}], "uid": "d5d7ce8427ad486d072c9aae7e2d5b3f"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "TestEllaVedioCallNumberWhatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "674938ed9b42673f", "parentUid": "0d26a41f5b9615ad2942aaedf4c3033b", "status": "passed", "time": {"start": 1755550785029, "stop": 1755550815738, "duration": 30709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0d26a41f5b9615ad2942aaedf4c3033b"}], "uid": "2aeb91366c9de96caf75e843c25ff4e8"}, {"name": "test_view_in_notebook", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试view in notebook", "uid": "486198e7ca2f5671", "parentUid": "c3ef4d52b4f3af521894d73b37ffcd6c", "status": "passed", "time": {"start": 1755550829705, "stop": 1755550864436, "duration": 34731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c3ef4d52b4f3af521894d73b37ffcd6c"}], "uid": "2a41ee4b2b23e90432d1271bde83c670"}, {"name": "test_voice_setting_page", "children": [{"name": "TestEllaVoiceSettingPage", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "1aefe55150cc415", "parentUid": "087c3d15647e1625a6f4e506048dafc7", "status": "passed", "time": {"start": 1755550878115, "stop": 1755550909670, "duration": 31555}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "087c3d15647e1625a6f4e506048dafc7"}], "uid": "20224dbf8d73e834d2be6a17094deb05"}, {"name": "test_what_date_is_it", "children": [{"name": "TestEllaWhatDateIsIt", "children": [{"name": "测试what date is it能正常执行", "uid": "3c858841add323a3", "parentUid": "a6477b2e019a056f6425d2950e664eda", "status": "passed", "time": {"start": 1755550923588, "stop": 1755550945795, "duration": 22207}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6477b2e019a056f6425d2950e664eda"}], "uid": "462cfd09072e082d758a28b5f86133d1"}, {"name": "test_what_is_the_weather_today", "children": [{"name": "TestEllaWhatIsWeatherToday", "children": [{"name": "测试what is the weather today能正常执行", "uid": "480a0fe4bb19fcbc", "parentUid": "a414b6a93668541af19f36ef5de69c30", "status": "passed", "time": {"start": 1755550960004, "stop": 1755550990198, "duration": 30194}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a414b6a93668541af19f36ef5de69c30"}], "uid": "e3099a6471a6935ea4a79d5929efd816"}, {"name": "test_what_s_the_date_today", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试what's the date today", "uid": "8b5755924feaa165", "parentUid": "f595c0dd6f1fd9a4e7baf1d329bad64c", "status": "passed", "time": {"start": 1755551004319, "stop": 1755551026729, "duration": 22410}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f595c0dd6f1fd9a4e7baf1d329bad64c"}], "uid": "59a37907804b8a3b0259567688977de8"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "TestEllaWhatSWheatherToday", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "d6444fbbd9fa86d2", "parentUid": "34ec790ead0442abc792eadbded82b45", "status": "passed", "time": {"start": 1755551040688, "stop": 1755551062769, "duration": 22081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34ec790ead0442abc792eadbded82b45"}], "uid": "93ccf0164b324d8401f93f28368fa587"}, {"name": "test_what_s_your_name", "children": [{"name": "TestEllaOpenPlayPoliticalNews", "children": [{"name": "测试what's your name", "uid": "b3d6c60bb022e56e", "parentUid": "b844a592b24930bb05406461a3b55d12", "status": "passed", "time": {"start": 1755551076900, "stop": 1755551098907, "duration": 22007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b844a592b24930bb05406461a3b55d12"}], "uid": "150beefb27e3fb4ef7f8d5b7c8b79550"}, {"name": "test_what_time_is_it", "children": [{"name": "TestEllaWhatTimeIsIt", "children": [{"name": "测试what time is it能正常执行", "uid": "f88fb1e507dee323", "parentUid": "9a1707cab6ba21805e5557c27878166f", "status": "passed", "time": {"start": 1755551113074, "stop": 1755551135081, "duration": 22007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a1707cab6ba21805e5557c27878166f"}], "uid": "62cbf432c6942e953309364cba948cfd"}, {"name": "test_what_time_is_it_in_china", "children": [{"name": "TestEllaWhatTimeIsItChina", "children": [{"name": "测试what time is it in china能正常执行", "uid": "12ed095629d33d34", "parentUid": "3d53633b743425d02140cc93addc1f0b", "status": "passed", "time": {"start": 1755551148997, "stop": 1755551171662, "duration": 22665}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3d53633b743425d02140cc93addc1f0b"}], "uid": "4a9f7a7ef35e6b31c4f5af99323fa779"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "TestEllaWhatTimeIsItLondon", "children": [{"name": "测试what time is it in London能正常执行", "uid": "dd5f2e8b9cbf2bfa", "parentUid": "9e6b9c0060829efcc73d4ea740df1b49", "status": "passed", "time": {"start": 1755551185946, "stop": 1755551208146, "duration": 22200}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e6b9c0060829efcc73d4ea740df1b49"}], "uid": "af53bd7e2482c27610ecc08ef1183aeb"}, {"name": "test_where_is_my_car", "children": [{"name": "TestEllaWhereIsMyCar", "children": [{"name": "测试where is my car能正常执行", "uid": "d69e193e35f210e0", "parentUid": "c5f06521808b3dfc5047ef209e95fcbc", "status": "passed", "time": {"start": 1755551222229, "stop": 1755551244233, "duration": 22004}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5f06521808b3dfc5047ef209e95fcbc"}], "uid": "8cbe670b8f03e2204b3dddfa23a6e7bb"}, {"name": "test_where_s_my_car", "children": [{"name": "TestEllaWhereSMyCar", "children": [{"name": "测试where`s my car能正常执行", "uid": "e34af531cc9853a3", "parentUid": "29ba7f25d098ddc58f6283babdc91d05", "status": "passed", "time": {"start": 1755551258713, "stop": 1755551281024, "duration": 22311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29ba7f25d098ddc58f6283babdc91d05"}], "uid": "f4119e4a1ede5a25ce07bb10ab021122"}, {"name": "test_yandex_eats", "children": [{"name": "TestEllaYandexEats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "979aaae63733c3e6", "parentUid": "3c01adf30480b292f5f2b75550755b66", "status": "failed", "time": {"start": 1755551295036, "stop": 1755551317273, "duration": 22237}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3c01adf30480b292f5f2b75550755b66"}], "uid": "e099b3554c54efdee17091c4c12f77c7"}], "uid": "09cb3650ff0a2cc2af23d31dd3c975a2"}]}