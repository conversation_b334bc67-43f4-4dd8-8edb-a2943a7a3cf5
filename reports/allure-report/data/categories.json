{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: 响应未包含期望内容: ['The following event has been added for you.']\nassert False", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "7823681a04e66dc9", "parentUid": "6ca1a954a23c1af31dbdbbe03bb017f6", "status": "failed", "time": {"start": 1755532475122, "stop": 1755532503008, "duration": 27886}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ca1a954a23c1af31dbdbbe03bb017f6"}, {"name": "AssertionError: clock: 初始=None, 最终=None, 响应='['open clock', 'Done!', '', '', '', '', '[com.transsion.deskclock页面内容] 闹钟 | 06:00 | 周一至周五 | 07:00 | 仅一次. 7 小时1 分钟后响铃 | 07:00 | 周日, 周六. 4 天后响铃 | 09:00 | 周日, 周六. 4 天后响铃 | 10:00 | 仅一次. 10 小时1 分钟后响铃 | 闹钟 | 世界时钟 | 定时器 | 秒表']'\nassert None", "children": [{"name": "open clock", "uid": "8ac2fbb40ff2fe45", "parentUid": "ae1f369edf330813938146af304b8ccd", "status": "failed", "time": {"start": 1755532748815, "stop": 1755532778833, "duration": 30018}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae1f369edf330813938146af304b8ccd"}, {"name": "AssertionError: 响应未包含期望内容: ['Ok']\nassert False", "children": [{"name": "测试pause song能正常执行", "uid": "35deb8fb0261ed29", "parentUid": "90b0f15d519cbcb48d2d2b1b19e1b7b1", "status": "failed", "time": {"start": 1755533126191, "stop": 1755533147873, "duration": 21682}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90b0f15d519cbcb48d2d2b1b19e1b7b1"}, {"name": "AssertionError: 响应未包含期望内容: ['OK']\nassert False", "children": [{"name": "测试previous music能正常执行", "uid": "a00ea4f467dbafd9", "parentUid": "1c747593578634f5ca93d4cd48ec7b3d", "status": "failed", "time": {"start": 1755533504668, "stop": 1755533526338, "duration": 21670}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c747593578634f5ca93d4cd48ec7b3d"}, {"name": "AssertionError: 响应未包含期望内容: ['8:00 AM']\nassert False", "children": [{"name": "测试set an alarm at 8 am", "uid": "b7c3846ca060a587", "parentUid": "2673797f2c429e81b0365fd0d2b1a6a3", "status": "failed", "time": {"start": 1755533615693, "stop": 1755533637438, "duration": 21745}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2673797f2c429e81b0365fd0d2b1a6a3"}, {"name": "AssertionError: 响应文本应包含['I am sorry', 'I can help you with that']，实际响应: '['could you please search an for me', '', '', '', 'I am sorry, I am unable to search for you at this time.', 'Generated by AI, for reference only', '00:23 Dialogue Explore Arsenal, Chelsea in 5/1 Acca Tip How soothe spicy food stomach pain? How to solve primary & junior high questions? could you please search an for me I am sorry, I am unable to search for you at this time. Generated by AI, for reference only What can you search for me today? Search for the weather forecast now? Can you search for restaurants nearby? DeepSeek-R1 Feel free to ask me any questions…']'\nassert False", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "962d28ec069171e0", "parentUid": "85461cf8bb0e1eba18b68ba90651bcb2", "status": "failed", "time": {"start": 1755534186900, "stop": 1755534212350, "duration": 25450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85461cf8bb0e1eba18b68ba90651bcb2"}, {"name": "AssertionError: 响应未包含期望内容: ['not installed yet. Please download the app and try again.']\nassert False", "children": [{"name": "测试play music by visha", "uid": "3daf15b7b3653968", "parentUid": "dc4c8b091911e4e3c62fb0d22bcc23e0", "status": "failed", "time": {"start": 1755535347151, "stop": 1755535385655, "duration": 38504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dc4c8b091911e4e3c62fb0d22bcc23e0"}, {"name": "AssertionError: 响应未包含期望内容: ['Here are your alarms. Which one do you want to delete?']\nassert False", "children": [{"name": "测试remove alarms能正常执行", "uid": "613be028b5c3d57", "parentUid": "e415fac49ec0dd04c039006c5b9187d7", "status": "failed", "time": {"start": 1755535648839, "stop": 1755535672450, "duration": 23611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e415fac49ec0dd04c039006c5b9187d7"}, {"name": "AssertionError: 响应未包含期望内容: ['Premier League Goals Ranking']\nassert False", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "86944148d896cd06", "parentUid": "968491c31f7e73b0cd2b15d7bdd63075", "status": "failed", "time": {"start": 1755535882062, "stop": 1755535905697, "duration": 23635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968491c31f7e73b0cd2b15d7bdd63075"}, {"name": "AssertionError: 响应未包含期望内容: ['Here are your alarms']\nassert False", "children": [{"name": "测试show my all alarms能正常执行", "uid": "a625d34db1d2363e", "parentUid": "580353137101b3ac8832fcfbabe2c4a4", "status": "failed", "time": {"start": 1755535919927, "stop": 1755535943416, "duration": 23489}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试view recent alarms能正常执行", "uid": "76bca7539af6676c", "parentUid": "580353137101b3ac8832fcfbabe2c4a4", "status": "failed", "time": {"start": 1755536449626, "stop": 1755536473336, "duration": 23710}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "580353137101b3ac8832fcfbabe2c4a4"}, {"name": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "children": [{"name": "测试tell me a joke能正常执行", "uid": "7becbd0e020b5726", "parentUid": "451b5a50bea9158be02a728e085db107", "status": "failed", "time": {"start": 1755536328555, "stop": 1755536353272, "duration": 24717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "451b5a50bea9158be02a728e085db107"}, {"name": "AssertionError: 响应未包含期望内容: ['Here are your alarms. Which one do you want to turn off? ']\nassert False", "children": [{"name": "测试unset alarms能正常执行", "uid": "216cc9ef302dab76", "parentUid": "49656f1a77323b5e5e44a1d602cc06f0", "status": "failed", "time": {"start": 1755536367541, "stop": 1755536391042, "duration": 23501}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49656f1a77323b5e5e44a1d602cc06f0"}, {"name": "AssertionError: 响应未包含期望内容: ['Today: the high is forecast']\nassert False", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "ceea2a980c7d0d9c", "parentUid": "78528f98f3aad9478dbf790697dc3756", "status": "failed", "time": {"start": 1755536563259, "stop": 1755536592217, "duration": 28958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78528f98f3aad9478dbf790697dc3756"}, {"name": "AssertionError: 响应未包含期望内容: ['Chong Qing Shi is Fair today. The high is forecast as 37°C and the low as 28°C.']\nassert False", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "44ed27c7b7731a47", "parentUid": "1ff649f1409abb91bfbcbcab690d9ccb", "status": "failed", "time": {"start": 1755536692634, "stop": 1755536714426, "duration": 21792}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ff649f1409abb91bfbcbcab690d9ccb"}, {"name": "AssertionError: 响应未包含期望内容: ['These suggestions are for your reference']\nassert False", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "e68af3605869f6ab", "parentUid": "83e6ce370469a50cbcd5886ffacbabdd", "status": "failed", "time": {"start": 1755536967108, "stop": 1755536988272, "duration": 21164}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83e6ce370469a50cbcd5886ffacbabdd"}, {"name": "AssertionError: 响应未包含期望内容: ['Sorry', 'a bit difficult']\nassert False", "children": [{"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "13f549d9d5b7167a", "parentUid": "bea5a77b0d97e2a935f7509c41885612", "status": "failed", "time": {"start": 1755537094408, "stop": 1755537211531, "duration": 117123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bea5a77b0d97e2a935f7509c41885612"}, {"name": "AssertionError: 响应未包含期望内容: ['The following images are generated for you']\nassert False", "children": [{"name": "测试puppy能正常执行", "uid": "1bcb128e66a5b9a5", "parentUid": "779d4c3a9038ec82f7441487f5def24f", "status": "failed", "time": {"start": 1755537424190, "stop": 1755537502424, "duration": 78234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "779d4c3a9038ec82f7441487f5def24f"}, {"name": "AssertionError: 响应未包含期望内容: ['document provides a summary of the application']\nassert False", "children": [{"name": "测试Summarize what I'm reading能正常执行", "uid": "b58347ad96176240", "parentUid": "d79d483a87b688d99c2746c68b74699e", "status": "failed", "time": {"start": 1755537779885, "stop": 1755537843216, "duration": 63331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d79d483a87b688d99c2746c68b74699e"}, {"name": "AssertionError: 初始=False, 最终=False, 响应='['countdown 5 min', 'Done!', '', '', '', '']'\nassert False", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "4bce7b61e04ef6f8", "parentUid": "3844dde47488de0594ebf7f166ed85cc", "status": "failed", "time": {"start": 1755538254786, "stop": 1755538285262, "duration": 30476}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3844dde47488de0594ebf7f166ed85cc"}, {"name": "AssertionError: 响应未包含期望内容: ['Brightness goes down']\nassert False", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "e34cb01b2c00ec2a", "parentUid": "f81e48840f2cfd5eef5902dcdd149838", "status": "failed", "time": {"start": 1755538299424, "stop": 1755538322745, "duration": 23321}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f81e48840f2cfd5eef5902dcdd149838"}, {"name": "AssertionError: 文件不存在！\nassert False", "children": [{"name": "stop  screen recording能正常执行", "uid": "ffe9139580a870b0", "parentUid": "b16df34f724c90a3340ae49c743c6a99", "status": "failed", "time": {"start": 1755538416925, "stop": 1755538445384, "duration": 28459}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "9b5c88885de7c800", "parentUid": "b16df34f724c90a3340ae49c743c6a99", "status": "failed", "time": {"start": 1755541635822, "stop": 1755541662964, "duration": 27142}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b16df34f724c90a3340ae49c743c6a99"}, {"name": "AssertionError: 初始=None, 最终=None, 响应='['make the phone mute', 'Mute is turned on now.', 'Mute', '', '', '']'\nassert None", "children": [{"name": "测试make the phone mute能正常执行", "uid": "44359ff7b8030e39", "parentUid": "0e9cbcbfb48a2d7bbf8fb9c0d17e5aa6", "status": "failed", "time": {"start": 1755538725583, "stop": 1755538748958, "duration": 23375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0e9cbcbfb48a2d7bbf8fb9c0d17e5aa6"}, {"name": "AssertionError: 初始=0, 最终=0, 响应='['max notifications volume', 'Notification volume has been set to the maximum.', '', '', '', '']'\nassert 0 == 15", "children": [{"name": "测试max notifications volume能正常执行", "uid": "ce531324130742d4", "parentUid": "fb6719d9c5cabf43832fc756d400139b", "status": "failed", "time": {"start": 1755538836770, "stop": 1755538858989, "duration": 22219}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb6719d9c5cabf43832fc756d400139b"}, {"name": "AssertionError: 响应未包含期望内容: ['The alarm for 10:00 AM has been set.']\nassert False", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "320a4646a2dfbba3", "parentUid": "c79a2c8b3e5877c56652b264d1fb5389", "status": "failed", "time": {"start": 1755539510703, "stop": 1755539534441, "duration": 23738}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c79a2c8b3e5877c56652b264d1fb5389"}, {"name": "AssertionError: clock: 初始=None, 最终=None, 响应='['set my alarm volume to 50%', 'Alarm volume has been set to 50.', '', '', '', '']'\nassert None == 7", "children": [{"name": "测试set my alarm volume to 50%", "uid": "328cb95c896f8b39", "parentUid": "449cfbf2a8d6effab2618565d64411da", "status": "failed", "time": {"start": 1755539635251, "stop": 1755539658539, "duration": 23288}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "449cfbf2a8d6effab2618565d64411da"}, {"name": "AssertionError: 初始=0, 最终=0, 响应='['set notifications volume to 50', 'Notification volume has been set to 50.', '', '', '', '']'\nassert 0 == 7", "children": [{"name": "测试set notifications volume to 50能正常执行", "uid": "684fcf296df9cee5", "parentUid": "87ee0194c1cf89f390fc5e1e51afaaed", "status": "failed", "time": {"start": 1755539672907, "stop": 1755539696420, "duration": 23513}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "87ee0194c1cf89f390fc5e1e51afaaed"}, {"name": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "children": [{"name": "测试smart charge能正常执行", "uid": "abcbb8c6f6452862", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1755539824246, "stop": 1755539847138, "duration": 22892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch charging modes能正常执行", "uid": "523026d721903565", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1755540169131, "stop": 1755540191004, "duration": 21873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to smart charge能正常执行", "uid": "55991e123833109c", "parentUid": "40c8e511bd59d1b6469cd14c6e42d010", "status": "failed", "time": {"start": 1755540551537, "stop": 1755540573104, "duration": 21567}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "40c8e511bd59d1b6469cd14c6e42d010"}, {"name": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "children": [{"name": "测试switch to default mode能正常执行", "uid": "c86277f1fc22de8e", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1755540313809, "stop": 1755540345990, "duration": 32181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to flash notification能正常执行", "uid": "ab28f76d4ee5ffab", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1755540396088, "stop": 1755540428767, "duration": 32679}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check system update", "uid": "ecc71f2f493e1262", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1755543571254, "stop": 1755543593013, "duration": 21759}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试extend the image能正常执行", "uid": "d5ebc6c67f353f03", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1755544880054, "stop": 1755544901864, "duration": 21810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pls open whatsapp", "uid": "1d166cd5bb89f599", "parentUid": "5a710013a126f24211d2305c197d8623", "status": "failed", "time": {"start": 1755548023055, "stop": 1755548046986, "duration": 23931}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a710013a126f24211d2305c197d8623"}, {"name": "AssertionError: 初始=None, 最终=None, 响应='['turn on adaptive brightness', 'Auto-brightness is turned on now.', 'Auto-brightness', '', '', '']'\nassert None", "children": [{"name": "测试turn on adaptive brightness能正常执行", "uid": "774b232eb8b4279", "parentUid": "04d837c08a6180adc752bdb9576e6a8a", "status": "failed", "time": {"start": 1755541150238, "stop": 1755541173024, "duration": 22786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04d837c08a6180adc752bdb9576e6a8a"}, {"name": "AssertionError: 响应未包含期望内容: ['Auto-rotation is turned on now']\nassert False", "children": [{"name": "测试turn on auto rotate screen能正常执行", "uid": "ef8f8d42edcb4f66", "parentUid": "58aa0db6760298f5bea868ac1b16e6d0", "status": "failed", "time": {"start": 1755541186998, "stop": 1755541209769, "duration": 22771}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58aa0db6760298f5bea868ac1b16e6d0"}, {"name": "AssertionError: 初始=False, 最终=False, 响应='['turn on nfc', 'NFC is turned on now.', 'NFC', '', '', '']'\nassert False", "children": [{"name": "测试turn on nfc能正常执行", "uid": "eb294921beeb20c3", "parentUid": "7bd953848c3f3d7f5c6a11be5740be1c", "status": "failed", "time": {"start": 1755541443841, "stop": 1755541468196, "duration": 24355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7bd953848c3f3d7f5c6a11be5740be1c"}, {"name": "AssertionError: 初始=None, 最终=None, 响应='['turn on smart reminder', 'Smart reminder is turned on now.', 'Smart reminder', '', '', '']'\nassert None", "children": [{"name": "测试turn on smart reminder能正常执行", "uid": "bd361d609157db", "parentUid": "2ba0d3bf866584915ef5ecdec2b3de96", "status": "failed", "time": {"start": 1755541482387, "stop": 1755541505404, "duration": 23017}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ba0d3bf866584915ef5ecdec2b3de96"}, {"name": "AssertionError: 响应未包含期望内容: ['The alarm']\nassert False", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "a7eef724e114d433", "parentUid": "72d7187d1e6d18d7d6031e43745b6163", "status": "failed", "time": {"start": 1755541896311, "stop": 1755541918389, "duration": 22078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "72d7187d1e6d18d7d6031e43745b6163"}, {"name": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['download basketball', 'No relevant apps found. Please download and try again.', '', '', '', '']'\nassert False", "children": [{"name": "测试download basketball能正常执行", "uid": "5a0f62687113a756", "parentUid": "6dae73b2aac0b54aa483ebdfacc374f7", "status": "failed", "time": {"start": 1755542015938, "stop": 1755542039422, "duration": 23484}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6dae73b2aac0b54aa483ebdfacc374f7"}, {"name": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "children": [{"name": "测试order a burger能正常执行", "uid": "190a2c7d515274c1", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1755542412322, "stop": 1755542433838, "duration": 21516}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway能正常执行", "uid": "4bb0f3490eb7fd10", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1755542448115, "stop": 1755542469919, "duration": 21804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball返回正确的不支持响应", "uid": "878b77da97742d88", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1755544230502, "stop": 1755544252918, "duration": 22416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试driving mode返回正确的不支持响应", "uid": "e99caa00c608f900", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1755544390869, "stop": 1755544413086, "duration": 22217}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "a12388ce0112ec8b", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1755547501236, "stop": 1755547523724, "duration": 22488}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试yandex eats返回正确的不支持响应", "uid": "979aaae63733c3e6", "parentUid": "8c7fcb7e700713ec9d3081e0b69d1f2e", "status": "failed", "time": {"start": 1755551295036, "stop": 1755551317273, "duration": 22237}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c7fcb7e700713ec9d3081e0b69d1f2e"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "children": [{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "70a5f4ae04e6558e", "parentUid": "5a757b63ea699f9ed7ff608edc8f78c8", "status": "failed", "time": {"start": 1755542750283, "stop": 1755542772092, "duration": 21809}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a757b63ea699f9ed7ff608edc8f78c8"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A furry little monkey', '', '', '', \"That sounds like a cute image! I am unable to generate images directly. Since you're interested in image generation, would you like to explore some other visual styles or image ideas?\", 'Generated by AI, for reference only', \"Dialogue Explore Can you help with high school function problems? Educators Guide GenAI Ethics Help remove people from images A furry little monkey That sounds like a cute image! I am unable to generate images directly. Since you're interested in image generation, would you like to explore some other visual styles or image ideas? Generated by AI, for reference only Monkey image visual style exploration Monkey image creation software options Monkey image generation techniques DeepSeek-R1 Feel free to ask me any questions… 02:46\"]'\nassert False", "children": [{"name": "测试A furry little monkey", "uid": "77992e69d66afef9", "parentUid": "2e0cfa0f913054873c48f5b4261f9da2", "status": "failed", "time": {"start": 1755542786715, "stop": 1755542811669, "duration": 24954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e0cfa0f913054873c48f5b4261f9da2"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance', '', '', '', \"That's a wonderfully detailed image! I can't generate images directly, but I know you're interested in image generation and design. Would you like to try creating that image with a jewelry ring, or maybe explore some visual styles like vector doodles or minimalist art?\", 'Generated by AI, for reference only', \"Dialogue Explore Can you help with high school function problems? A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance That's a wonderfully detailed image! I can't generate images directly, but I know you're interested in image generation and design. Would you like to try creating that image with a jewelry ring, or maybe explore some visual styles like vector doodles or minimalist art? Generated by AI, for reference only How to create the raccoon image now? Different art styles for the raccoon image Raccoon image generation tools and tips DeepSeek-R1 Feel free to ask me any questions… 02:47\"]'\nassert False", "children": [{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "30209a062596ef82", "parentUid": "d09abc797507f601b5385b7713501833", "status": "failed", "time": {"start": 1755542825673, "stop": 1755542852185, "duration": 26512}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d09abc797507f601b5385b7713501833"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "children": [{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "f1406a88d312092e", "parentUid": "27aa08b3fcd888906364a3a2ea69df8c", "status": "failed", "time": {"start": 1755542869874, "stop": 1755542891488, "duration": 21614}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27aa08b3fcd888906364a3a2ea69df8c"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A photo of a transparent glass cup', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "children": [{"name": "测试A photo of a transparent glass cup ", "uid": "fae32e11992942f2", "parentUid": "a2db5508ca80bb1c88fdc944e073e7c6", "status": "failed", "time": {"start": 1755542905521, "stop": 1755542927616, "duration": 22095}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a2db5508ca80bb1c88fdc944e073e7c6"}, {"name": "AssertionError: contacts: 初始=False, 最终=False, 响应='['call mom', 'Found the following contact. Would you like to call?', '', '', '', '']'\nassert False", "children": [{"name": "测试call mom", "uid": "8be6bece5fd1af0d", "parentUid": "1a2f5f5dd600a2736f7eadaf27b1133a", "status": "failed", "time": {"start": 1755542980983, "stop": 1755543012019, "duration": 31036}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1a2f5f5dd600a2736f7eadaf27b1133a"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a landscape painting image for me', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "children": [{"name": "测试Generate a landscape painting image for me", "uid": "4b2de58234c4451e", "parentUid": "5039e3b5b2324f4e2f2957578d1312b2", "status": "failed", "time": {"start": 1755545024642, "stop": 1755545046581, "duration": 21939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5039e3b5b2324f4e2f2957578d1312b2"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.', '', '', '', \"That's a fantastic description for an image! Since you're interested in image generation, would you like to explore some other visual styles or image ideas?\", 'Generated by AI, for reference only', \"Dialogue Explore hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively. That's a fantastic description for an image! Since you're interested in image generation, would you like to explore some other visual styles or image ideas? Generated by AI, for reference only Hamster mascot design for brand identity Hamster mascot illustration software options Different hamster mascot poses and styles DeepSeek-R1 Feel free to ask me any questions… 03:28\"]'\nassert False", "children": [{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "397b1501c16d5e75", "parentUid": "b42bf688cb92ea58ddfd401b6d964e7b", "status": "failed", "time": {"start": 1755545279615, "stop": 1755545304641, "duration": 25026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b42bf688cb92ea58ddfd401b6d964e7b"}, {"name": "AssertionError: 响应未包含期望内容: ['Generated by AI, for reference only']\nassert False", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "a5616c43d872369b", "parentUid": "9939635229b1a9f0e29b413706a1cdcd", "status": "failed", "time": {"start": 1755545835689, "stop": 1755545861121, "duration": 25432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9939635229b1a9f0e29b413706a1cdcd"}, {"name": "AssertionError: 响应文本应包含['your Infinix phone assistant', 'your TECNO phone assistant']，实际响应: '['i am your voice assistant', '', '', '', \"That's right! I'm here to assist you. How can I help you today?\", 'Generated by AI, for reference only', \"Dialogue Explore Refresh Can physics & chemistry problems be solved by photo? AI Reshaping HR Roles Why do metals expand when heated i am your voice assistant That's right! I'm here to assist you. How can I help you today? Generated by AI, for reference only What are your main capabilities? What are your limitations as a voice assistant? How do you process user requests? DeepSeek-R1 Feel free to ask me any questions… 03:39\"]'\nassert False", "children": [{"name": "测试i am your voice assistant", "uid": "6933b6fb12ae346d", "parentUid": "36c1b3c3daa849708f722096e8ed9a9c", "status": "failed", "time": {"start": 1755545950033, "stop": 1755545975296, "duration": 25263}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "36c1b3c3daa849708f722096e8ed9a9c"}, {"name": "AssertionError: 响应未包含期望内容: ['Please tell me the name or number to call']\nassert False", "children": [{"name": "测试i want make a video call to能正常执行", "uid": "1a5d810bd216a785", "parentUid": "e96f119b35e082cea5ffc8e7df6d9ff4", "status": "failed", "time": {"start": 1755546074978, "stop": 1755546105867, "duration": 30889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e96f119b35e082cea5ffc8e7df6d9ff4"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.']，实际响应: '['merry christmas', 'Merry Christmas to you🎄', '', '', '', '']'\nassert False", "children": [{"name": "测试merry christmas", "uid": "32a4642822207cf0", "parentUid": "aebea6ebe7d11c591deba91d68f1db66", "status": "failed", "time": {"start": 1755546864527, "stop": 1755546889045, "duration": 24518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "aebea6ebe7d11c591deba91d68f1db66"}, {"name": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to navigate to any address at the moment. Is there anything else I can help you with?', 'Generated by AI, for reference only', '03:57 Dialogue Explore Do traditional dance steps contain numerical sequences? Can you help with high school function problems? Send my recent photo to mom on WhatsApp navigation to the address in thie image I am sorry, I am unable to navigate to any address at the moment. Is there anything else I can help you with? Generated by AI, for reference only Troubleshooting navigation system errors How to save the address for later use Alternative navigation apps and features DeepSeek-R1 Feel free to ask me any questions…']'\nassert False", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "c4a9a1ad677f0098", "parentUid": "bce9af8b3746a2689db5b1d0f1580e0e", "status": "failed", "time": {"start": 1755547027098, "stop": 1755547058930, "duration": 31832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bce9af8b3746a2689db5b1d0f1580e0e"}, {"name": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "children": [{"name": "测试open whatsapp", "uid": "d907a6931b9bab45", "parentUid": "cd83a8ba9fa1be6164e1ab47cca1e5e3", "status": "failed", "time": {"start": 1755547463774, "stop": 1755547487298, "duration": 23524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd83a8ba9fa1be6164e1ab47cca1e5e3"}, {"name": "AssertionError: 响应未包含期望内容: ['Your current location:']\nassert False", "children": [{"name": "测试please show me where i am能正常执行", "uid": "79010a10fe7f461d", "parentUid": "17d4b37b7d5e6a360b52105bd7fd8e47", "status": "failed", "time": {"start": 1755547984334, "stop": 1755548008569, "duration": 24235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "17d4b37b7d5e6a360b52105bd7fd8e47"}, {"name": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', '\"remember the parking space\" recorded', '', '', '', '']'\nassert False", "children": [{"name": "测试remember the parking space", "uid": "bc289b8805ed6f47", "parentUid": "c121de1894a76326df8671a55a225b7a", "status": "failed", "time": {"start": 1755548175424, "stop": 1755548197727, "duration": 22303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c121de1894a76326df8671a55a225b7a"}, {"name": "AssertionError: 响应文本应包含['The following images are generated for you.', 'Do you enjoy being outdoors?', 'enjoy running', 'outdoors', 'Do you have a favorite park or location where you like to go running on the grass?']，实际响应: '['running on the grass', '', '', '', 'That sounds like a great way to enjoy some fresh air! Do you have a favorite park or trail for running?', 'Generated by AI, for reference only', \"04:18 Dialogue Explore How to solve primary & junior high questions? Send my recent photo to mom on WhatsApp Taylor Swift's 2025 Pop Reign running on the grass That sounds like a great way to enjoy some fresh air! Do you have a favorite park or trail for running? Generated by AI, for reference only Best shoes for running on grass Running on grass injury risks Benefits of running on grass DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "children": [{"name": "测试running on the grass", "uid": "15b8865ebef84c4", "parentUid": "b20e01d28e5793ec9af3965f663e8844", "status": "failed", "time": {"start": 1755548253869, "stop": 1755548281183, "duration": 27314}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b20e01d28e5793ec9af3965f663e8844"}, {"name": "AssertionError: 响应未包含期望内容: ['What do you want it to say?']\nassert False", "children": [{"name": "测试take notes能正常执行", "uid": "2fb5de1bf6466d20", "parentUid": "7f289369a823f220d4b32bfc3dd749bb", "status": "failed", "time": {"start": 1755550287593, "stop": 1755550313863, "duration": 26270}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f289369a823f220d4b32bfc3dd749bb"}, {"name": "AssertionError: 响应未包含期望内容: ['joke for you']\nassert False", "children": [{"name": "测试tell me joke能正常执行", "uid": "c554cc0509ed2c17", "parentUid": "235f58b319e1c816d9218c9a1556ca47", "status": "failed", "time": {"start": 1755550364424, "stop": 1755550389686, "duration": 25262}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "235f58b319e1c816d9218c9a1556ca47"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}, {"name": "Test defects", "children": [{"name": "TypeError: a bytes-like object is required, not 'dict'", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "2016852632d1a42f", "parentUid": "018bca9f4d9ac8e9d50a0bff138a0cdf", "status": "broken", "time": {"start": 1755533915043, "stop": 1755533940339, "duration": 25296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "018bca9f4d9ac8e9d50a0bff138a0cdf"}, {"name": "ValueError: too many values to unpack (expected 3)", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "5a07f04e872ede5e", "parentUid": "8e6c858299af90aba28521bd81ad3b69", "status": "broken", "time": {"start": 1755535806907, "stop": 1755535828451, "duration": 21544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试navigation to the first address in the image能正常执行", "uid": "f252f9db319d4be8", "parentUid": "8e6c858299af90aba28521bd81ad3b69", "status": "broken", "time": {"start": 1755547073652, "stop": 1755547105068, "duration": 31416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8e6c858299af90aba28521bd81ad3b69"}], "uid": "bdbf199525818fae7a8651db9eafe741"}]}