{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "测试close aivana能正常执行", "uid": "b7f17392cbd20aee", "parentUid": "0a654e8ba5d406f4dced08e54877f4f8", "status": "passed", "time": {"start": 1755532260908, "stop": 1755532293374, "duration": 32466}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a654e8ba5d406f4dced08e54877f4f8"}, {"name": "test_close_ella", "children": [{"name": "测试close ella能正常执行", "uid": "e738d908d4f56738", "parentUid": "b5a2cabf288d90878566c5ce33227175", "status": "passed", "time": {"start": 1755532307829, "stop": 1755532344211, "duration": 36382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5a2cabf288d90878566c5ce33227175"}, {"name": "test_close_folax", "children": [{"name": "测试close folax能正常执行", "uid": "b6e2a81031476512", "parentUid": "3b1f73acd32162c3bc6cd626dc7f1272", "status": "passed", "time": {"start": 1755532358236, "stop": 1755532391952, "duration": 33716}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b1f73acd32162c3bc6cd626dc7f1272"}, {"name": "test_close_phonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "d0cb405c048b5762", "parentUid": "e2aa70cb4f489d6ed135a8d54afbd69e", "status": "passed", "time": {"start": 1755532405659, "stop": 1755532427281, "duration": 21622}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2aa70cb4f489d6ed135a8d54afbd69e"}, {"name": "test_continue_music", "children": [{"name": "测试continue music能正常执行", "uid": "fe991698e5267e29", "parentUid": "6a23546ce5380ec0616c9f82a57a36ad", "status": "passed", "time": {"start": 1755532440416, "stop": 1755532462076, "duration": 21660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a23546ce5380ec0616c9f82a57a36ad"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "7823681a04e66dc9", "parentUid": "6ef10c8d834a3fd9e2b41931a6d5b667", "status": "failed", "time": {"start": 1755532475122, "stop": 1755532503008, "duration": 27886}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ef10c8d834a3fd9e2b41931a6d5b667"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "79a84d7137b558e3", "parentUid": "744e7a2785bdcb064b4cb0104fffb47a", "status": "passed", "time": {"start": 1755532516620, "stop": 1755532538737, "duration": 22117}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "744e7a2785bdcb064b4cb0104fffb47a"}, {"name": "test_disable_call_on_hold", "children": [{"name": "测试disable call on hold返回正确的不支持响应", "uid": "c13cdb2a38eadf83", "parentUid": "77a12ec993f1aecf9afde2b5350a0a71", "status": "passed", "time": {"start": 1755532551996, "stop": 1755532582809, "duration": 30813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77a12ec993f1aecf9afde2b5350a0a71"}, {"name": "test_display_the_route_go_company", "children": [{"name": "测试display the route go company", "uid": "744cbb103ecdfa25", "parentUid": "e22797f7f98b084598882fb5cec440fe", "status": "passed", "time": {"start": 1755532595464, "stop": 1755532622408, "duration": 26944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e22797f7f98b084598882fb5cec440fe"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "a87b2b4fbad25d0f", "parentUid": "9df0629aeb726d588255055629526bcb", "status": "passed", "time": {"start": 1755532635332, "stop": 1755532656373, "duration": 21041}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9df0629aeb726d588255055629526bcb"}, {"name": "test_next_channel", "children": [{"name": "测试next channel能正常执行", "uid": "ff4baae6c2c6c2f2", "parentUid": "3f23899abed5acee33c72c68b61ec080", "status": "passed", "time": {"start": 1755532669374, "stop": 1755532691320, "duration": 21946}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f23899abed5acee33c72c68b61ec080"}, {"name": "test_open_camera", "children": [{"name": "测试open camera能正常执行", "uid": "f3e2e05aaeb14ddc", "parentUid": "470a45e0cfa4244680b09ac2a8c782a2", "status": "passed", "time": {"start": 1755532704470, "stop": 1755532734454, "duration": 29984}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "470a45e0cfa4244680b09ac2a8c782a2"}, {"name": "test_open_clock", "children": [{"name": "open clock", "uid": "8ac2fbb40ff2fe45", "parentUid": "50daf9198779f130ca593a54f87ec128", "status": "failed", "time": {"start": 1755532748815, "stop": 1755532778833, "duration": 30018}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50daf9198779f130ca593a54f87ec128"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "1486d4607be0b253", "parentUid": "c18b5d7a76803510c6e0d2868a096cc6", "status": "passed", "time": {"start": 1755532793075, "stop": 1755532830457, "duration": 37382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c18b5d7a76803510c6e0d2868a096cc6"}, {"name": "test_open_countdown", "children": [{"name": "测试open countdown能正常执行", "uid": "4d7d35dfd5353048", "parentUid": "34dd669acbf251ecc3b4bcb8039abeb9", "status": "passed", "time": {"start": 1755532844673, "stop": 1755532867161, "duration": 22488}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34dd669acbf251ecc3b4bcb8039abeb9"}, {"name": "test_open_dialer", "children": [{"name": "测试open dialer能正常执行", "uid": "36874b7f55820c4a", "parentUid": "9072ee3e27d0b6c6a2866a718c2df078", "status": "passed", "time": {"start": 1755532881286, "stop": 1755532917469, "duration": 36183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9072ee3e27d0b6c6a2866a718c2df078"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "f866a2bad0ee5662", "parentUid": "4ee54c4b3bb105e867eb3473949bd494", "status": "passed", "time": {"start": 1755532931498, "stop": 1755532953979, "duration": 22481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ee54c4b3bb105e867eb3473949bd494"}, {"name": "test_open_folax", "children": [{"name": "测试open folax能正常执行", "uid": "9dc10018605926ba", "parentUid": "e02fc6e8f5b7a4fa147ce01474445b5b", "status": "passed", "time": {"start": 1755532968248, "stop": 1755532990113, "duration": 21865}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e02fc6e8f5b7a4fa147ce01474445b5b"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "a600cbbbab2efcc8", "parentUid": "342f26d794168c8355629898d89335fe", "status": "passed", "time": {"start": 1755533003998, "stop": 1755533039380, "duration": 35382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "342f26d794168c8355629898d89335fe"}, {"name": "test_pause_fm", "children": [{"name": "测试pause fm能正常执行", "uid": "fd60a3ff320b3f36", "parentUid": "83a06f2be77f3b44f6a099f43b575dbf", "status": "passed", "time": {"start": 1755533053460, "stop": 1755533075835, "duration": 22375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83a06f2be77f3b44f6a099f43b575dbf"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "f8f62e26362f4acd", "parentUid": "a3026ab8ba7d276b771465a73b8cd69f", "status": "passed", "time": {"start": 1755533089991, "stop": 1755533112024, "duration": 22033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3026ab8ba7d276b771465a73b8cd69f"}, {"name": "test_pause_song", "children": [{"name": "测试pause song能正常执行", "uid": "35deb8fb0261ed29", "parentUid": "1a65a9bcb8b35f4b071220b80ad32958", "status": "failed", "time": {"start": 1755533126191, "stop": 1755533147873, "duration": 21682}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1a65a9bcb8b35f4b071220b80ad32958"}, {"name": "test_phone_boost", "children": [{"name": "测试phone boost能正常执行", "uid": "ea5b3cbd54bf3b2a", "parentUid": "e9cea1e20f3779ed164d139ee8b570ab", "status": "passed", "time": {"start": 1755533162236, "stop": 1755533183911, "duration": 21675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9cea1e20f3779ed164d139ee8b570ab"}, {"name": "test_play_afro_strut", "children": [{"name": "测试play afro strut", "uid": "ee1adc8b75cf0025", "parentUid": "51e1e1cd02cc1154b480c322b15c41c7", "status": "passed", "time": {"start": 1755533197292, "stop": 1755533240096, "duration": 42804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51e1e1cd02cc1154b480c322b15c41c7"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "测试play jay chou's music", "uid": "68c40bdfdb691181", "parentUid": "80ffd53a52b22963552b5b4f8ad4a69c", "status": "passed", "time": {"start": 1755533254235, "stop": 1755533293660, "duration": 39425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80ffd53a52b22963552b5b4f8ad4a69c"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "测试play jay chou's music by spotify", "uid": "8e014ba05d1d3d20", "parentUid": "fb3c6fdc98f3f31c0cad30d7f977b25b", "status": "passed", "time": {"start": 1755533307222, "stop": 1755533331544, "duration": 24322}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb3c6fdc98f3f31c0cad30d7f977b25b"}, {"name": "test_play_music", "children": [{"name": "测试play music", "uid": "dbd33f4313f9e7e1", "parentUid": "012cce9a22125b4f9274376f463a21a6", "status": "passed", "time": {"start": 1755533344992, "stop": 1755533383918, "duration": 38926}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012cce9a22125b4f9274376f463a21a6"}, {"name": "test_play_rock_music", "children": [{"name": "测试play rock music", "uid": "d7a88c8a026436bb", "parentUid": "5fe073acfe44a1d8a05ea050b2df638f", "status": "passed", "time": {"start": 1755533397763, "stop": 1755533436640, "duration": 38877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5fe073acfe44a1d8a05ea050b2df638f"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "测试play sun be song of jide chord", "uid": "e21c5cf1146a7ec", "parentUid": "e61c3229af1d310b3286353485e7dc26", "status": "passed", "time": {"start": 1755533451310, "stop": 1755533491041, "duration": 39731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e61c3229af1d310b3286353485e7dc26"}, {"name": "test_previous_music", "children": [{"name": "测试previous music能正常执行", "uid": "a00ea4f467dbafd9", "parentUid": "a7e1f637551ed2330d6d67ed54a71f33", "status": "failed", "time": {"start": 1755533504668, "stop": 1755533526338, "duration": 21670}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7e1f637551ed2330d6d67ed54a71f33"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "f1d7293de10d9144", "parentUid": "cfa8b6a3fd06497697d78e23fab251dd", "status": "passed", "time": {"start": 1755533540018, "stop": 1755533565891, "duration": 25873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfa8b6a3fd06497697d78e23fab251dd"}, {"name": "test_resume_music", "children": [{"name": "测试resume music能正常执行", "uid": "dd8189ee216b6c12", "parentUid": "0f680c0e3c91eaed54cda04d37208ed3", "status": "passed", "time": {"start": 1755533579539, "stop": 1755533601527, "duration": 21988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f680c0e3c91eaed54cda04d37208ed3"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "测试set an alarm at 8 am", "uid": "b7c3846ca060a587", "parentUid": "934efa6c18aa0ed520853ffbd4ef3bd7", "status": "failed", "time": {"start": 1755533615693, "stop": 1755533637438, "duration": 21745}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "934efa6c18aa0ed520853ffbd4ef3bd7"}, {"name": "test_stop_playing", "children": [{"name": "测试stop playing", "uid": "687c67ac43338e6c", "parentUid": "c598fac3e058ad96b70d8acf40dc13d8", "status": "passed", "time": {"start": 1755533651835, "stop": 1755533675491, "duration": 23656}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c598fac3e058ad96b70d8acf40dc13d8"}, {"name": "test_take_a_screenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "61332991e71deb47", "parentUid": "c1793294d28b52937b4f0aa9a0fee952", "status": "passed", "time": {"start": 1755533689519, "stop": 1755533713980, "duration": 24461}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1793294d28b52937b4f0aa9a0fee952"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "测试turn off the 7AM alarm", "uid": "96b1f45ef3f7523", "parentUid": "32bfb55cdaddbf6d8fd789b810c908d9", "status": "passed", "time": {"start": 1755533727782, "stop": 1755533749866, "duration": 22084}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "32bfb55cdaddbf6d8fd789b810c908d9"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "测试turn off the 8 am alarm", "uid": "3a4693475a14571f", "parentUid": "568593413cf95986c665f08593ea21e9", "status": "passed", "time": {"start": 1755533764116, "stop": 1755533786554, "duration": 22438}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "568593413cf95986c665f08593ea21e9"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "98d99df8f07af07c", "parentUid": "739e8257af5cd9a36c4e8e1ddaad5c24", "status": "passed", "time": {"start": 1755533800620, "stop": 1755533822664, "duration": 22044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "739e8257af5cd9a36c4e8e1ddaad5c24"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "ec904da81ea162a6", "parentUid": "85d39690a527a90b7d30ade2fdd4a8fc", "status": "passed", "time": {"start": 1755533836407, "stop": 1755533865439, "duration": 29032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85d39690a527a90b7d30ade2fdd4a8fc"}], "uid": "155057a0afc640ed94e8c8d7c444a680"}, {"name": "dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "测试appeler maman能正常执行", "uid": "9e09c78c059d573c", "parentUid": "7954f72724ef95cff1433b0efbee5646", "status": "passed", "time": {"start": 1755533879354, "stop": 1755533901182, "duration": 21828}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7954f72724ef95cff1433b0efbee5646"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "2016852632d1a42f", "parentUid": "8f0bd634d6e7676a3a49d6dff49497fa", "status": "broken", "time": {"start": 1755533915043, "stop": 1755533940339, "duration": 25296}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f0bd634d6e7676a3a49d6dff49497fa"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "c7834e9682ae356d", "parentUid": "8541ffcf150af606538b232e3aea7434", "status": "passed", "time": {"start": 1755533954607, "stop": 1755533985423, "duration": 30816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8541ffcf150af606538b232e3aea7434"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "89372ef1b45d48b4", "parentUid": "7e22a70303a1dcd81523a4e5a786a8c4", "status": "passed", "time": {"start": 1755533999433, "stop": 1755534024121, "duration": 24688}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e22a70303a1dcd81523a4e5a786a8c4"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "939cebe22906f8ea", "parentUid": "8c0c4d699c270397d14ca2a01dc1ee04", "status": "passed", "time": {"start": 1755534037938, "stop": 1755534060173, "duration": 22235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c0c4d699c270397d14ca2a01dc1ee04"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "f3c3b82db6e5f74a", "parentUid": "4ea0f2069633d579cb2aa7e3f4dee562", "status": "passed", "time": {"start": 1755534074311, "stop": 1755534097644, "duration": 23333}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ea0f2069633d579cb2aa7e3f4dee562"}, {"name": "test_close_whatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "be07492f78740106", "parentUid": "254be578575e3bd49816bef9d50f165f", "status": "passed", "time": {"start": 1755534111697, "stop": 1755534135236, "duration": 23539}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "254be578575e3bd49816bef9d50f165f"}, {"name": "test_continue_playing", "children": [{"name": "测试continue playing能正常执行", "uid": "ce2c76aab15be26", "parentUid": "d1d509a5f33ff7082393c4843543ffff", "status": "passed", "time": {"start": 1755534149151, "stop": 1755534172506, "duration": 23355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d1d509a5f33ff7082393c4843543ffff"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "962d28ec069171e0", "parentUid": "7327508f376f094b8e8fcbe0310288ab", "status": "failed", "time": {"start": 1755534186900, "stop": 1755534212350, "duration": 25450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7327508f376f094b8e8fcbe0310288ab"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "f6e59a7abb074140", "parentUid": "3622231925f5d7d87dfc0a1200f98b66", "status": "passed", "time": {"start": 1755534226372, "stop": 1755534248683, "duration": 22311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3622231925f5d7d87dfc0a1200f98b66"}, {"name": "test_give_me_some_money", "children": [{"name": "测试give me some money能正常执行", "uid": "5efdde9e15b3a8b4", "parentUid": "690fab1bc7060b99c06d337a10d44d74", "status": "passed", "time": {"start": 1755534262782, "stop": 1755534287138, "duration": 24356}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "690fab1bc7060b99c06d337a10d44d74"}, {"name": "test_global_gdp_trends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "36bd15928d9b0905", "parentUid": "76c541180f5aa46cc8bcc707c7d9c16f", "status": "passed", "time": {"start": 1755534301066, "stop": 1755534326958, "duration": 25892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76c541180f5aa46cc8bcc707c7d9c16f"}, {"name": "test_go_on_playing_fm", "children": [{"name": "测试go on playing fm能正常执行", "uid": "7186c09ba4d6ed7b", "parentUid": "f0bcda9b9eeb890ffe415969c037c977", "status": "passed", "time": {"start": 1755534340663, "stop": 1755534364374, "duration": 23711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0bcda9b9eeb890ffe415969c037c977"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "6a06414489839fca", "parentUid": "b9ee9e8a38234ec69db09f78700d7fbb", "status": "passed", "time": {"start": 1755534378496, "stop": 1755534404032, "duration": 25536}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9ee9e8a38234ec69db09f78700d7fbb"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "febd4965bc5b5d32", "parentUid": "c54c88063ac1edf2e569991ff95d16e9", "status": "passed", "time": {"start": 1755534418034, "stop": 1755534453910, "duration": 35876}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c54c88063ac1edf2e569991ff95d16e9"}, {"name": "test_hi", "children": [{"name": "测试hi能正常执行", "uid": "e31835399ffcc5d7", "parentUid": "acfbc4c8f3f5adee7b09787ceaa2dd0d", "status": "passed", "time": {"start": 1755534467838, "stop": 1755534493385, "duration": 25547}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acfbc4c8f3f5adee7b09787ceaa2dd0d"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "测试how is the weather today能正常执行", "uid": "aa9cf13684b28f0c", "parentUid": "35fc4938c7d094c99682c376792faffe", "status": "passed", "time": {"start": 1755534507267, "stop": 1755534536333, "duration": 29066}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "35fc4938c7d094c99682c376792faffe"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "294d0761bd0b35ea", "parentUid": "ccd640573d805b985ffb9441df6e5fe3", "status": "passed", "time": {"start": 1755534550532, "stop": 1755534572689, "duration": 22157}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccd640573d805b985ffb9441df6e5fe3"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "3006e468cff1526", "parentUid": "addae1e5b6cb9e45cb30b52b0519d0df", "status": "passed", "time": {"start": 1755534586604, "stop": 1755534615590, "duration": 28986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "addae1e5b6cb9e45cb30b52b0519d0df"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "80c8b496993f5e37", "parentUid": "5cbfc9081ff73a6bd530b2831a9e3384", "status": "passed", "time": {"start": 1755534629582, "stop": 1755534658909, "duration": 29327}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cbfc9081ff73a6bd530b2831a9e3384"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "d993e90b89a322eb", "parentUid": "58f878e12ecf6b0d82d8bb49999f4d47", "status": "passed", "time": {"start": 1755534672894, "stop": 1755534693038, "duration": 20144}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58f878e12ecf6b0d82d8bb49999f4d47"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "65d99825173c0947", "parentUid": "82fbe34286468477e9134feb14dde382", "status": "passed", "time": {"start": 1755534707111, "stop": 1755534727518, "duration": 20407}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82fbe34286468477e9134feb14dde382"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "b81fc5f7f6d96890", "parentUid": "e8c194f64f4bc6de5e59e9c7b048cfe4", "status": "passed", "time": {"start": 1755534741316, "stop": 1755534765733, "duration": 24417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8c194f64f4bc6de5e59e9c7b048cfe4"}, {"name": "test_i_want_to_listen_to_fm", "children": [{"name": "测试i want to listen to fm能正常执行", "uid": "cf9f80b2bf9e2831", "parentUid": "62c1ecd6121de1385415edcb14e97d2c", "status": "passed", "time": {"start": 1755534779500, "stop": 1755534801331, "duration": 21831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62c1ecd6121de1385415edcb14e97d2c"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "测试i want to make a call能正常执行", "uid": "b888d3bc51fa3b1b", "parentUid": "3a5af3715ffe87f7125f4fc9d4a39a79", "status": "passed", "time": {"start": 1755534815267, "stop": 1755534846296, "duration": 31029}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3a5af3715ffe87f7125f4fc9d4a39a79"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "c3fab8a57bf5eaa5", "parentUid": "2d9bf69b53882c59bc26f9d5bf23b4c7", "status": "passed", "time": {"start": 1755534860314, "stop": 1755534885786, "duration": 25472}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d9bf69b53882c59bc26f9d5bf23b4c7"}, {"name": "test_introduce_yourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "e062c6b9a8b843fd", "parentUid": "472715b25ea329acf3f408d9a469274a", "status": "passed", "time": {"start": 1755534899694, "stop": 1755534924691, "duration": 24997}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472715b25ea329acf3f408d9a469274a"}, {"name": "test_last_channel", "children": [{"name": "测试last channel能正常执行", "uid": "66e2b58373c1c525", "parentUid": "1673f8e5a00bba040872744c10466a1a", "status": "passed", "time": {"start": 1755534938772, "stop": 1755534961004, "duration": 22232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1673f8e5a00bba040872744c10466a1a"}, {"name": "test_listen_to_fm", "children": [{"name": "测试listen to fm能正常执行", "uid": "ef1df60b7ba75692", "parentUid": "3f8cc40b215a2f65db4811e729bc65fd", "status": "passed", "time": {"start": 1755534974838, "stop": 1755534996912, "duration": 22074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f8cc40b215a2f65db4811e729bc65fd"}, {"name": "test_make_a_call", "children": [{"name": "测试make a call能正常执行", "uid": "eb676212321caaf6", "parentUid": "11ee179329ce36ecafe38100e52ec70d", "status": "passed", "time": {"start": 1755535011062, "stop": 1755535041885, "duration": 30823}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11ee179329ce36ecafe38100e52ec70d"}, {"name": "test_measure_blood_oxygen", "children": [{"name": "测试measure blood oxygen", "uid": "409e680a2a7a5258", "parentUid": "2707f2dd651484f2df0cab1faf1ff403", "status": "passed", "time": {"start": 1755535055553, "stop": 1755535077198, "duration": 21645}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2707f2dd651484f2df0cab1faf1ff403"}, {"name": "test_measure_heart_rate", "children": [{"name": "测试measure heart rate", "uid": "ddc06db5db20b677", "parentUid": "df37b64c34a0bb9e92aee8469cf785c9", "status": "passed", "time": {"start": 1755535091232, "stop": 1755535113013, "duration": 21781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df37b64c34a0bb9e92aee8469cf785c9"}, {"name": "test_next_music", "children": [{"name": "测试next music能正常执行", "uid": "59a4ff0abe989014", "parentUid": "23537c9370c1a822549b7a79eb682856", "status": "passed", "time": {"start": 1755535127017, "stop": 1755535149023, "duration": 22006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "23537c9370c1a822549b7a79eb682856"}, {"name": "test_next_song", "children": [{"name": "测试next song能正常执行", "uid": "817201f101a3fce8", "parentUid": "036f43ecd5f7343046e2af56838f0b2c", "status": "passed", "time": {"start": 1755535162990, "stop": 1755535185150, "duration": 22160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "036f43ecd5f7343046e2af56838f0b2c"}, {"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "c1f1346361a3976a", "parentUid": "c969d4a520c25845a9e33a24d4e3b7a4", "status": "passed", "time": {"start": 1755535199034, "stop": 1755535220932, "duration": 21898}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c969d4a520c25845a9e33a24d4e3b7a4"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "81430c2cbc2daab3", "parentUid": "80d0cfc5ba53853ee4e5c374ac3ca7b4", "status": "passed", "time": {"start": 1755535235001, "stop": 1755535256706, "duration": 21705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80d0cfc5ba53853ee4e5c374ac3ca7b4"}, {"name": "test_play_music_by_VLC", "children": [{"name": "测试play music by VLC", "uid": "bbf40f8013d48bf8", "parentUid": "0cffe8d8bb255395290a9dd513723b30", "status": "passed", "time": {"start": 1755535270790, "stop": 1755535294960, "duration": 24170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0cffe8d8bb255395290a9dd513723b30"}, {"name": "test_play_music_by_boomplay", "children": [{"name": "测试play music by boomplay", "uid": "3b0a2a501433e61e", "parentUid": "973892d18717ef61a9c60231ae0888fd", "status": "passed", "time": {"start": 1755535308816, "stop": 1755535333056, "duration": 24240}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "973892d18717ef61a9c60231ae0888fd"}, {"name": "test_play_music_by_visha", "children": [{"name": "测试play music by visha", "uid": "3daf15b7b3653968", "parentUid": "7b9d8d24ad1638d28448c5d76170b0db", "status": "failed", "time": {"start": 1755535347151, "stop": 1755535385655, "duration": 38504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b9d8d24ad1638d28448c5d76170b0db"}, {"name": "test_play_music_by_yandex_music", "children": [{"name": "测试play music by yandex music", "uid": "1c76bbb443caa181", "parentUid": "480e35527d7a53156164d862f3505bc3", "status": "passed", "time": {"start": 1755535399772, "stop": 1755535424407, "duration": 24635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "480e35527d7a53156164d862f3505bc3"}, {"name": "test_play_music_on_boomplayer", "children": [{"name": "测试play music on boomplayer", "uid": "25a1d94359b88fb7", "parentUid": "b9e3972a06a525f9a243e4a5ad6d8284", "status": "passed", "time": {"start": 1755535438162, "stop": 1755535462677, "duration": 24515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9e3972a06a525f9a243e4a5ad6d8284"}, {"name": "test_play_music_on_visha", "children": [{"name": "测试play music on visha", "uid": "f475b7a1580f4c44", "parentUid": "45b5c17a13f2f2c58c18e0c5403b8174", "status": "passed", "time": {"start": 1755535476591, "stop": 1755535516009, "duration": 39418}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45b5c17a13f2f2c58c18e0c5403b8174"}, {"name": "test_play_news", "children": [{"name": "测试play news", "uid": "e6acbfc61014ddb0", "parentUid": "0545a1fc4a946cb1c690d92255face5c", "status": "passed", "time": {"start": 1755535530009, "stop": 1755535557931, "duration": 27922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0545a1fc4a946cb1c690d92255face5c"}, {"name": "test_play_political_news", "children": [{"name": "测试play political news", "uid": "70ae52d960b93bbd", "parentUid": "350bcef11f4fb6eb99f97d3fa6fe1d47", "status": "passed", "time": {"start": 1755535571881, "stop": 1755535599049, "duration": 27168}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350bcef11f4fb6eb99f97d3fa6fe1d47"}, {"name": "test_previous_song", "children": [{"name": "测试previous song能正常执行", "uid": "d454847eee1af6e6", "parentUid": "4277679ed8d0b781135c825e64cd8c91", "status": "passed", "time": {"start": 1755535613034, "stop": 1755535634719, "duration": 21685}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4277679ed8d0b781135c825e64cd8c91"}, {"name": "test_remove_alarms", "children": [{"name": "测试remove alarms能正常执行", "uid": "613be028b5c3d57", "parentUid": "a37f4999e35eb21b851cb519de2c19b5", "status": "failed", "time": {"start": 1755535648839, "stop": 1755535672450, "duration": 23611}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a37f4999e35eb21b851cb519de2c19b5"}, {"name": "test_say_hello", "children": [{"name": "测试say hello能正常执行", "uid": "4947f2cef0c48e09", "parentUid": "b695b344ff456dc284b52017667bcd48", "status": "passed", "time": {"start": 1755535686786, "stop": 1755535712138, "duration": 25352}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b695b344ff456dc284b52017667bcd48"}, {"name": "test_search_my_gallery_for_food_pictures", "children": [{"name": "测试search my gallery for food pictures能正常执行", "uid": "f7c114017e246923", "parentUid": "cdb79778de81160b7231a87afe6bd1ba", "status": "passed", "time": {"start": 1755535726211, "stop": 1755535752166, "duration": 25955}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdb79778de81160b7231a87afe6bd1ba"}, {"name": "test_search_picture_in_my_gallery", "children": [{"name": "测试search picture in my gallery能正常执行", "uid": "825ccf498017b3be", "parentUid": "2ee81b68d6fe8782f4c16078e5ded646", "status": "passed", "time": {"start": 1755535766243, "stop": 1755535793130, "duration": 26887}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ee81b68d6fe8782f4c16078e5ded646"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "5a07f04e872ede5e", "parentUid": "f3e7c7fb3655fd1be27c6d251a6f492c", "status": "broken", "time": {"start": 1755535806907, "stop": 1755535828451, "duration": 21544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3e7c7fb3655fd1be27c6d251a6f492c"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "f82ce057e728b5af", "parentUid": "11264fcbfd285641cac64cf3c0f1f4a8", "status": "passed", "time": {"start": 1755535842792, "stop": 1755535868100, "duration": 25308}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11264fcbfd285641cac64cf3c0f1f4a8"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "86944148d896cd06", "parentUid": "51407e55f461bb75e0ea1d0bcd974a5d", "status": "failed", "time": {"start": 1755535882062, "stop": 1755535905697, "duration": 23635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51407e55f461bb75e0ea1d0bcd974a5d"}, {"name": "test_show_my_all_alarms", "children": [{"name": "测试show my all alarms能正常执行", "uid": "a625d34db1d2363e", "parentUid": "7d58aebf2bec28bf77ceb0d213b422ee", "status": "failed", "time": {"start": 1755535919927, "stop": 1755535943416, "duration": 23489}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d58aebf2bec28bf77ceb0d213b422ee"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "71fced2abbc8460c", "parentUid": "390af3b968f43ff01d77f8b7960b3916", "status": "passed", "time": {"start": 1755535957935, "stop": 1755535983319, "duration": 25384}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "390af3b968f43ff01d77f8b7960b3916"}, {"name": "test_start_countdown", "children": [{"name": "测试start countdown能正常执行", "uid": "f90ad952af985cbc", "parentUid": "7f509a668a017ed18e7c0a0484490f1d", "status": "passed", "time": {"start": 1755535997349, "stop": 1755536019197, "duration": 21848}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f509a668a017ed18e7c0a0484490f1d"}, {"name": "test_stop_music", "children": [{"name": "测试stop music能正常执行", "uid": "ddd871a1ce7ff648", "parentUid": "77eaeb306e34a7a9bfb4cedeba8ee35f", "status": "passed", "time": {"start": 1755536033339, "stop": 1755536055063, "duration": 21724}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77eaeb306e34a7a9bfb4cedeba8ee35f"}, {"name": "test_stop_run", "children": [{"name": "测试stop run能正常执行", "uid": "3708a4f460cc073c", "parentUid": "ccf9f3b4d1f905064aacc7d3127597b9", "status": "passed", "time": {"start": 1755536068964, "stop": 1755536091096, "duration": 22132}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccf9f3b4d1f905064aacc7d3127597b9"}, {"name": "test_stop_workout", "children": [{"name": "测试stop workout能正常执行", "uid": "2fc78b29d7c3b8a0", "parentUid": "8ee49369bb9d601da7f062855654c904", "status": "passed", "time": {"start": 1755536105054, "stop": 1755536127302, "duration": 22248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ee49369bb9d601da7f062855654c904"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "7b92e467542433c3", "parentUid": "b6a6c5c1cc3bbfd99be0b798596d90fd", "status": "passed", "time": {"start": 1755536141449, "stop": 1755536163238, "duration": 21789}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6a6c5c1cc3bbfd99be0b798596d90fd"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试summarize what i'm reading能正常执行", "uid": "dd571f0ce49847cb", "parentUid": "a593e493c32cd99871d376fe55644611", "status": "passed", "time": {"start": 1755536177633, "stop": 1755536199724, "duration": 22091}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a593e493c32cd99871d376fe55644611"}, {"name": "test_take_a_joke", "children": [{"name": "测试take a joke能正常执行", "uid": "f5cd234643930df6", "parentUid": "a1543d72151cd8524884aba6ef4613f6", "status": "passed", "time": {"start": 1755536213534, "stop": 1755536238671, "duration": 25137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1543d72151cd8524884aba6ef4613f6"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "2c4695e673696dce", "parentUid": "218955f9fce08124722f8aa63ee5293a", "status": "passed", "time": {"start": 1755536252538, "stop": 1755536274482, "duration": 21944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "218955f9fce08124722f8aa63ee5293a"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "74958e75bd6a6af", "parentUid": "acd0d4c0ff78ae721b07111ac720141b", "status": "passed", "time": {"start": 1755536288386, "stop": 1755536314654, "duration": 26268}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd0d4c0ff78ae721b07111ac720141b"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "7becbd0e020b5726", "parentUid": "1efd42262e16a36d4ac595eed9b7e185", "status": "failed", "time": {"start": 1755536328555, "stop": 1755536353272, "duration": 24717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1efd42262e16a36d4ac595eed9b7e185"}, {"name": "test_unset_alarms", "children": [{"name": "测试unset alarms能正常执行", "uid": "216cc9ef302dab76", "parentUid": "8eb34f4ad03f2b3301605936af5f6db6", "status": "failed", "time": {"start": 1755536367541, "stop": 1755536391042, "duration": 23501}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8eb34f4ad03f2b3301605936af5f6db6"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "4b3e6384cc783150", "parentUid": "f8daa61044336f258fd904820f097662", "status": "passed", "time": {"start": 1755536405171, "stop": 1755536435715, "duration": 30544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8daa61044336f258fd904820f097662"}, {"name": "test_view_recent_alarms", "children": [{"name": "测试view recent alarms能正常执行", "uid": "76bca7539af6676c", "parentUid": "42362c26e7ec72bda0a34e7ccd060ca6", "status": "failed", "time": {"start": 1755536449626, "stop": 1755536473336, "duration": 23710}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42362c26e7ec72bda0a34e7ccd060ca6"}, {"name": "test_what_is_apec", "children": [{"name": "测试what is apec?能正常执行", "uid": "25841d99901eab3", "parentUid": "4567e57227f42cc7993892d86e523dbf", "status": "passed", "time": {"start": 1755536487501, "stop": 1755536513261, "duration": 25760}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4567e57227f42cc7993892d86e523dbf"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "测试What languages do you support能正常执行", "uid": "29b0fa4f7a3cc5a", "parentUid": "20b964271968b9aba9b13a138825e688", "status": "passed", "time": {"start": 1755536527132, "stop": 1755536549205, "duration": 22073}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20b964271968b9aba9b13a138825e688"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试what's the weather like in shanghai today能正常执行", "uid": "ceea2a980c7d0d9c", "parentUid": "15f8c381e13aff9342434a5db73fe066", "status": "failed", "time": {"start": 1755536563259, "stop": 1755536592217, "duration": 28958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "15f8c381e13aff9342434a5db73fe066"}, {"name": "test_what_s_the_weather_like_today", "children": [{"name": "测试What's the weather like today能正常执行", "uid": "80086802f00af29", "parentUid": "51399e7e2539b443ff346f3e8db98136", "status": "passed", "time": {"start": 1755536606412, "stop": 1755536635478, "duration": 29066}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51399e7e2539b443ff346f3e8db98136"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "c02619d66e652e22", "parentUid": "af2f763310dd868fb7293de462763b64", "status": "passed", "time": {"start": 1755536649309, "stop": 1755536678621, "duration": 29312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af2f763310dd868fb7293de462763b64"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "44ed27c7b7731a47", "parentUid": "b880b898b5ae6eb8576adc4f20cdd8c6", "status": "failed", "time": {"start": 1755536692634, "stop": 1755536714426, "duration": 21792}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b880b898b5ae6eb8576adc4f20cdd8c6"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name？能正常执行", "uid": "16d295102618a407", "parentUid": "4b64931505303134ebf07e49f3a9c0af", "status": "passed", "time": {"start": 1755536728444, "stop": 1755536750354, "duration": 21910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4b64931505303134ebf07e49f3a9c0af"}, {"name": "test_what_time_is_it_now", "children": [{"name": "测试what time is it now能正常执行", "uid": "8a9d56a1fc3fb7f6", "parentUid": "6a367b30c7c610464ef1a8acfa0f93c9", "status": "passed", "time": {"start": 1755536764315, "stop": 1755536786453, "duration": 22138}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a367b30c7c610464ef1a8acfa0f93c9"}, {"name": "test_whats_the_weather_today", "children": [{"name": "测试what's the weather today?能正常执行", "uid": "edbd6b5a32553e05", "parentUid": "d38e79c83442fa3e23fba8b6ba34de56", "status": "passed", "time": {"start": 1755536800425, "stop": 1755536829587, "duration": 29162}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d38e79c83442fa3e23fba8b6ba34de56"}, {"name": "test_who_is_harry_potter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "d4d4eeb7e7cd4dd7", "parentUid": "651e0dc624a6f14649ebc34a90907bc1", "status": "passed", "time": {"start": 1755536843226, "stop": 1755536868904, "duration": 25678}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "651e0dc624a6f14649ebc34a90907bc1"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "cc934885a239c888", "parentUid": "9e3d19482db247c568f3393a2db09f1f", "status": "passed", "time": {"start": 1755536882797, "stop": 1755536908635, "duration": 25838}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3d19482db247c568f3393a2db09f1f"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "4f78c40977d29fd5", "parentUid": "d08c8b89f0cacddcb2f61563717a9da6", "status": "passed", "time": {"start": 1755536922602, "stop": 1755536953417, "duration": 30815}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d08c8b89f0cacddcb2f61563717a9da6"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "e68af3605869f6ab", "parentUid": "015300f55fc1d98de4057611dffc7dd0", "status": "failed", "time": {"start": 1755536967108, "stop": 1755536988272, "duration": 21164}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "015300f55fc1d98de4057611dffc7dd0"}], "uid": "9c28642eff039d4ac586a58d4f4a0368"}, {"name": "self_function", "children": [{"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "测试A cute little boy is skiing 能正常执行", "uid": "d49e8d0d736d2f2b", "parentUid": "5dce92d62b80cb6553f1512dba0fb4c1", "status": "passed", "time": {"start": 1755537002442, "stop": 1755537080578, "duration": 78136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5dce92d62b80cb6553f1512dba0fb4c1"}, {"name": "test_change_the_style_of_this_image_to_d_cartoon", "children": [{"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "13f549d9d5b7167a", "parentUid": "df3998e3ff6eea9977d2eb96f3dafe5c", "status": "failed", "time": {"start": 1755537094408, "stop": 1755537211531, "duration": 117123}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df3998e3ff6eea9977d2eb96f3dafe5c"}, {"name": "test_document_summary", "children": [{"name": "测试document summary能正常执行", "uid": "20d4b045c93c589c", "parentUid": "c53eeb531d41bb17ebbb8c4a5a1c0c7f", "status": "passed", "time": {"start": 1755537225693, "stop": 1755537279470, "duration": 53777}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c53eeb531d41bb17ebbb8c4a5a1c0c7f"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "586d00224ff103cd", "parentUid": "424f36be71517de9612f5c1c4bbfd51b", "status": "passed", "time": {"start": 1755537293489, "stop": 1755537410226, "duration": 116737}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "424f36be71517de9612f5c1c4bbfd51b"}, {"name": "test_puppy", "children": [{"name": "测试puppy能正常执行", "uid": "1bcb128e66a5b9a5", "parentUid": "5c4a74895e8f5f371499f67901869735", "status": "failed", "time": {"start": 1755537424190, "stop": 1755537502424, "duration": 78234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c4a74895e8f5f371499f67901869735"}, {"name": "test_scan_the_qr_code_in_the_image", "children": [{"name": "测试Scan the QR code in the image 能正常执行", "uid": "93132b8d78ed6db", "parentUid": "6d5e42acbf3b14ff9c7a1cb039af1eca", "status": "passed", "time": {"start": 1755537516454, "stop": 1755537633655, "duration": 117201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d5e42acbf3b14ff9c7a1cb039af1eca"}, {"name": "test_scan_this_qr_code", "children": [{"name": "测试Scan this QR code 能正常执行", "uid": "a3fec9292f8b0113", "parentUid": "e69f8752d4c2fff88f531d31725048e7", "status": "passed", "time": {"start": 1755537647829, "stop": 1755537765913, "duration": 118084}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e69f8752d4c2fff88f531d31725048e7"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试Summarize what I'm reading能正常执行", "uid": "b58347ad96176240", "parentUid": "4a719e0465c60ccc21a482f8a248c354", "status": "failed", "time": {"start": 1755537779885, "stop": 1755537843216, "duration": 63331}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a719e0465c60ccc21a482f8a248c354"}], "uid": "d4441fc536166659c8ba844cb229b96d"}, {"name": "system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "ce3ad8cd15332bdc", "parentUid": "f42ec0f584493c7e8f49de225d028d41", "status": "passed", "time": {"start": 1755537857648, "stop": 1755537880029, "duration": 22381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f42ec0f584493c7e8f49de225d028d41"}, {"name": "test_adjustment_the_brightness_to_maximun", "children": [{"name": "测试adjustment the brightness to maximun能正常执行", "uid": "56faba69a46b6a0", "parentUid": "0a124f1e35221e2f7fc58638efac4609", "status": "passed", "time": {"start": 1755537893929, "stop": 1755537916363, "duration": 22434}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a124f1e35221e2f7fc58638efac4609"}, {"name": "test_adjustment_the_brightness_to_minimun", "children": [{"name": "测试adjustment the brightness to minimun能正常执行", "uid": "b255e6f959e5a001", "parentUid": "6cabd7a2575b3a49ba1d7e27cbebac2c", "status": "passed", "time": {"start": 1755537930409, "stop": 1755537952531, "duration": 22122}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cabd7a2575b3a49ba1d7e27cbebac2c"}, {"name": "test_boost_phone", "children": [{"name": "测试boost phone能正常执行", "uid": "221ac10e9d0a0fd8", "parentUid": "084463cd25c5bdf79a97950326707013", "status": "passed", "time": {"start": 1755537966527, "stop": 1755537988156, "duration": 21629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "084463cd25c5bdf79a97950326707013"}, {"name": "test_change_your_language", "children": [{"name": "测试change your language能正常执行", "uid": "23689521abf187ca", "parentUid": "6e01a0998eaccde4524ec4e2c73e42c4", "status": "passed", "time": {"start": 1755538002305, "stop": 1755538029474, "duration": 27169}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e01a0998eaccde4524ec4e2c73e42c4"}, {"name": "test_change_your_language_to_chinese", "children": [{"name": "测试change your language to chinese能正常执行", "uid": "dc1728464f2098ce", "parentUid": "3ac391fa66b27ea8ccce7735db22cb6a", "status": "skipped", "time": {"start": 1755538030786, "stop": 1755538030786, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='语言设置为中文，影响别的Case，先跳过')"]}], "uid": "3ac391fa66b27ea8ccce7735db22cb6a"}, {"name": "test_check_front_camera_information", "children": [{"name": "测试check front camera information能正常执行", "uid": "66074d91611f0379", "parentUid": "a3c3dc73a42e85d88737ae9cfea06ccf", "status": "passed", "time": {"start": 1755538042556, "stop": 1755538067497, "duration": 24941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c3dc73a42e85d88737ae9cfea06ccf"}, {"name": "test_clear_junk_files", "children": [{"name": "测试clear junk files命令", "uid": "df184aa14f08e16a", "parentUid": "79b6df9a290803cad2cc59c26f69f0d5", "status": "passed", "time": {"start": 1755538080665, "stop": 1755538130030, "duration": 49365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b6df9a290803cad2cc59c26f69f0d5"}, {"name": "test_close_airplane", "children": [{"name": "测试close airplane能正常执行", "uid": "29e24e26da6b1f7c", "parentUid": "472d53b48cd9911b61f6571aa3fabc81", "status": "passed", "time": {"start": 1755538143900, "stop": 1755538166705, "duration": 22805}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472d53b48cd9911b61f6571aa3fabc81"}, {"name": "test_close_bluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "a4ff49fb87f88c71", "parentUid": "43c3e77fe7845ae372f8b29b479e9c36", "status": "passed", "time": {"start": 1755538180436, "stop": 1755538202211, "duration": 21775}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43c3e77fe7845ae372f8b29b479e9c36"}, {"name": "test_close_flashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "9a80a467c73adc93", "parentUid": "05e7de1f6a30400f4ba09b6b927c0c5f", "status": "passed", "time": {"start": 1755538216019, "stop": 1755538240697, "duration": 24678}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05e7de1f6a30400f4ba09b6b927c0c5f"}, {"name": "test_close_wifi", "children": [{"name": "测试close wifi能正常执行", "uid": "7480b55353f93bc6", "parentUid": "2fcb64cb23f984daec672237d94bb890", "status": "skipped", "time": {"start": 1755538242038, "stop": 1755538242038, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='该脚本较特殊，先跳过')", "smoke"]}], "uid": "2fcb64cb23f984daec672237d94bb890"}, {"name": "test_countdown_min", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "4bce7b61e04ef6f8", "parentUid": "5acc0b94de2365f9212477ba5756e434", "status": "failed", "time": {"start": 1755538254786, "stop": 1755538285262, "duration": 30476}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5acc0b94de2365f9212477ba5756e434"}, {"name": "test_decrease_the_brightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "e34cb01b2c00ec2a", "parentUid": "71d6a5aa3a46022e7d7ccf02f396978b", "status": "failed", "time": {"start": 1755538299424, "stop": 1755538322745, "duration": 23321}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d6a5aa3a46022e7d7ccf02f396978b"}, {"name": "test_decrease_the_volume_to_the_minimun", "children": [{"name": "测试decrease the volume to the minimun能正常执行", "uid": "3d8ea4f9bffaeeda", "parentUid": "20521c4eded335a33ef2e1e8ba13e750", "status": "passed", "time": {"start": 1755538337147, "stop": 1755538360848, "duration": 23701}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20521c4eded335a33ef2e1e8ba13e750"}, {"name": "test_end_screen_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "77d90cbd0aa54ede", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "passed", "time": {"start": 1755538374910, "stop": 1755538402909, "duration": 27999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "ffe9139580a870b0", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "failed", "time": {"start": 1755538416925, "stop": 1755538445384, "duration": 28459}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62823fc182e440a2772cd17c42ab4c29"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "3c6057146d8431fa", "parentUid": "1349bd48475843b043511c26d6b12b24", "status": "passed", "time": {"start": 1755538459644, "stop": 1755538485320, "duration": 25676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1349bd48475843b043511c26d6b12b24"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "4f28f303d3b93e43", "parentUid": "f5c6c4880b5d8ab718c388b911880945", "status": "passed", "time": {"start": 1755538499457, "stop": 1755538525684, "duration": 26227}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f5c6c4880b5d8ab718c388b911880945"}, {"name": "test_increase_notification_volume", "children": [{"name": "测试increase notification volume能正常执行", "uid": "98a2bd79089efe14", "parentUid": "c820439150f9f3eeda53e313099e370f", "status": "passed", "time": {"start": 1755538539706, "stop": 1755538563343, "duration": 23637}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c820439150f9f3eeda53e313099e370f"}, {"name": "test_increase_screen_brightness", "children": [{"name": "测试increase screen brightness能正常执行", "uid": "c8d53c70b4d6dbed", "parentUid": "c07e39e268fcc0b13f001cda95d292f1", "status": "passed", "time": {"start": 1755538577374, "stop": 1755538599601, "duration": 22227}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c07e39e268fcc0b13f001cda95d292f1"}, {"name": "test_increase_the_brightness", "children": [{"name": "测试increase the brightness能正常执行", "uid": "aac5c19fbbd75fa1", "parentUid": "318cb9a601f399198ad7b64303d6a804", "status": "passed", "time": {"start": 1755538613838, "stop": 1755538636244, "duration": 22406}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "318cb9a601f399198ad7b64303d6a804"}, {"name": "test_increase_the_volume_to_the_maximun", "children": [{"name": "测试increase the volume to the maximun能正常执行", "uid": "472b2f5bc7ebd088", "parentUid": "6daa3caef60cbff270b232fec14c929c", "status": "passed", "time": {"start": 1755538650413, "stop": 1755538672876, "duration": 22463}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6daa3caef60cbff270b232fec14c929c"}, {"name": "test_long_screenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "efe9dd5698af5838", "parentUid": "eee53365e608da87e8c1a38cd7dffd82", "status": "passed", "time": {"start": 1755538686791, "stop": 1755538711774, "duration": 24983}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eee53365e608da87e8c1a38cd7dffd82"}, {"name": "test_make_the_phone_mute", "children": [{"name": "测试make the phone mute能正常执行", "uid": "44359ff7b8030e39", "parentUid": "0b44ef42d6ad53a2bea3bbed424e41ee", "status": "failed", "time": {"start": 1755538725583, "stop": 1755538748958, "duration": 23375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b44ef42d6ad53a2bea3bbed424e41ee"}, {"name": "test_max_alarm_clock_volume", "children": [{"name": "测试max alarm clock volume", "uid": "ea916f8d49f87076", "parentUid": "cb770bbd2e94c64f600ababeb4f0ee6a", "status": "passed", "time": {"start": 1755538763210, "stop": 1755538785995, "duration": 22785}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb770bbd2e94c64f600ababeb4f0ee6a"}, {"name": "test_max_brightness", "children": [{"name": "测试max brightness能正常执行", "uid": "ac23d9c080599ec1", "parentUid": "bc6c7f74ec9c5d95879c2d1cf9880a19", "status": "passed", "time": {"start": 1755538800160, "stop": 1755538822693, "duration": 22533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc6c7f74ec9c5d95879c2d1cf9880a19"}, {"name": "test_max_notifications_volume", "children": [{"name": "测试max notifications volume能正常执行", "uid": "ce531324130742d4", "parentUid": "d168a4bb52fda427e53b993ad648e85a", "status": "failed", "time": {"start": 1755538836770, "stop": 1755538858989, "duration": 22219}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d168a4bb52fda427e53b993ad648e85a"}, {"name": "test_max_ring_volume", "children": [{"name": "测试max ring volume能正常执行", "uid": "93055a6f3a5e2208", "parentUid": "446c602b84cd3dd1f96b31e0108411fb", "status": "passed", "time": {"start": 1755538873347, "stop": 1755538896291, "duration": 22944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "446c602b84cd3dd1f96b31e0108411fb"}, {"name": "test_maximum_volume", "children": [{"name": "测试maximum volume能正常执行", "uid": "74799b443815fe47", "parentUid": "df87e0d39fb14600720a434db404fb7b", "status": "passed", "time": {"start": 1755538910308, "stop": 1755538933258, "duration": 22950}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df87e0d39fb14600720a434db404fb7b"}, {"name": "test_memory_cleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "5cb654923a5394e", "parentUid": "3060b8cc7f41dc69110dfc534e6b31d5", "status": "passed", "time": {"start": 1755538947324, "stop": 1755538995563, "duration": 48239}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3060b8cc7f41dc69110dfc534e6b31d5"}, {"name": "test_min_alarm_clock_volume", "children": [{"name": "测试min alarm clock volume", "uid": "96d8d1a506ac9b9e", "parentUid": "fd1b7fb10ddbe5eadf93766446821b65", "status": "passed", "time": {"start": 1755539009642, "stop": 1755539032100, "duration": 22458}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd1b7fb10ddbe5eadf93766446821b65"}, {"name": "test_min_brightness", "children": [{"name": "测试min brightness能正常执行", "uid": "3492662f4dd7bebb", "parentUid": "e5eb14b295c00a0d59407203918f4795", "status": "passed", "time": {"start": 1755539046145, "stop": 1755539068664, "duration": 22519}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5eb14b295c00a0d59407203918f4795"}, {"name": "test_min_notifications_volume", "children": [{"name": "测试min notifications volume能正常执行", "uid": "5717823ea652bea4", "parentUid": "11646edd82fe620b37c2b64152ced5c6", "status": "passed", "time": {"start": 1755539082567, "stop": 1755539105335, "duration": 22768}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11646edd82fe620b37c2b64152ced5c6"}, {"name": "test_min_ring_volume", "children": [{"name": "测试min ring volume能正常执行", "uid": "451d5363ecf7e75f", "parentUid": "d45b602df43d160bc186bdce526932e8", "status": "passed", "time": {"start": 1755539119149, "stop": 1755539142045, "duration": 22896}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d45b602df43d160bc186bdce526932e8"}, {"name": "test_minimum_volume", "children": [{"name": "测试minimum volume能正常执行", "uid": "4997c4a01fffa2e1", "parentUid": "25a58f43bfaf0da153ec687fea5dcbb5", "status": "passed", "time": {"start": 1755539155868, "stop": 1755539178585, "duration": 22717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25a58f43bfaf0da153ec687fea5dcbb5"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open bluetooth", "uid": "18d532e0c27f0a7d", "parentUid": "31e546db33350801c6eaef968b45b6aa", "status": "passed", "time": {"start": 1755539192709, "stop": 1755539215056, "duration": 22347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31e546db33350801c6eaef968b45b6aa"}, {"name": "test_open_bt", "children": [{"name": "测试open bt", "uid": "71be630e71caa50e", "parentUid": "176a0fdfd9c4144b8dfe3d66a049fda5", "status": "passed", "time": {"start": 1755539229114, "stop": 1755539251618, "duration": 22504}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "176a0fdfd9c4144b8dfe3d66a049fda5"}, {"name": "test_open_flashlight", "children": [{"name": "测试open flashlight", "uid": "1092ee4849172c85", "parentUid": "649e9c0b88c706f3f0551679bed64e42", "status": "passed", "time": {"start": 1755539265631, "stop": 1755539290669, "duration": 25038}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649e9c0b88c706f3f0551679bed64e42"}, {"name": "test_open_wifi", "children": [{"name": "测试open wifi", "uid": "20b6e0e752922848", "parentUid": "f9eadf40e8c6f09400b67a9959223d07", "status": "passed", "time": {"start": 1755539304705, "stop": 1755539328368, "duration": 23663}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9eadf40e8c6f09400b67a9959223d07"}, {"name": "test_power_off", "children": [{"name": "测试power off能正常执行", "uid": "73f1f654df418a3e", "parentUid": "cc3a6f764485169c77c1128d52c6bffb", "status": "skipped", "time": {"start": 1755539329699, "stop": 1755539329699, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "cc3a6f764485169c77c1128d52c6bffb"}, {"name": "test_power_saving", "children": [{"name": "测试power saving能正常执行", "uid": "d25630d6258acf1e", "parentUid": "c06c413949d25e13cefc3d7381223a9b", "status": "passed", "time": {"start": 1755539342207, "stop": 1755539370883, "duration": 28676}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c06c413949d25e13cefc3d7381223a9b"}, {"name": "test_screen_record", "children": [{"name": "测试screen record能正常执行", "uid": "d987956ec23c66d0", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1755539384585, "stop": 1755539411294, "duration": 26709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "7bbe18dc462c8bb2", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1755539425460, "stop": 1755539452536, "duration": 27076}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b54fab3751e243d1b424e37d5d287ec3"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "f4f27779b7087710", "parentUid": "b51793332bcfdc1c2f251c5ba2c434e5", "status": "passed", "time": {"start": 1755539466302, "stop": 1755539496281, "duration": 29979}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b51793332bcfdc1c2f251c5ba2c434e5"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "320a4646a2dfbba3", "parentUid": "afeb5aed32d36d5eaa4b132ea5b382e0", "status": "failed", "time": {"start": 1755539510703, "stop": 1755539534441, "duration": 23738}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afeb5aed32d36d5eaa4b132ea5b382e0"}, {"name": "test_set_alarm_volume", "children": [{"name": "测试set alarm volume 50", "uid": "49ea15c67117169b", "parentUid": "1c45deff54cbb6d416ba43bacd27945b", "status": "passed", "time": {"start": 1755539548781, "stop": 1755539572811, "duration": 24030}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c45deff54cbb6d416ba43bacd27945b"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "305a8e737b98a962", "parentUid": "b6e60ab86df5fa2a1124b5d765cbfe9a", "status": "passed", "time": {"start": 1755539586962, "stop": 1755539621174, "duration": 34212}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6e60ab86df5fa2a1124b5d765cbfe9a"}, {"name": "test_set_my_alarm_volume_to", "children": [{"name": "测试set my alarm volume to 50%", "uid": "328cb95c896f8b39", "parentUid": "bba654dbbf79ef15853a19abfddc76e4", "status": "failed", "time": {"start": 1755539635251, "stop": 1755539658539, "duration": 23288}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bba654dbbf79ef15853a19abfddc76e4"}, {"name": "test_set_notifications_volume_to", "children": [{"name": "测试set notifications volume to 50能正常执行", "uid": "684fcf296df9cee5", "parentUid": "8941ad1b28523da85fde9e91215c0464", "status": "failed", "time": {"start": 1755539672907, "stop": 1755539696420, "duration": 23513}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8941ad1b28523da85fde9e91215c0464"}, {"name": "test_set_ringtone_volume_to", "children": [{"name": "测试set ringtone volume to 50能正常执行", "uid": "feb779f3ce9b15a7", "parentUid": "9dd83889897a00276a8e889caae40a1f", "status": "passed", "time": {"start": 1755539710917, "stop": 1755539734868, "duration": 23951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9dd83889897a00276a8e889caae40a1f"}, {"name": "test_set_screen_to_maximum_brightness", "children": [{"name": "测试set screen to maximum brightness能正常执行", "uid": "b8620cc65f4a7b43", "parentUid": "545283340cc920a78362ea8849374231", "status": "passed", "time": {"start": 1755539749356, "stop": 1755539772999, "duration": 23643}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "545283340cc920a78362ea8849374231"}, {"name": "test_set_the_alarm_at_9_o_clock_on_weekends", "children": [{"name": "测试set the alarm at 9 o'clock on weekends", "uid": "cfc600f66f56271b", "parentUid": "3921f68f918e595aa1edf4f965392437", "status": "passed", "time": {"start": 1755539787019, "stop": 1755539810024, "duration": 23005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3921f68f918e595aa1edf4f965392437"}, {"name": "test_smart_charge", "children": [{"name": "测试smart charge能正常执行", "uid": "abcbb8c6f6452862", "parentUid": "38c8329119fb1c04106bbcd94b91ce11", "status": "failed", "time": {"start": 1755539824246, "stop": 1755539847138, "duration": 22892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38c8329119fb1c04106bbcd94b91ce11"}, {"name": "test_start_record", "children": [{"name": "测试start record能正常执行", "uid": "97499e652f70f1f2", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "passed", "time": {"start": 1755539861533, "stop": 1755539917984, "duration": 56451}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9018ab08ec79118ab3c7ac811af9f4fa"}, {"name": "test_start_screen_recording", "children": [{"name": "测试start screen recording能正常执行", "uid": "86a88237508b1115", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1755539931947, "stop": 1755539958940, "duration": 26993}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "4d5cccf79669ff0c", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1755539973003, "stop": 1755539997769, "duration": 24766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "7547d8dd682ea094", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1755540011356, "stop": 1755540036420, "duration": 25064}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "e82690830b99a38a", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1755540050262, "stop": 1755540077630, "duration": 27368}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a083bce7f3fa2d36f16068ac034f4e62"}, {"name": "test_stop_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "2f7ba4c5d127a68c", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1755540091534, "stop": 1755540118119, "duration": 26585}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "8290a3a5e5bcf3f4", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1755540132285, "stop": 1755540155496, "duration": 23211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4eb0eb9df164551aeca11800ab680c92"}, {"name": "test_switch_charging_modes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "523026d721903565", "parentUid": "43ff861df9a59eeef7ac3edd50740e02", "status": "failed", "time": {"start": 1755540169131, "stop": 1755540191004, "duration": 21873}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43ff861df9a59eeef7ac3edd50740e02"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "6b9bb04eb7f0c695", "parentUid": "035194a70f8a76a4b76a28f704b8ff10", "status": "passed", "time": {"start": 1755540205374, "stop": 1755540227557, "duration": 22183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "035194a70f8a76a4b76a28f704b8ff10"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "1c736fd2e0572192", "parentUid": "661f1bc7755dcb73f23369637ff67460", "status": "passed", "time": {"start": 1755540241758, "stop": 1755540263880, "duration": 22122}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "661f1bc7755dcb73f23369637ff67460"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "1c8fd1a76b13782e", "parentUid": "71d9b05a5d33eaee0a7365eb0716032e", "status": "passed", "time": {"start": 1755540277666, "stop": 1755540299565, "duration": 21899}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d9b05a5d33eaee0a7365eb0716032e"}, {"name": "test_switch_to_default_mode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "c86277f1fc22de8e", "parentUid": "62ed32eba114769fb134a432054912e4", "status": "failed", "time": {"start": 1755540313809, "stop": 1755540345990, "duration": 32181}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62ed32eba114769fb134a432054912e4"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "9002bdf24cf5753c", "parentUid": "2d94b30b5f27ad8b3f8967a2a3fdebe3", "status": "passed", "time": {"start": 1755540359966, "stop": 1755540381952, "duration": 21986}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d94b30b5f27ad8b3f8967a2a3fdebe3"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "ab28f76d4ee5ffab", "parentUid": "561ddfab014b4de5926c248cf15d4c27", "status": "failed", "time": {"start": 1755540396088, "stop": 1755540428767, "duration": 32679}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "561ddfab014b4de5926c248cf15d4c27"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "576291327acaf0e2", "parentUid": "99e29771f7571a2fc146c069d7b2c125", "status": "passed", "time": {"start": 1755540443086, "stop": 1755540465098, "duration": 22012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99e29771f7571a2fc146c069d7b2c125"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "faf45958c476966a", "parentUid": "e6a1e2156d8852f07854b84613f04720", "status": "passed", "time": {"start": 1755540479232, "stop": 1755540501365, "duration": 22133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6a1e2156d8852f07854b84613f04720"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "a0520ca561c270a8", "parentUid": "dd9e8f3389e8ba0ae9839c873338d760", "status": "passed", "time": {"start": 1755540515305, "stop": 1755540537560, "duration": 22255}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd9e8f3389e8ba0ae9839c873338d760"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "55991e123833109c", "parentUid": "a22da6d101545dc9b0d8edfda3746b20", "status": "failed", "time": {"start": 1755540551537, "stop": 1755540573104, "duration": 21567}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a22da6d101545dc9b0d8edfda3746b20"}, {"name": "test_switched_to_data_mode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "e25e8aa73ba38521", "parentUid": "2902f5b25b15010f9670892ff424d92c", "status": "passed", "time": {"start": 1755540587325, "stop": 1755540611357, "duration": 24032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2902f5b25b15010f9670892ff424d92c"}, {"name": "test_take_a_photo", "children": [{"name": "测试take a photo能正常执行", "uid": "75e3e5fe8cdf881b", "parentUid": "07997292671f0c2fc7c4f6639faf371f", "status": "passed", "time": {"start": 1755540625588, "stop": 1755540668510, "duration": 42922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07997292671f0c2fc7c4f6639faf371f"}, {"name": "test_take_a_selfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "a02a9851e8f97808", "parentUid": "b76edbc7be133088ff6cc7dcf79d0688", "status": "passed", "time": {"start": 1755540682283, "stop": 1755540724540, "duration": 42257}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b76edbc7be133088ff6cc7dcf79d0688"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "d83d0ba4b2e5fcc3", "parentUid": "fd16a15b2bee6fa00e607a69966a816d", "status": "passed", "time": {"start": 1755540738840, "stop": 1755540766433, "duration": 27593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd16a15b2bee6fa00e607a69966a816d"}, {"name": "test_turn_down_alarm_clock_volume", "children": [{"name": "测试turn down alarm clock volume", "uid": "e21f057e4d8226ef", "parentUid": "058fbd49b611700b8e525883b7a715c7", "status": "passed", "time": {"start": 1755540780248, "stop": 1755540803102, "duration": 22854}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "058fbd49b611700b8e525883b7a715c7"}, {"name": "test_turn_down_notifications_volume", "children": [{"name": "测试turn down notifications volume能正常执行", "uid": "9b668cc2394b5bc4", "parentUid": "653ec2baabc9caa267ed836aab8220ec", "status": "passed", "time": {"start": 1755540817193, "stop": 1755540839637, "duration": 22444}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "653ec2baabc9caa267ed836aab8220ec"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "6179f1c471bf7b0", "parentUid": "550486b451765b696814b7b6743f77fe", "status": "passed", "time": {"start": 1755540853327, "stop": 1755540875951, "duration": 22624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "550486b451765b696814b7b6743f77fe"}, {"name": "test_turn_down_the_brightness_to_the_min", "children": [{"name": "测试turn down the brightness to the min能正常执行", "uid": "3e1ef9c9539919e2", "parentUid": "4a960850c7bf239ac31ab236d66e878f", "status": "passed", "time": {"start": 1755540890128, "stop": 1755540912248, "duration": 22120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a960850c7bf239ac31ab236d66e878f"}, {"name": "test_turn_off_adaptive_brightness", "children": [{"name": "测试turn off adaptive brightness能正常执行", "uid": "ce4bd0701fd3c3fd", "parentUid": "3e70ee736f216554340491db70fd8257", "status": "passed", "time": {"start": 1755540926281, "stop": 1755540949515, "duration": 23234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e70ee736f216554340491db70fd8257"}, {"name": "test_turn_off_auto_rotate_screen", "children": [{"name": "测试turn off auto rotate screen能正常执行", "uid": "75fa29c2105e19b3", "parentUid": "9029516e3ad0598210caeb84ca36028a", "status": "passed", "time": {"start": 1755540963765, "stop": 1755540986553, "duration": 22788}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9029516e3ad0598210caeb84ca36028a"}, {"name": "test_turn_off_flashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "4eadf7aaf6065e14", "parentUid": "990086735fb4bd184a8ac5a25a5f821c", "status": "passed", "time": {"start": 1755541000559, "stop": 1755541024984, "duration": 24425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990086735fb4bd184a8ac5a25a5f821c"}, {"name": "test_turn_off_light_theme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "bf3e0d5d8588af5b", "parentUid": "83d0ab198b941cca60896fe1448789bd", "status": "passed", "time": {"start": 1755541038774, "stop": 1755541060765, "duration": 21991}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83d0ab198b941cca60896fe1448789bd"}, {"name": "test_turn_off_nfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "5c1299382f028a5e", "parentUid": "25370c24404047b47256854554ecf19e", "status": "passed", "time": {"start": 1755541074574, "stop": 1755541099089, "duration": 24515}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25370c24404047b47256854554ecf19e"}, {"name": "test_turn_off_smart_reminder", "children": [{"name": "测试turn off smart reminder能正常执行", "uid": "ce0cbaec22f70255", "parentUid": "69969f8163b3af4121192a7c63c3fa49", "status": "passed", "time": {"start": 1755541113139, "stop": 1755541135920, "duration": 22781}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69969f8163b3af4121192a7c63c3fa49"}, {"name": "test_turn_on_adaptive_brightness", "children": [{"name": "测试turn on adaptive brightness能正常执行", "uid": "774b232eb8b4279", "parentUid": "8074f6de3cc4d8e0ee673e8436e36098", "status": "failed", "time": {"start": 1755541150238, "stop": 1755541173024, "duration": 22786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8074f6de3cc4d8e0ee673e8436e36098"}, {"name": "test_turn_on_airplane_mode", "children": [{"name": "测试turn on airplane mode能正常执行", "uid": "155df7699608c58e", "parentUid": "01abd5dec02dc63c8d78a8b97b6ef4b1", "status": "skipped", "time": {"start": 1755541174560, "stop": 1755541174560, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='airplane mode 会导致设备断开网络，先跳过')"]}], "uid": "01abd5dec02dc63c8d78a8b97b6ef4b1"}, {"name": "test_turn_on_auto_rotate_screen", "children": [{"name": "测试turn on auto rotate screen能正常执行", "uid": "ef8f8d42edcb4f66", "parentUid": "ae898b5cc3303c318290953f962dbbd3", "status": "failed", "time": {"start": 1755541186998, "stop": 1755541209769, "duration": 22771}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae898b5cc3303c318290953f962dbbd3"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "eeaa4130f4971e69", "parentUid": "06858c4ec997c86c877ce6ab09e3bbaf", "status": "passed", "time": {"start": 1755541224153, "stop": 1755541246179, "duration": 22026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "06858c4ec997c86c877ce6ab09e3bbaf"}, {"name": "test_turn_on_brightness_to_80", "children": [{"name": "测试turn on brightness to 80能正常执行", "uid": "d0217fa5fc8a4a81", "parentUid": "2fb18876082f3ff4b34b7ccd72bb7b28", "status": "passed", "time": {"start": 1755541260399, "stop": 1755541282499, "duration": 22100}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2fb18876082f3ff4b34b7ccd72bb7b28"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "baa21503fd9a96ca", "parentUid": "869ae35c254b699074c4a890b2986c6d", "status": "passed", "time": {"start": 1755541296337, "stop": 1755541319475, "duration": 23138}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "869ae35c254b699074c4a890b2986c6d"}, {"name": "test_turn_on_light_theme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "7d0c5927796b6392", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1755541333638, "stop": 1755541355918, "duration": 22280}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "3c7c9f887c88cd32", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1755541369911, "stop": 1755541391953, "duration": 22042}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9684df9c422f9cf3eef3d6f0ddd6607c"}, {"name": "test_turn_on_location_services", "children": [{"name": "测试turn on location services能正常执行", "uid": "95216e3dbb2b6a8e", "parentUid": "13b991141a19f0fe4b82a01990c6a851", "status": "passed", "time": {"start": 1755541406362, "stop": 1755541429561, "duration": 23199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13b991141a19f0fe4b82a01990c6a851"}, {"name": "test_turn_on_nfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "eb294921beeb20c3", "parentUid": "ac9c5a7d76d32a3f0024f1bbc4260940", "status": "failed", "time": {"start": 1755541443841, "stop": 1755541468196, "duration": 24355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac9c5a7d76d32a3f0024f1bbc4260940"}, {"name": "test_turn_on_smart_reminder", "children": [{"name": "测试turn on smart reminder能正常执行", "uid": "bd361d609157db", "parentUid": "2ea8c3d3e0404ec5eb56e144f27b3b76", "status": "failed", "time": {"start": 1755541482387, "stop": 1755541505404, "duration": 23017}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ea8c3d3e0404ec5eb56e144f27b3b76"}, {"name": "test_turn_on_the_7am_alarm", "children": [{"name": "测试turn on the 7AM alarm", "uid": "4b737429b4e03ee3", "parentUid": "20bb02d2344b8a65effdb63f9de8d6cf", "status": "passed", "time": {"start": 1755541519387, "stop": 1755541541302, "duration": 21915}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20bb02d2344b8a65effdb63f9de8d6cf"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "563422272498211e", "parentUid": "c5e4b9b650952bb94f49d9ea64fb9376", "status": "passed", "time": {"start": 1755541555184, "stop": 1755541580044, "duration": 24860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5e4b9b650952bb94f49d9ea64fb9376"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "bcecb343f6bed949", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "passed", "time": {"start": 1755541594412, "stop": 1755541621649, "duration": 27237}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "9b5c88885de7c800", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "failed", "time": {"start": 1755541635822, "stop": 1755541662964, "duration": 27142}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cf049f3605e39b97e2902b74b81323d"}, {"name": "test_turn_on_wifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "937a6a1b80540fdf", "parentUid": "8f8981d5b6534e5c462a0d8cbafb4302", "status": "passed", "time": {"start": 1755541676941, "stop": 1755541700497, "duration": 23556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f8981d5b6534e5c462a0d8cbafb4302"}, {"name": "test_turn_up_alarm_clock_volume", "children": [{"name": "测试turn up alarm clock volume", "uid": "78d354cf604a4076", "parentUid": "dcce4917ed7cc8e4416996e747de9c57", "status": "passed", "time": {"start": 1755541714266, "stop": 1755541736705, "duration": 22439}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcce4917ed7cc8e4416996e747de9c57"}, {"name": "test_turn_up_notifications_volume", "children": [{"name": "测试turn up notifications volume能正常执行", "uid": "15453db92b625b22", "parentUid": "b0cf36b6141ae92300d37ba7f5cc28b4", "status": "passed", "time": {"start": 1755541750629, "stop": 1755541773466, "duration": 22837}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0cf36b6141ae92300d37ba7f5cc28b4"}, {"name": "test_turn_up_ring_volume", "children": [{"name": "测试turn up ring volume能正常执行", "uid": "981e04ed084bd6b0", "parentUid": "d5f810cb9cfc767a77e9451e7e822fb9", "status": "passed", "time": {"start": 1755541787396, "stop": 1755541810217, "duration": 22821}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5f810cb9cfc767a77e9451e7e822fb9"}, {"name": "test_turn_up_the_brightness_to_the_max", "children": [{"name": "测试turn up the brightness to the max能正常执行", "uid": "2bb3ce4693c21548", "parentUid": "6357b07572538890d600f578a4c3c4aa", "status": "passed", "time": {"start": 1755541823865, "stop": 1755541846352, "duration": 22487}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6357b07572538890d600f578a4c3c4aa"}, {"name": "test_turn_up_the_volume_to_the_max", "children": [{"name": "测试turn up the volume to the max能正常执行", "uid": "acf343c73d15018b", "parentUid": "2d41809a57eff7adaff72dd6fb331012", "status": "passed", "time": {"start": 1755541859998, "stop": 1755541882503, "duration": 22505}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d41809a57eff7adaff72dd6fb331012"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "a7eef724e114d433", "parentUid": "193cbbfa30db6a41aacdad7a864e09bc", "status": "failed", "time": {"start": 1755541896311, "stop": 1755541918389, "duration": 22078}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "193cbbfa30db6a41aacdad7a864e09bc"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "245590744fd1760c", "parentUid": "a69c8f4ca42c7f7233299fb00d064c9f", "status": "passed", "time": {"start": 1755541932633, "stop": 1755541964641, "duration": 32008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a69c8f4ca42c7f7233299fb00d064c9f"}], "uid": "93fc5475dc5c7ad817f2b1ab5b0ac7b9"}, {"name": "third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "测试download app能正常执行", "uid": "4ed39439d861cebf", "parentUid": "b132f6b6f7a47dbe39c1fb7de000fe78", "status": "passed", "time": {"start": 1755541978279, "stop": 1755542002141, "duration": 23862}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b132f6b6f7a47dbe39c1fb7de000fe78"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball能正常执行", "uid": "5a0f62687113a756", "parentUid": "9ad7d52518459e93bf395586c4de03d3", "status": "failed", "time": {"start": 1755542015938, "stop": 1755542039422, "duration": 23484}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9ad7d52518459e93bf395586c4de03d3"}, {"name": "test_download_qq", "children": [{"name": "测试download qq能正常执行", "uid": "cf4417d6abdc10b1", "parentUid": "88f428f9095db30aa10835e50fff2f44", "status": "passed", "time": {"start": 1755542053420, "stop": 1755542086701, "duration": 33281}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88f428f9095db30aa10835e50fff2f44"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "354bfa2b8e5a4d90", "parentUid": "990b9417ef5d8284b5ed1c8f1dcb2456", "status": "passed", "time": {"start": 1755542100645, "stop": 1755542138165, "duration": 37520}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990b9417ef5d8284b5ed1c8f1dcb2456"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "d621004ece966b52", "parentUid": "98910e36f666a4645d039127f271b15c", "status": "passed", "time": {"start": 1755542152419, "stop": 1755542185515, "duration": 33096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98910e36f666a4645d039127f271b15c"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "467ff75891f341fb", "parentUid": "b5db47a2899bd2831280d03f6ab5ccde", "status": "passed", "time": {"start": 1755542199306, "stop": 1755542229004, "duration": 29698}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5db47a2899bd2831280d03f6ab5ccde"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "5f18b39a20f5d622", "parentUid": "63d4dbd18e96d1791e9029023802405a", "status": "passed", "time": {"start": 1755542242740, "stop": 1755542271847, "duration": 29107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "63d4dbd18e96d1791e9029023802405a"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "32df01cfab8864af", "parentUid": "26d631af39b9512dace23d786ed426bc", "status": "passed", "time": {"start": 1755542285440, "stop": 1755542316964, "duration": 31524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26d631af39b9512dace23d786ed426bc"}, {"name": "test_open_facebook", "children": [{"name": "测试open facebook能正常执行", "uid": "41a258194b47f5ba", "parentUid": "28034a46d61d0cc3fef6e9ffd4db7994", "status": "passed", "time": {"start": 1755542330966, "stop": 1755542360458, "duration": 29492}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28034a46d61d0cc3fef6e9ffd4db7994"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "2e458485db602770", "parentUid": "300a56019ec17b2ba7bcf48ab420b3c3", "status": "passed", "time": {"start": 1755542374615, "stop": 1755542398281, "duration": 23666}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "300a56019ec17b2ba7bcf48ab420b3c3"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger能正常执行", "uid": "190a2c7d515274c1", "parentUid": "2f48f1343c12cc6c07d8e886b62e4c0d", "status": "failed", "time": {"start": 1755542412322, "stop": 1755542433838, "duration": 21516}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2f48f1343c12cc6c07d8e886b62e4c0d"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "4bb0f3490eb7fd10", "parentUid": "540441448375adc4953206d10f542ed2", "status": "failed", "time": {"start": 1755542448115, "stop": 1755542469919, "duration": 21804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "540441448375adc4953206d10f542ed2"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "68e268cc4fc1b311", "parentUid": "18df90aff668e599fc314e80b0851b3d", "status": "passed", "time": {"start": 1755542483785, "stop": 1755542507616, "duration": 23831}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18df90aff668e599fc314e80b0851b3d"}, {"name": "test_whatsapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "6e8ca94f901f3575", "parentUid": "3936daaae04788e4f3494c8c7b4dd2c4", "status": "passed", "time": {"start": 1755542521457, "stop": 1755542545051, "duration": 23594}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3936daaae04788e4f3494c8c7b4dd2c4"}], "uid": "f10231acc09c45f9430bedaedd076cbf"}, {"name": "unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "b9851214b3a35736", "parentUid": "b7d205f78a6f3e533fa2c219a8aae3b4", "status": "passed", "time": {"start": 1755542559339, "stop": 1755542581522, "duration": 22183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7d205f78a6f3e533fa2c219a8aae3b4"}, {"name": "test_Language_List", "children": [{"name": "测试Language List", "uid": "372bf679db832d71", "parentUid": "acd49ea16884c92b4bc0827ff1a9b518", "status": "passed", "time": {"start": 1755542595818, "stop": 1755542617831, "duration": 22013}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd49ea16884c92b4bc0827ff1a9b518"}, {"name": "test_a_clear_and_pink_crystal_necklace_in_the_water", "children": [{"name": "测试a clear and pink crystal necklace in the water", "uid": "bde5993ab80214c5", "parentUid": "868b92d4860fe62d37a238a2a03ab514", "status": "passed", "time": {"start": 1755542631858, "stop": 1755542657387, "duration": 25529}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "868b92d4860fe62d37a238a2a03ab514"}, {"name": "test_a_clear_glass_cup", "children": [{"name": "测试a clear glass cup", "uid": "f87fb922cd22cb5e", "parentUid": "7c33673a4b0e7e0bb3ee397c5642873f", "status": "passed", "time": {"start": 1755542671215, "stop": 1755542697241, "duration": 26026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c33673a4b0e7e0bb3ee397c5642873f"}, {"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "测试A cute little boy is skiing", "uid": "bd35946baa6a9c7c", "parentUid": "39ee1e792baffbc9f8bd43921c2e8f29", "status": "passed", "time": {"start": 1755542711300, "stop": 1755542736578, "duration": 25278}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39ee1e792baffbc9f8bd43921c2e8f29"}, {"name": "test_a_cute_little_girl_with_long_hair", "children": [{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "70a5f4ae04e6558e", "parentUid": "b95f5c90bba17331cb7f1bfb4dc8e34c", "status": "failed", "time": {"start": 1755542750283, "stop": 1755542772092, "duration": 21809}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b95f5c90bba17331cb7f1bfb4dc8e34c"}, {"name": "test_a_furry_little_monkey", "children": [{"name": "测试A furry little monkey", "uid": "77992e69d66afef9", "parentUid": "8edf4e99036a18e605daf4b2a5d6d006", "status": "failed", "time": {"start": 1755542786715, "stop": 1755542811669, "duration": 24954}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8edf4e99036a18e605daf4b2a5d6d006"}, {"name": "test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance", "children": [{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "30209a062596ef82", "parentUid": "0b181376538184b1e498cc28c7d921b2", "status": "failed", "time": {"start": 1755542825673, "stop": 1755542852185, "duration": 26512}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b181376538184b1e498cc28c7d921b2"}, {"name": "test_a_little_raccoon_walks_on_a_forest_meadow", "children": [{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "f1406a88d312092e", "parentUid": "125ccc5d6c2cd29e3eff4f495de03e9d", "status": "failed", "time": {"start": 1755542869874, "stop": 1755542891488, "duration": 21614}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "125ccc5d6c2cd29e3eff4f495de03e9d"}, {"name": "test_a_photo_of_a_transparent_glass_cup", "children": [{"name": "测试A photo of a transparent glass cup ", "uid": "fae32e11992942f2", "parentUid": "b32afb34a72d8a3b432ccecaeb467c06", "status": "failed", "time": {"start": 1755542905521, "stop": 1755542927616, "duration": 22095}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b32afb34a72d8a3b432ccecaeb467c06"}, {"name": "test_a_sports_car_is_parked_on_the_street_side", "children": [{"name": "测试A sports car is parked on the street side", "uid": "ee55ab8579ed7a97", "parentUid": "e8e5fdd20e1395cd53e5a7bc120140da", "status": "passed", "time": {"start": 1755542941770, "stop": 1755542967243, "duration": 25473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8e5fdd20e1395cd53e5a7bc120140da"}, {"name": "test_call_mom", "children": [{"name": "测试call mom", "uid": "8be6bece5fd1af0d", "parentUid": "69781a4c71a996e5064410023e23e4af", "status": "failed", "time": {"start": 1755542980983, "stop": 1755543012019, "duration": 31036}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69781a4c71a996e5064410023e23e4af"}, {"name": "test_call_number_by_whatsapp", "children": [{"name": "测试call number by whatsapp能正常执行", "uid": "a36e4a54b6cc5d0", "parentUid": "6e03192eee0fc1e49b67b96aab8f4f9d", "status": "passed", "time": {"start": 1755543026002, "stop": 1755543057161, "duration": 31159}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e03192eee0fc1e49b67b96aab8f4f9d"}, {"name": "test_can_u_check_the_notebook", "children": [{"name": "测试can u check the notebook", "uid": "dad0ac13e605048a", "parentUid": "411f9a4aa5205c0bfd9405f0b10d5637", "status": "passed", "time": {"start": 1755543071377, "stop": 1755543106052, "duration": 34675}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "411f9a4aa5205c0bfd9405f0b10d5637"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "3e0c9fe5dca6e346", "parentUid": "e304012317c1b3a1f774488d1d79d607", "status": "passed", "time": {"start": 1755543119889, "stop": 1755543142147, "duration": 22258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e304012317c1b3a1f774488d1d79d607"}, {"name": "test_change_man_voice", "children": [{"name": "测试change man voice能正常执行", "uid": "4e20f74cb4938ea2", "parentUid": "9a2cbc470fe28892afde5493b609c218", "status": "passed", "time": {"start": 1755543156187, "stop": 1755543177815, "duration": 21628}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a2cbc470fe28892afde5493b609c218"}, {"name": "test_change_your_voice", "children": [{"name": "测试change your voice能正常执行", "uid": "753d92772d675cae", "parentUid": "0aec3e6cbfb649afcf2d7319501ab2f5", "status": "passed", "time": {"start": 1755543191647, "stop": 1755543214228, "duration": 22581}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aec3e6cbfb649afcf2d7319501ab2f5"}, {"name": "test_check_battery_information", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "6325a8bfe82ee58", "parentUid": "8757fd926e61f6ebb245d3d3d474c927", "status": "passed", "time": {"start": 1755543228368, "stop": 1755543250570, "duration": 22202}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8757fd926e61f6ebb245d3d3d474c927"}, {"name": "test_check_contact", "children": [{"name": "测试check contact能正常执行", "uid": "130b6c368ae3f475", "parentUid": "a40100045be74641430d892fba85ca45", "status": "passed", "time": {"start": 1755543264398, "stop": 1755543295558, "duration": 31160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a40100045be74641430d892fba85ca45"}, {"name": "test_check_contacts", "children": [{"name": "测试check contacts能正常执行", "uid": "6966801bd55088f5", "parentUid": "2d2788a266628ad79e779a5222860ff2", "status": "passed", "time": {"start": 1755543309236, "stop": 1755543340429, "duration": 31193}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d2788a266628ad79e779a5222860ff2"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "858c22d7630a2a7d", "parentUid": "837efc09915a750bd95f96f1811eb69d", "status": "passed", "time": {"start": 1755543354491, "stop": 1755543376726, "duration": 22235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "837efc09915a750bd95f96f1811eb69d"}, {"name": "test_check_model_information", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "5d13ab9424ca7076", "parentUid": "78831bedd8240ff30a47ed4c298163e6", "status": "passed", "time": {"start": 1755543390467, "stop": 1755543412287, "duration": 21820}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78831bedd8240ff30a47ed4c298163e6"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "2488d85e6c4e33d8", "parentUid": "bddf49f414a6afcdd8f3aa88c2990b73", "status": "passed", "time": {"start": 1755543426204, "stop": 1755543448737, "duration": 22533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bddf49f414a6afcdd8f3aa88c2990b73"}, {"name": "test_check_my_to_do_list", "children": [{"name": "测试check my to-do list能正常执行", "uid": "11fc47f08ff73cab", "parentUid": "941e24236863d467719d0e685e22e88f", "status": "passed", "time": {"start": 1755543462596, "stop": 1755543484509, "duration": 21913}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "941e24236863d467719d0e685e22e88f"}, {"name": "test_check_ram_information", "children": [{"name": "测试check ram information", "uid": "18dcfed9cffe3fc1", "parentUid": "80af6b2bcd88de0caeb66147d7b56775", "status": "passed", "time": {"start": 1755543498357, "stop": 1755543520294, "duration": 21937}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80af6b2bcd88de0caeb66147d7b56775"}, {"name": "test_check_rear_camera_information", "children": [{"name": "测试check rear camera information能正常执行", "uid": "59ce51639a430d6d", "parentUid": "0f68e4cddcec3d7808a4b93d7289e24d", "status": "passed", "time": {"start": 1755543534411, "stop": 1755543556865, "duration": 22454}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f68e4cddcec3d7808a4b93d7289e24d"}, {"name": "test_check_system_update", "children": [{"name": "测试check system update", "uid": "ecc71f2f493e1262", "parentUid": "0275cb25e1cc42b06e4cc93e842b4b50", "status": "failed", "time": {"start": 1755543571254, "stop": 1755543593013, "duration": 21759}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0275cb25e1cc42b06e4cc93e842b4b50"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "d5c602fd9982fe2", "parentUid": "2a21ec0aa1162e7205ed4479032eec28", "status": "passed", "time": {"start": 1755543607155, "stop": 1755543628894, "duration": 21739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a21ec0aa1162e7205ed4479032eec28"}, {"name": "test_close_performance_mode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "4b4b33da4101679c", "parentUid": "95753a1df5aed1e2d1f53aaac547d09e", "status": "passed", "time": {"start": 1755543642589, "stop": 1755543664873, "duration": 22284}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "95753a1df5aed1e2d1f53aaac547d09e"}, {"name": "test_close_power_saving_mode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "859cd1ac4096dca4", "parentUid": "cc7047e4d72a0d0f3090e6371b0aadaf", "status": "passed", "time": {"start": 1755543678926, "stop": 1755543701149, "duration": 22223}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc7047e4d72a0d0f3090e6371b0aadaf"}, {"name": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "children": [{"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "uid": "6e8cfebab1b113de", "parentUid": "f3c878dcefcd898c51bcf86ea1fe78fa", "status": "passed", "time": {"start": 1755543714952, "stop": 1755543736798, "duration": 21846}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3c878dcefcd898c51bcf86ea1fe78fa"}, {"name": "test_dial_the_number_on_the_screen", "children": [{"name": "测试Dial the number on the screen", "uid": "e6a76f74a137ce40", "parentUid": "9e3a850134620a6df84f6f9e714c5644", "status": "passed", "time": {"start": 1755543750696, "stop": 1755543773049, "duration": 22353}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3a850134620a6df84f6f9e714c5644"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "ab36b5e97a9a304d", "parentUid": "eb416fd31892d7985df8f4a00fa61281", "status": "passed", "time": {"start": 1755543787128, "stop": 1755543809387, "duration": 22259}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb416fd31892d7985df8f4a00fa61281"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "15de6c1c1a1bde89", "parentUid": "ae74f6051eb946be44a497a5345a6122", "status": "passed", "time": {"start": 1755543823441, "stop": 1755543845293, "duration": 21852}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae74f6051eb946be44a497a5345a6122"}, {"name": "test_disable_auto_pickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "2c81a4a04cdc1071", "parentUid": "12db62453bb023c9292784bafa6f3501", "status": "passed", "time": {"start": 1755543859540, "stop": 1755543881464, "duration": 21924}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12db62453bb023c9292784bafa6f3501"}, {"name": "test_disable_brightness_locking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "cd7d712956ba0f25", "parentUid": "3dcacd9d4311d09538f3bfd9137d3407", "status": "passed", "time": {"start": 1755543895172, "stop": 1755543917265, "duration": 22093}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dcacd9d4311d09538f3bfd9137d3407"}, {"name": "test_disable_call_rejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "f47927df2a6b5ce3", "parentUid": "2df8406295adf2f3ca8780a6fd181bd8", "status": "passed", "time": {"start": 1755543931278, "stop": 1755543962526, "duration": 31248}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2df8406295adf2f3ca8780a6fd181bd8"}, {"name": "test_disable_hide_notifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "d3dea9e9d4c15593", "parentUid": "4f012dc2e8bb211fd5ead2f1f23cd42d", "status": "passed", "time": {"start": 1755543976460, "stop": 1755543998487, "duration": 22027}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f012dc2e8bb211fd5ead2f1f23cd42d"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "de12e238457e041e", "parentUid": "a6c9b5ce2532611959a8b1cf0b7abdf0", "status": "passed", "time": {"start": 1755544012434, "stop": 1755544034816, "duration": 22382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6c9b5ce2532611959a8b1cf0b7abdf0"}, {"name": "test_disable_network_enhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "ae0d0bc69c535867", "parentUid": "4c36c5378c3644d7cdbf63c08440a304", "status": "passed", "time": {"start": 1755544049042, "stop": 1755544070936, "duration": 21894}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c36c5378c3644d7cdbf63c08440a304"}, {"name": "test_disable_running_lock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "1c93ac7f69b05022", "parentUid": "bdd8ac18c65aee858c82eb5de9f7b502", "status": "passed", "time": {"start": 1755544085000, "stop": 1755544108381, "duration": 23381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdd8ac18c65aee858c82eb5de9f7b502"}, {"name": "test_disable_touch_optimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "d93759f5df6d71b2", "parentUid": "7777d1894ae8ebdcc0703d8d7a1d888d", "status": "passed", "time": {"start": 1755544122605, "stop": 1755544144741, "duration": 22136}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7777d1894ae8ebdcc0703d8d7a1d888d"}, {"name": "test_disable_unfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "72118dc54e25e056", "parentUid": "f4f38e300a9eadad2128e98bc9820a33", "status": "passed", "time": {"start": 1755544158549, "stop": 1755544180596, "duration": 22047}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f38e300a9eadad2128e98bc9820a33"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "15cdef69b8beed3e", "parentUid": "b2b3f74c91ec8af12a7b70a1858cb09f", "status": "passed", "time": {"start": 1755544194307, "stop": 1755544216422, "duration": 22115}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2b3f74c91ec8af12a7b70a1858cb09f"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "878b77da97742d88", "parentUid": "ac5ff5b664434d007d90715847bb0efa", "status": "failed", "time": {"start": 1755544230502, "stop": 1755544252918, "duration": 22416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac5ff5b664434d007d90715847bb0efa"}, {"name": "test_download_in_play_store", "children": [{"name": "测试download in play store", "uid": "20d5bddf727c0fba", "parentUid": "ab04619dbfb0da204448c4c9be7b6a4f", "status": "passed", "time": {"start": 1755544267541, "stop": 1755544294143, "duration": 26602}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ab04619dbfb0da204448c4c9be7b6a4f"}, {"name": "test_download_in_playstore", "children": [{"name": "测试download in playstore", "uid": "8f694e90876ac004", "parentUid": "3b042038f79bb86f8776a7ddaad8e1fb", "status": "passed", "time": {"start": 1755544308198, "stop": 1755544335028, "duration": 26830}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b042038f79bb86f8776a7ddaad8e1fb"}, {"name": "test_download_whatsapp", "children": [{"name": "测试download whatsapp能正常执行", "uid": "ef7a15af380820c6", "parentUid": "1f0c2d58931bbc9fa753a998f57e6ee2", "status": "passed", "time": {"start": 1755544348925, "stop": 1755544376897, "duration": 27972}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f0c2d58931bbc9fa753a998f57e6ee2"}, {"name": "test_driving_mode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "e99caa00c608f900", "parentUid": "e021bf6bc9463d8e3bd9aab9475fb30a", "status": "failed", "time": {"start": 1755544390869, "stop": 1755544413086, "duration": 22217}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e021bf6bc9463d8e3bd9aab9475fb30a"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "61c1dc608e36883c", "parentUid": "29814f9b6b5bad3852e86d186b662dab", "status": "passed", "time": {"start": 1755544427265, "stop": 1755544449022, "duration": 21757}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29814f9b6b5bad3852e86d186b662dab"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "e01919838c9e2688", "parentUid": "9b7a1bd9dfc15c1c490b49730b3b06c6", "status": "passed", "time": {"start": 1755544463027, "stop": 1755544485067, "duration": 22040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b7a1bd9dfc15c1c490b49730b3b06c6"}, {"name": "test_enable_auto_pickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "c28179de4e531562", "parentUid": "34950724c1fa063bc98eef873eb35771", "status": "passed", "time": {"start": 1755544499399, "stop": 1755544521558, "duration": 22159}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34950724c1fa063bc98eef873eb35771"}, {"name": "test_enable_brightness_locking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "dc8175669360e0", "parentUid": "fb01cd9d24d4da8385ec6db44b9bf596", "status": "passed", "time": {"start": 1755544535387, "stop": 1755544557375, "duration": 21988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb01cd9d24d4da8385ec6db44b9bf596"}, {"name": "test_enable_call_on_hold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "361a48d619f8b474", "parentUid": "743e0a22021355320010c1959207f737", "status": "passed", "time": {"start": 1755544571427, "stop": 1755544602971, "duration": 31544}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "743e0a22021355320010c1959207f737"}, {"name": "test_enable_call_rejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "abd00a5f90b2dab6", "parentUid": "a7a8abf2aae8cc5f45b540e4f777f037", "status": "passed", "time": {"start": 1755544616887, "stop": 1755544647919, "duration": 31032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7a8abf2aae8cc5f45b540e4f777f037"}, {"name": "test_enable_network_enhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "7c313c685873fd0c", "parentUid": "ecc995aaaa028fb08ab7ae1ab0e7875d", "status": "passed", "time": {"start": 1755544662095, "stop": 1755544684324, "duration": 22229}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ecc995aaaa028fb08ab7ae1ab0e7875d"}, {"name": "test_enable_running_lock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "3f740db0cef2375b", "parentUid": "d576b618126fc7256f34985c398c1d3b", "status": "passed", "time": {"start": 1755544698511, "stop": 1755544721984, "duration": 23473}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d576b618126fc7256f34985c398c1d3b"}, {"name": "test_enable_touch_optimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "a69cd11544f75120", "parentUid": "8480a7d7a44bc6838b96d68a4c18aa05", "status": "passed", "time": {"start": 1755544736044, "stop": 1755544757995, "duration": 21951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8480a7d7a44bc6838b96d68a4c18aa05"}, {"name": "test_enable_unfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "409d638ac1bedc2", "parentUid": "66a93da14003d58b4dfb0006f98aa4c5", "status": "passed", "time": {"start": 1755544772103, "stop": 1755544794095, "duration": 21992}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66a93da14003d58b4dfb0006f98aa4c5"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "5b707a563124442", "parentUid": "8c6899d4ddce4d928d89f60cb07aeea9", "status": "passed", "time": {"start": 1755544808173, "stop": 1755544830192, "duration": 22019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6899d4ddce4d928d89f60cb07aeea9"}, {"name": "test_end_exercising", "children": [{"name": "测试end exercising能正常执行", "uid": "efbf1ae95846c87f", "parentUid": "a21fb30a814209d2df9c14775968c587", "status": "passed", "time": {"start": 1755544844031, "stop": 1755544866050, "duration": 22019}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a21fb30a814209d2df9c14775968c587"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "d5ebc6c67f353f03", "parentUid": "656e7206a16e23ae119789777b609183", "status": "failed", "time": {"start": 1755544880054, "stop": 1755544901864, "duration": 21810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "656e7206a16e23ae119789777b609183"}, {"name": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "children": [{"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "uid": "a216829f25a48ac0", "parentUid": "b958aba74a145b539d7044e12999e87d", "status": "passed", "time": {"start": 1755544915793, "stop": 1755544937936, "duration": 22143}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b958aba74a145b539d7044e12999e87d"}, {"name": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "children": [{"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "uid": "dc42dd62a92d5d19", "parentUid": "158f9e5ecd83a63dfb841fa3bf07b617", "status": "passed", "time": {"start": 1755544951816, "stop": 1755544973951, "duration": 22135}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "158f9e5ecd83a63dfb841fa3bf07b617"}, {"name": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "children": [{"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "uid": "282267417455ce5d", "parentUid": "10aa45bdca32abb792f72299d7d1602a", "status": "passed", "time": {"start": 1755544988234, "stop": 1755545010487, "duration": 22253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10aa45bdca32abb792f72299d7d1602a"}, {"name": "test_generate_a_landscape_painting_image_for_me", "children": [{"name": "测试Generate a landscape painting image for me", "uid": "4b2de58234c4451e", "parentUid": "cd614964dd5077ebe6826f8a658328d5", "status": "failed", "time": {"start": 1755545024642, "stop": 1755545046581, "duration": 21939}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd614964dd5077ebe6826f8a658328d5"}, {"name": "test_generate_a_picture_in_the_night_forest_for_me", "children": [{"name": "测试Generate a picture in the night forest for me", "uid": "14dee738f79101ae", "parentUid": "318e0edf3649f85c5115c2f373b5c98e", "status": "passed", "time": {"start": 1755545061038, "stop": 1755545082942, "duration": 21904}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "318e0edf3649f85c5115c2f373b5c98e"}, {"name": "test_generate_a_picture_of_a_jungle_stream_for_me", "children": [{"name": "测试Generate a picture of a jungle stream for me", "uid": "3409f70c757d998f", "parentUid": "c5a9ca5935e3a68d59f85b9ff612c907", "status": "passed", "time": {"start": 1755545096743, "stop": 1755545118876, "duration": 22133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5a9ca5935e3a68d59f85b9ff612c907"}, {"name": "test_generate_an_image_of_a_chubby_orange_cat_chef", "children": [{"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "uid": "bad169110b617eec", "parentUid": "968ed53b3fe60b381f5f61bec75c01db", "status": "passed", "time": {"start": 1755545132693, "stop": 1755545154594, "duration": 21901}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968ed53b3fe60b381f5f61bec75c01db"}, {"name": "test_go_home", "children": [{"name": "测试go home能正常执行", "uid": "b082323455bd01a3", "parentUid": "4a5220f470f11d89726e23ef156eb88b", "status": "passed", "time": {"start": 1755545168500, "stop": 1755545190680, "duration": 22180}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a5220f470f11d89726e23ef156eb88b"}, {"name": "test_go_to_office", "children": [{"name": "测试go to office", "uid": "dcfc63b23aff804b", "parentUid": "17ff186ceded764016c0155f25eb3375", "status": "passed", "time": {"start": 1755545204884, "stop": 1755545227157, "duration": 22273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "17ff186ceded764016c0155f25eb3375"}, {"name": "test_gold_coin_rain", "children": [{"name": "测试gold coin rain能正常执行", "uid": "7a71af2e1e53eaf4", "parentUid": "4ff40e880339f30d1929194918d94abe", "status": "passed", "time": {"start": 1755545241022, "stop": 1755545265610, "duration": 24588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ff40e880339f30d1929194918d94abe"}, {"name": "test_hamster_mascot", "children": [{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "397b1501c16d5e75", "parentUid": "ac616b983471f9d0a7ed28334712daea", "status": "failed", "time": {"start": 1755545279615, "stop": 1755545304641, "duration": 25026}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac616b983471f9d0a7ed28334712daea"}, {"name": "test_happy_new_year", "children": [{"name": "测试happy new year能正常执行", "uid": "9cecfbff8b58d513", "parentUid": "a0b2ddf900ecce63d74a6e5a2048669c", "status": "passed", "time": {"start": 1755545319005, "stop": 1755545343640, "duration": 24635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0b2ddf900ecce63d74a6e5a2048669c"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "204d2c40a3295f49", "parentUid": "79da41b110afe1954f2965621e69bebc", "status": "passed", "time": {"start": 1755545357716, "stop": 1755545383139, "duration": 25423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79da41b110afe1954f2965621e69bebc"}, {"name": "test_help_generate_a_picture_of_ancient_city", "children": [{"name": "测试A furry little monkey", "uid": "626820f2f317f7d6", "parentUid": "5d23429c1981e990d47168a7d4242b52", "status": "passed", "time": {"start": 1755545397220, "stop": 1755545422399, "duration": 25179}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d23429c1981e990d47168a7d4242b52"}, {"name": "test_help_me_generate_a_3D_rendered_picture_of_a_Song_style_palace_surrounded_by_auspicious_clouds_and_with_delicate_colors", "children": [{"name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "uid": "d98e6b072f4e3cec", "parentUid": "1c7cbdeca2def7d1f1eda877c8cb7276", "status": "passed", "time": {"start": 1755545436695, "stop": 1755545458640, "duration": 21945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c7cbdeca2def7d1f1eda877c8cb7276"}, {"name": "test_help_me_generate_a_photo_of_a_fully_nude_boy", "children": [{"name": "测试Help me generate a photo of a fully nude boy", "uid": "81efefb346d1b1cf", "parentUid": "4a7c46ca503f2c509fdb86d6ce5f77a8", "status": "passed", "time": {"start": 1755545472694, "stop": 1755545495044, "duration": 22350}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a7c46ca503f2c509fdb86d6ce5f77a8"}, {"name": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "children": [{"name": "测试help me generate a picture of a bamboo forest stream", "uid": "3177d325c06c0ced", "parentUid": "59c1a46c44f4d208182b82cd7fd56466", "status": "passed", "time": {"start": 1755545509327, "stop": 1755545531209, "duration": 21882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "59c1a46c44f4d208182b82cd7fd56466"}, {"name": "test_help_me_generate_a_picture_of_a_puppy", "children": [{"name": "测试help me generate a picture of a puppy", "uid": "3ad46c4247b74393", "parentUid": "a771820a4e3abaa61c6636c3889b02b9", "status": "passed", "time": {"start": 1755545545131, "stop": 1755545567100, "duration": 21969}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a771820a4e3abaa61c6636c3889b02b9"}, {"name": "test_help_me_generate_a_picture_of_a_white_facial_cleanser", "children": [{"name": "测试help me generate a picture of a white facial cleanser product advertisement", "uid": "29e79e9a23da4107", "parentUid": "c8f1941d2f772ac665fc3e12896b1a98", "status": "passed", "time": {"start": 1755545581193, "stop": 1755545603404, "duration": 22211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8f1941d2f772ac665fc3e12896b1a98"}, {"name": "test_help_me_generate_a_picture_of_an_airplane", "children": [{"name": "测试help me generate a picture of an airplane", "uid": "c8a10778742f62b7", "parentUid": "ca1448a1bc27323ba1cb66335d0ac56a", "status": "passed", "time": {"start": 1755545617350, "stop": 1755545639385, "duration": 22035}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca1448a1bc27323ba1cb66335d0ac56a"}, {"name": "test_help_me_generate_a_picture_of_an_elegant_girl", "children": [{"name": "测试help me generate a picture of an elegant girl", "uid": "f034caa98e78f0aa", "parentUid": "69b56d7b8b05dbd51933cf6938544bdf", "status": "passed", "time": {"start": 1755545653284, "stop": 1755545675171, "duration": 21887}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69b56d7b8b05dbd51933cf6938544bdf"}, {"name": "test_help_me_generate_a_picture_of_blue_and_gold_landscape", "children": [{"name": "测试help me generate a picture of blue and gold landscape", "uid": "6fceb27a8985fb17", "parentUid": "ee4e872810d71bc5dda09924d1a91c4e", "status": "passed", "time": {"start": 1755545689268, "stop": 1755545711354, "duration": 22086}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee4e872810d71bc5dda09924d1a91c4e"}, {"name": "test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state", "children": [{"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "uid": "dfd3fac825d35b2e", "parentUid": "8c43634d02c4b93421e468daca65f6f3", "status": "passed", "time": {"start": 1755545725296, "stop": 1755545747340, "duration": 22044}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c43634d02c4b93421e468daca65f6f3"}, {"name": "test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower", "children": [{"name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "uid": "a764398584dbd337", "parentUid": "bd550d62fd6ab24a654023a07141bc5b", "status": "passed", "time": {"start": 1755545761411, "stop": 1755545783561, "duration": 22150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd550d62fd6ab24a654023a07141bc5b"}, {"name": "test_help_me_write_an_email", "children": [{"name": "测试help me write an email能正常执行", "uid": "4b6b0366475ba56c", "parentUid": "ef47f41b350d73a6140e48381c877229", "status": "passed", "time": {"start": 1755545797449, "stop": 1755545821691, "duration": 24242}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef47f41b350d73a6140e48381c877229"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "a5616c43d872369b", "parentUid": "b693f3729a9fcd922c000405462cc41b", "status": "failed", "time": {"start": 1755545835689, "stop": 1755545861121, "duration": 25432}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b693f3729a9fcd922c000405462cc41b"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "2cf73ad699817a37", "parentUid": "bc1e1c7d2a07be4f8bd829a14683b10e", "status": "passed", "time": {"start": 1755545875017, "stop": 1755545900251, "duration": 25234}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc1e1c7d2a07be4f8bd829a14683b10e"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "1e00a46747da8d89", "parentUid": "98125eb3c0eb347bf93f6ebbb14e8847", "status": "passed", "time": {"start": 1755545914505, "stop": 1755545936269, "duration": 21764}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98125eb3c0eb347bf93f6ebbb14e8847"}, {"name": "test_i_am_your_voice_assistant", "children": [{"name": "测试i am your voice assistant", "uid": "6933b6fb12ae346d", "parentUid": "9436cb3786c5c99cd02eae55e104db19", "status": "failed", "time": {"start": 1755545950033, "stop": 1755545975296, "duration": 25263}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9436cb3786c5c99cd02eae55e104db19"}, {"name": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "children": [{"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "uid": "5b7b43a0a224ef94", "parentUid": "b65ff6714d0bd5e3a66cd4a7fc84cc57", "status": "passed", "time": {"start": 1755545989614, "stop": 1755546011887, "duration": 22273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b65ff6714d0bd5e3a66cd4a7fc84cc57"}, {"name": "test_i_wanna_use_sim", "children": [{"name": "测试open settings", "uid": "10d2f5628c8cbadc", "parentUid": "7b8e3220ca81e685a8104d748e3eb4e8", "status": "passed", "time": {"start": 1755546025986, "stop": 1755546061293, "duration": 35307}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8e3220ca81e685a8104d748e3eb4e8"}, {"name": "test_i_want_make_a_video_call_to", "children": [{"name": "测试i want make a video call to能正常执行", "uid": "1a5d810bd216a785", "parentUid": "196f42775838d3c3c33e90a1d31b3ac3", "status": "failed", "time": {"start": 1755546074978, "stop": 1755546105867, "duration": 30889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "196f42775838d3c3c33e90a1d31b3ac3"}, {"name": "test_i_want_to_hear_a_joke", "children": [{"name": "测试i want to hear a joke能正常执行", "uid": "3ff29417a9ae6010", "parentUid": "c70a6aa867b7400f339364fb25312211", "status": "passed", "time": {"start": 1755546120102, "stop": 1755546145733, "duration": 25631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c70a6aa867b7400f339364fb25312211"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "ff5d7bb4f38f240e", "parentUid": "849c4d59d6ccd19947474d581af3ccb2", "status": "passed", "time": {"start": 1755546159919, "stop": 1755546182369, "duration": 22450}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "849c4d59d6ccd19947474d581af3ccb2"}, {"name": "test_install_whatsapp", "children": [{"name": "测试install whatsapp", "uid": "4c621b6aa0134d82", "parentUid": "b0eca47fb2f8f32ac7a1ae66ba395e2e", "status": "passed", "time": {"start": 1755546196098, "stop": 1755546224342, "duration": 28244}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0eca47fb2f8f32ac7a1ae66ba395e2e"}, {"name": "test_it_wears_a_red_leather_collar", "children": [{"name": "测试it wears a red leather collar", "uid": "bf798a6e3d70daa5", "parentUid": "8882bfaab2f11ddb12771524426ec7d8", "status": "passed", "time": {"start": 1755546238351, "stop": 1755546264045, "duration": 25694}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8882bfaab2f11ddb12771524426ec7d8"}, {"name": "test_it_wears_a_yellow_leather_collar", "children": [{"name": "测试it wears a yellow leather collar", "uid": "5dee92077c27d125", "parentUid": "9e3b03020c4faae9aed71f7803e4f1df", "status": "passed", "time": {"start": 1755546278113, "stop": 1755546303687, "duration": 25574}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3b03020c4faae9aed71f7803e4f1df"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "344438313a655b16", "parentUid": "0355ddc9170b8d072304068f8868f806", "status": "passed", "time": {"start": 1755546317774, "stop": 1755546339813, "duration": 22039}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0355ddc9170b8d072304068f8868f806"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "303d6a001e695137", "parentUid": "fb66326ff650d5ed24178fe23a3ec6c7", "status": "passed", "time": {"start": 1755546353628, "stop": 1755546375896, "duration": 22268}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb66326ff650d5ed24178fe23a3ec6c7"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "97dab4c826b8251a", "parentUid": "4868992c324cca3aa80f892f5012edcc", "status": "passed", "time": {"start": 1755546389967, "stop": 1755546411968, "duration": 22001}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4868992c324cca3aa80f892f5012edcc"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "11c915e41556cea3", "parentUid": "c39df595d254493d9e1cf180846ee18a", "status": "passed", "time": {"start": 1755546425673, "stop": 1755546447711, "duration": 22038}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c39df595d254493d9e1cf180846ee18a"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "f005f18c11305148", "parentUid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9", "status": "passed", "time": {"start": 1755546461408, "stop": 1755546483763, "duration": 22355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "8fbb8cd52efa5bba", "parentUid": "5a05d3bb6cee7f72819e49d4f6d5ee1f", "status": "passed", "time": {"start": 1755546497907, "stop": 1755546528865, "duration": 30958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a05d3bb6cee7f72819e49d4f6d5ee1f"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "644d8cb140f62e0a", "parentUid": "4a8b87bfde2b2e8ce7967634be5288ab", "status": "passed", "time": {"start": 1755546542657, "stop": 1755546564878, "duration": 22221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8b87bfde2b2e8ce7967634be5288ab"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "8abf1f1d22e91cdf", "parentUid": "cfbc1116cbdd7d5906a09ffc432b57db", "status": "passed", "time": {"start": 1755546578584, "stop": 1755546601052, "duration": 22468}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfbc1116cbdd7d5906a09ffc432b57db"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "测试jump to nfc settings", "uid": "715c7de84e96b864", "parentUid": "0285fb88f545482d120dd7eeddef468a", "status": "passed", "time": {"start": 1755546614903, "stop": 1755546643114, "duration": 28211}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0285fb88f545482d120dd7eeddef468a"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "8948c2eff36606c6", "parentUid": "f6098b8dc4a6271dec4f5e5f57bd4752", "status": "passed", "time": {"start": 1755546657406, "stop": 1755546679914, "duration": 22508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6098b8dc4a6271dec4f5e5f57bd4752"}, {"name": "test_kill_whatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "cf36fb78559bcbb3", "parentUid": "48a28da8e5e405bb518e2fc0c08602a0", "status": "passed", "time": {"start": 1755546694143, "stop": 1755546717481, "duration": 23338}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a28da8e5e405bb518e2fc0c08602a0"}, {"name": "test_kinkaku_ji", "children": [{"name": "测试Kinkaku-ji", "uid": "887f67141ee95c72", "parentUid": "c662375ad1f42584024973d859a56ece", "status": "passed", "time": {"start": 1755546731984, "stop": 1755546757835, "duration": 25851}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c662375ad1f42584024973d859a56ece"}, {"name": "test_make_a_call_by_whatsapp", "children": [{"name": "测试make a call by whatsapp能正常执行", "uid": "9890369f3896e4c", "parentUid": "6f8cbc6fe3edd73cea435301bd04bc62", "status": "passed", "time": {"start": 1755546772083, "stop": 1755546803639, "duration": 31556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6f8cbc6fe3edd73cea435301bd04bc62"}, {"name": "test_make_a_call_on_whatsapp_to_a", "children": [{"name": "测试make a call on whatsapp to a能正常执行", "uid": "673fedbb294a6f1c", "parentUid": "d444662576dbd8dfa4249417991e35ca", "status": "passed", "time": {"start": 1755546817824, "stop": 1755546849811, "duration": 31987}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d444662576dbd8dfa4249417991e35ca"}, {"name": "test_make_a_phone_call_to_17621905233", "children": [{"name": "测试make a phone call to 17621905233", "uid": "2665f4e98abb9b17", "parentUid": "646863d55c63f7ac85128245a6418cb8", "status": "skipped", "time": {"start": 1755546851254, "stop": 1755546851254, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化')", "smoke"]}], "uid": "646863d55c63f7ac85128245a6418cb8"}, {"name": "test_merry_christmas", "children": [{"name": "测试merry christmas", "uid": "32a4642822207cf0", "parentUid": "640796148310e1604de99261ff5802bd", "status": "failed", "time": {"start": 1755546864527, "stop": 1755546889045, "duration": 24518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "640796148310e1604de99261ff5802bd"}, {"name": "test_modify_grape_timbre", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "e10b6bdd9f53c2ea", "parentUid": "09910b981ee980bf81421c1efd746a52", "status": "passed", "time": {"start": 1755546903675, "stop": 1755546925888, "duration": 22213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09910b981ee980bf81421c1efd746a52"}, {"name": "test_more_settings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "a009973ed07360d8", "parentUid": "ca0b29ba7554057ab3ba8216b6b1f0ba", "status": "passed", "time": {"start": 1755546940225, "stop": 1755546965932, "duration": 25707}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0b29ba7554057ab3ba8216b6b1f0ba"}, {"name": "test_navigate_to_the_address_on_the_screen", "children": [{"name": "测试Navigate to the address on the screen", "uid": "bff11b2846cb0bf2", "parentUid": "758224bbc0cda79cc3eb6dfa2dbb1df4", "status": "passed", "time": {"start": 1755546980454, "stop": 1755547012622, "duration": 32168}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "758224bbc0cda79cc3eb6dfa2dbb1df4"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "c4a9a1ad677f0098", "parentUid": "6e2b1fe773a9723006e50fc18b054a32", "status": "failed", "time": {"start": 1755547027098, "stop": 1755547058930, "duration": 31832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e2b1fe773a9723006e50fc18b054a32"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "f252f9db319d4be8", "parentUid": "930b7b34cf2d90f55ec5996ae313b76b", "status": "broken", "time": {"start": 1755547073652, "stop": 1755547105068, "duration": 31416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "930b7b34cf2d90f55ec5996ae313b76b"}, {"name": "test_new_year_wishes", "children": [{"name": "测试new year wishes", "uid": "8dd63573fd58dbfa", "parentUid": "dcdb94c9beb7ae21b8fc6e0e51c601c4", "status": "passed", "time": {"start": 1755547119803, "stop": 1755547144285, "duration": 24482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcdb94c9beb7ae21b8fc6e0e51c601c4"}, {"name": "test_new_year_wishs", "children": [{"name": "测试new year wishs能正常执行", "uid": "b8b38e493df50885", "parentUid": "7e84be7c77dafee1081f62d7cfa0849c", "status": "passed", "time": {"start": 1755547158710, "stop": 1755547183523, "duration": 24813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e84be7c77dafee1081f62d7cfa0849c"}, {"name": "test_open_camera", "children": [{"name": "测试open camera", "uid": "7f62336971efe5f8", "parentUid": "d9ce24a54ba9cdc1f1589d44178a6275", "status": "passed", "time": {"start": 1755547197681, "stop": 1755547238974, "duration": 41293}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9ce24a54ba9cdc1f1589d44178a6275"}, {"name": "test_open_font_family_settings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "68aca0b52f3a2e5e", "parentUid": "5d1c4a01c8558acb00bb5c6e528035a3", "status": "passed", "time": {"start": 1755547253335, "stop": 1755547275740, "duration": 22405}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d1c4a01c8558acb00bb5c6e528035a3"}, {"name": "test_open_maps", "children": [{"name": "测试open maps", "uid": "a1c4309236a8966a", "parentUid": "48a29ef2e8e06a69e79e3ce012a1ca44", "status": "passed", "time": {"start": 1755547289709, "stop": 1755547320995, "duration": 31286}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a29ef2e8e06a69e79e3ce012a1ca44"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "a6508523a9440c5d", "parentUid": "309e531c38122a270a22145c2b2f4a8a", "status": "passed", "time": {"start": 1755547335049, "stop": 1755547357658, "duration": 22609}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "309e531c38122a270a22145c2b2f4a8a"}, {"name": "test_open_settings", "children": [{"name": "测试open settings", "uid": "c76bb8231c514058", "parentUid": "5366ad01888b7b931dc846b46730ef41", "status": "passed", "time": {"start": 1755547371995, "stop": 1755547403917, "duration": 31922}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5366ad01888b7b931dc846b46730ef41"}, {"name": "test_open_the_settings", "children": [{"name": "测试open the settings", "uid": "87e4c8157a7c0b0a", "parentUid": "adb6b73b5c7aa6bc82b47a2854ec6049", "status": "passed", "time": {"start": 1755547417706, "stop": 1755547450006, "duration": 32300}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "adb6b73b5c7aa6bc82b47a2854ec6049"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "d907a6931b9bab45", "parentUid": "fae42569d3565f4a0509b1abe481814e", "status": "failed", "time": {"start": 1755547463774, "stop": 1755547487298, "duration": 23524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae42569d3565f4a0509b1abe481814e"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "a12388ce0112ec8b", "parentUid": "12a853573a3ed32b45369299151ae46b", "status": "failed", "time": {"start": 1755547501236, "stop": 1755547523724, "duration": 22488}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12a853573a3ed32b45369299151ae46b"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "15dbd978c9e9ff3c", "parentUid": "6116229fe40039026544aa05527a4376", "status": "passed", "time": {"start": 1755547537971, "stop": 1755547559923, "duration": 21952}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6116229fe40039026544aa05527a4376"}, {"name": "test_parking_space", "children": [{"name": "测试parking space能正常执行", "uid": "c862ac6b8bc8ba94", "parentUid": "d235978673eeb20a0aa5a17d0b9af2fb", "status": "passed", "time": {"start": 1755547574100, "stop": 1755547596332, "duration": 22232}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d235978673eeb20a0aa5a17d0b9af2fb"}, {"name": "test_play_carpenters_video", "children": [{"name": "测试play carpenters'video", "uid": "91c1ec3779acbc46", "parentUid": "4d419e427b323d88eac6c065a5f63bf0", "status": "passed", "time": {"start": 1755547610797, "stop": 1755547637613, "duration": 26816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d419e427b323d88eac6c065a5f63bf0"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "测试play football video by youtube", "uid": "8daac15c9f9ea1a8", "parentUid": "1b38b53340d3cf404c7d3184db4b3767", "status": "passed", "time": {"start": 1755547651841, "stop": 1755547680054, "duration": 28213}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b38b53340d3cf404c7d3184db4b3767"}, {"name": "test_play_love_sotry", "children": [{"name": "测试play love sotry", "uid": "78003ba8ba5d066c", "parentUid": "3279616bb2594ec7b639e82e288080c3", "status": "passed", "time": {"start": 1755547694222, "stop": 1755547737272, "duration": 43050}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3279616bb2594ec7b639e82e288080c3"}, {"name": "test_play_music_by_Audiomack", "children": [{"name": "测试play music by Audiomack", "uid": "afa51a402f294990", "parentUid": "f8faca32e9c6c24264e9529853e54177", "status": "passed", "time": {"start": 1755547751251, "stop": 1755547776030, "duration": 24779}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8faca32e9c6c24264e9529853e54177"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "e61d8722a56d0e37", "parentUid": "7c3b9cf1e80a1543e429a21b6580f11f", "status": "passed", "time": {"start": 1755547790139, "stop": 1755547831409, "duration": 41270}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c3b9cf1e80a1543e429a21b6580f11f"}, {"name": "test_play_the_album", "children": [{"name": "测试play the album", "uid": "b0188f89da3d109f", "parentUid": "96bab08553e379e0c0029cca77667f8f", "status": "passed", "time": {"start": 1755547845246, "stop": 1755547885666, "duration": 40420}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96bab08553e379e0c0029cca77667f8f"}, {"name": "test_play_video", "children": [{"name": "测试play video", "uid": "23aff1aca63900f9", "parentUid": "a38748b3b1f409e94ff601029a63f98d", "status": "passed", "time": {"start": 1755547899549, "stop": 1755547927800, "duration": 28251}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a38748b3b1f409e94ff601029a63f98d"}, {"name": "test_play_video_by_youtube", "children": [{"name": "测试play video by youtube", "uid": "cf9f2c5a2ad8b9eb", "parentUid": "c7f371ce0f7eeb044f6c854937a7502b", "status": "passed", "time": {"start": 1755547941987, "stop": 1755547970029, "duration": 28042}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7f371ce0f7eeb044f6c854937a7502b"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "测试please show me where i am能正常执行", "uid": "79010a10fe7f461d", "parentUid": "a856f6c3dff3e6d986ecbf33df7fabe2", "status": "failed", "time": {"start": 1755547984334, "stop": 1755548008569, "duration": 24235}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a856f6c3dff3e6d986ecbf33df7fabe2"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "1d166cd5bb89f599", "parentUid": "3dc012d4a4449a7636b0642c22bceb9e", "status": "failed", "time": {"start": 1755548023055, "stop": 1755548046986, "duration": 23931}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dc012d4a4449a7636b0642c22bceb9e"}, {"name": "test_power_off_my_phone", "children": [{"name": "测试power off my phone能正常执行", "uid": "ecbca05a49966cd4", "parentUid": "efc94c71c211627f11802239a7139801", "status": "skipped", "time": {"start": 1755548048698, "stop": 1755548048698, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "efc94c71c211627f11802239a7139801"}, {"name": "test_privacy_policy", "children": [{"name": "测试privacy policy", "uid": "304d1991e7b7338d", "parentUid": "fb018ed86c7e834a548eec9714f5e6a3", "status": "passed", "time": {"start": 1755548061813, "stop": 1755548084125, "duration": 22312}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb018ed86c7e834a548eec9714f5e6a3"}, {"name": "test_puppy", "children": [{"name": "测试puppy", "uid": "e1f427e407268742", "parentUid": "44e1d788e6211ef1bf44e5b853acf76e", "status": "passed", "time": {"start": 1755548098626, "stop": 1755548124639, "duration": 26013}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44e1d788e6211ef1bf44e5b853acf76e"}, {"name": "test_reboot_my_phone", "children": [{"name": "测试reboot my phone能正常执行", "uid": "474f128d73576157", "parentUid": "41272830cf23beb3b9d330466e63c528", "status": "skipped", "time": {"start": 1755548126145, "stop": 1755548126145, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='reboot 会导致设备断开，先跳过')", "smoke"]}], "uid": "41272830cf23beb3b9d330466e63c528"}, {"name": "test_redial", "children": [{"name": "测试redial", "uid": "110bca580595cc26", "parentUid": "494d3e1b731af3bce21d72c38e17e357", "status": "skipped", "time": {"start": 1755548126150, "stop": 1755548126150, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='redial命令需要先拨打电话，才能重拨，无法通用化')", "smoke"]}], "uid": "494d3e1b731af3bce21d72c38e17e357"}, {"name": "test_remember_the_parking_lot", "children": [{"name": "测试remember the parking lot能正常执行", "uid": "f0e19a1d22249e58", "parentUid": "57a5364756ca608e39ef1782f15a0eb6", "status": "passed", "time": {"start": 1755548139197, "stop": 1755548161261, "duration": 22064}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "57a5364756ca608e39ef1782f15a0eb6"}, {"name": "test_remember_the_parking_space", "children": [{"name": "测试remember the parking space", "uid": "bc289b8805ed6f47", "parentUid": "853e3f6d6eaa9b4bad958b3da289dde7", "status": "failed", "time": {"start": 1755548175424, "stop": 1755548197727, "duration": 22303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "853e3f6d6eaa9b4bad958b3da289dde7"}, {"name": "test_remove_the_people_from_the_image", "children": [{"name": "测试remove the people from the image", "uid": "617d260b52b35e2d", "parentUid": "d19db6058e5f4c4097a27510ecaa9d2b", "status": "passed", "time": {"start": 1755548212267, "stop": 1755548239506, "duration": 27239}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d19db6058e5f4c4097a27510ecaa9d2b"}, {"name": "test_reset_phone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "c0bcef3f87616eb5", "parentUid": "97792796a4c38ae78b9db124406767b2", "status": "skipped", "time": {"start": 1755548240955, "stop": 1755548240955, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='reset phone 会导致设备断开，先跳过')"]}], "uid": "97792796a4c38ae78b9db124406767b2"}, {"name": "test_restart_my_phone", "children": [{"name": "测试restart my phone能正常执行", "uid": "ec3f00031eaf4371", "parentUid": "c1ea683fb9d174ed7b5f8880d961bd37", "status": "skipped", "time": {"start": 1755548240961, "stop": 1755548240961, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "c1ea683fb9d174ed7b5f8880d961bd37"}, {"name": "test_restart_the_phone", "children": [{"name": "测试restart the phone能正常执行", "uid": "9bc8f2567daac5d0", "parentUid": "53adce70cf390992f02ff700aaada272", "status": "skipped", "time": {"start": 1755548240965, "stop": 1755548240965, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "53adce70cf390992f02ff700aaada272"}, {"name": "test_running_on_the_grass", "children": [{"name": "测试running on the grass", "uid": "15b8865ebef84c4", "parentUid": "1bc086e4c470f4bd3514200946305a81", "status": "failed", "time": {"start": 1755548253869, "stop": 1755548281183, "duration": 27314}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1bc086e4c470f4bd3514200946305a81"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "c7c9715779136ab0", "parentUid": "735baa4624c52854b8a2a73abb447f23", "status": "passed", "time": {"start": 1755548295766, "stop": 1755548321953, "duration": 26187}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "735baa4624c52854b8a2a73abb447f23"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "测试search the address in the image能正常执行", "uid": "7d126021ffb45811", "parentUid": "cba35d4f03acbacfc1c9c3b0792e089b", "status": "passed", "time": {"start": 1755548336283, "stop": 1755548362375, "duration": 26092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cba35d4f03acbacfc1c9c3b0792e089b"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "aafe14e472f370a3", "parentUid": "7c7aef6b5854cef222cfb44ffc5056fe", "status": "passed", "time": {"start": 1755548376434, "stop": 1755548404737, "duration": 28303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c7aef6b5854cef222cfb44ffc5056fe"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "a978d43a69fdb4a4", "parentUid": "1445db3159fb5e02b25911ddf53e9078", "status": "passed", "time": {"start": 1755548418679, "stop": 1755548444543, "duration": 25864}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1445db3159fb5e02b25911ddf53e9078"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "bc58b8085ad08e08", "parentUid": "7904446ab7fbbc082d949ad5dd1566fc", "status": "passed", "time": {"start": 1755548458658, "stop": 1755548481293, "duration": 22635}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7904446ab7fbbc082d949ad5dd1566fc"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "2aaf5347dbc01d8b", "parentUid": "42a0d4edd729031d01ed4ce54f32a86f", "status": "passed", "time": {"start": 1755548495332, "stop": 1755548517734, "duration": 22402}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42a0d4edd729031d01ed4ce54f32a86f"}, {"name": "test_set_app_notifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "454a02f184d6e886", "parentUid": "f17166693c717b681b5c4ace219a5254", "status": "passed", "time": {"start": 1755548531993, "stop": 1755548554414, "duration": 22421}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f17166693c717b681b5c4ace219a5254"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "7fb3652513992510", "parentUid": "90688cbd93c016ee7d407e660ac88b04", "status": "passed", "time": {"start": 1755548568717, "stop": 1755548591450, "duration": 22733}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90688cbd93c016ee7d407e660ac88b04"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "fe76b8e3bc1750b5", "parentUid": "27c7eefc61a4f26939cc8e62add52a75", "status": "passed", "time": {"start": 1755548605576, "stop": 1755548637601, "duration": 32025}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27c7eefc61a4f26939cc8e62add52a75"}, {"name": "test_set_color_style", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "8e7863fa9e0516bc", "parentUid": "4a9eb6eed41e8d978e3f2271d25aeeb9", "status": "passed", "time": {"start": 1755548651804, "stop": 1755548674233, "duration": 22429}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a9eb6eed41e8d978e3f2271d25aeeb9"}, {"name": "test_set_compatibility_mode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "9f87c3103add02e5", "parentUid": "71f9d2696419dde9e2fbc887cd35352f", "status": "passed", "time": {"start": 1755548688485, "stop": 1755548710878, "duration": 22393}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71f9d2696419dde9e2fbc887cd35352f"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "f0bc2546728b57be", "parentUid": "3e469638b07e0f748f137f566308ee39", "status": "passed", "time": {"start": 1755548725100, "stop": 1755548747846, "duration": 22746}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e469638b07e0f748f137f566308ee39"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "7d88c44af202bec", "parentUid": "681063f39b4b3d4670516dc945bc7e8f", "status": "passed", "time": {"start": 1755548761809, "stop": 1755548784471, "duration": 22662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "681063f39b4b3d4670516dc945bc7e8f"}, {"name": "test_set_date_time", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "70ef09b5d0578ff", "parentUid": "55afb5474f6367c035d3c3b4c5aa4a0e", "status": "passed", "time": {"start": 1755548798623, "stop": 1755548820833, "duration": 22210}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55afb5474f6367c035d3c3b4c5aa4a0e"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "9671c04e652145d5", "parentUid": "d063298b5e7b7ba1dd78f28933196633", "status": "passed", "time": {"start": 1755548835228, "stop": 1755548857548, "duration": 22320}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d063298b5e7b7ba1dd78f28933196633"}, {"name": "test_set_flex_still_mode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "6463f34280457263", "parentUid": "fb37e27acd491ebde3cb6675b3ff4b33", "status": "passed", "time": {"start": 1755548872001, "stop": 1755548894307, "duration": 22306}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb37e27acd491ebde3cb6675b3ff4b33"}, {"name": "test_set_flip_case_feature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "9d79c15c7aa3b791", "parentUid": "d093123b5193b9aead35dd1996c7cd1a", "status": "passed", "time": {"start": 1755548908478, "stop": 1755548930430, "duration": 21952}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d093123b5193b9aead35dd1996c7cd1a"}, {"name": "test_set_floating_windows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "83c1baf55df42d77", "parentUid": "260060301e90f5edbed593882a5fec0d", "status": "passed", "time": {"start": 1755548944420, "stop": 1755548966779, "duration": 22359}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "260060301e90f5edbed593882a5fec0d"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "86b9e6f5cf0f3680", "parentUid": "d7d6eb8f64580ac8b8fb024a954198fa", "status": "passed", "time": {"start": 1755548980760, "stop": 1755549003220, "duration": 22460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7d6eb8f64580ac8b8fb024a954198fa"}, {"name": "test_set_font_size", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "cd2d1addc7608d8a", "parentUid": "740d4f9856207354991fc052a0633f21", "status": "passed", "time": {"start": 1755549017205, "stop": 1755549039403, "duration": 22198}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "740d4f9856207354991fc052a0633f21"}, {"name": "test_set_gesture_navigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "4873813c4a892c87", "parentUid": "47320f9ee2ac872ac9be9e52d1af28d8", "status": "passed", "time": {"start": 1755549053787, "stop": 1755549081626, "duration": 27839}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47320f9ee2ac872ac9be9e52d1af28d8"}, {"name": "test_set_languages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "ea971f8afc8ac3d8", "parentUid": "46ab090271dad35a2f22e1aa891d167d", "status": "passed", "time": {"start": 1755549095869, "stop": 1755549118203, "duration": 22334}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46ab090271dad35a2f22e1aa891d167d"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "7bcfa670f3717f3c", "parentUid": "1ebb1fb5844374f960b3ecd76cddec92", "status": "passed", "time": {"start": 1755549132314, "stop": 1755549154625, "duration": 22311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ebb1fb5844374f960b3ecd76cddec92"}, {"name": "test_set_my_fonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "f8218991466f15e9", "parentUid": "c63b678cbbe7de452848a070298e3fab", "status": "passed", "time": {"start": 1755549168756, "stop": 1755549191000, "duration": 22244}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c63b678cbbe7de452848a070298e3fab"}, {"name": "test_set_my_themes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "c98d6e623d24e658", "parentUid": "452ed970a52e7ee9049bbdb424f9b57d", "status": "passed", "time": {"start": 1755549205169, "stop": 1755549227729, "duration": 22560}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "452ed970a52e7ee9049bbdb424f9b57d"}, {"name": "test_set_nfc_tag", "children": [{"name": "测试set nfc tag", "uid": "df6140718ba46854", "parentUid": "85fdaa0cd8fa013cf340b4e7ee2dfa5c", "status": "passed", "time": {"start": 1755549241914, "stop": 1755549271257, "duration": 29343}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85fdaa0cd8fa013cf340b4e7ee2dfa5c"}, {"name": "test_set_off_a_firework", "children": [{"name": "测试set off a firework能正常执行", "uid": "967ae3d31576809e", "parentUid": "bd299436c9a75170d414625cc2cf2562", "status": "passed", "time": {"start": 1755549285581, "stop": 1755549310614, "duration": 25033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd299436c9a75170d414625cc2cf2562"}, {"name": "test_set_parallel_windows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "41af1b410102153b", "parentUid": "b7b2179becb10c93603c2fcce15e0006", "status": "passed", "time": {"start": 1755549324908, "stop": 1755549347327, "duration": 22419}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7b2179becb10c93603c2fcce15e0006"}, {"name": "test_set_personal_hotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "b3fde257da3ffa32", "parentUid": "4fed48034322a5d5119c937240c4aa77", "status": "passed", "time": {"start": 1755549361790, "stop": 1755549384169, "duration": 22379}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4fed48034322a5d5119c937240c4aa77"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "3c1d99d953a261c6", "parentUid": "7b8efe12e2cc4a1e4fec01a67e1ead0d", "status": "passed", "time": {"start": 1755549398624, "stop": 1755549421021, "duration": 22397}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8efe12e2cc4a1e4fec01a67e1ead0d"}, {"name": "test_set_phone_number", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "c76ab9470c51fe56", "parentUid": "f0d8d3a4b043370c607b0bb64b8df1bd", "status": "passed", "time": {"start": 1755549435535, "stop": 1755549457878, "duration": 22343}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0d8d3a4b043370c607b0bb64b8df1bd"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "dea6a036feb878e0", "parentUid": "2095cb76657298bca8aa08a5ddc4452c", "status": "passed", "time": {"start": 1755549472460, "stop": 1755549495150, "duration": 22690}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2095cb76657298bca8aa08a5ddc4452c"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "d7139503c14d5b2d", "parentUid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b", "status": "passed", "time": {"start": 1755549509614, "stop": 1755549531578, "duration": 21964}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b"}, {"name": "test_set_screen_relay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "2b5c5da56c75ff4", "parentUid": "66dc5bdcc3e27c2ae07a1536c41488cd", "status": "passed", "time": {"start": 1755549546096, "stop": 1755549568423, "duration": 22327}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66dc5bdcc3e27c2ae07a1536c41488cd"}, {"name": "test_set_screen_timeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "e1839257fa3f6667", "parentUid": "efb5e97f214d22c0692101b1024ecde2", "status": "passed", "time": {"start": 1755549582878, "stop": 1755549605151, "duration": 22273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "efb5e97f214d22c0692101b1024ecde2"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "6eb43be3ca854b83", "parentUid": "e1c10086104522b2fbc3cd552709a125", "status": "passed", "time": {"start": 1755549619480, "stop": 1755549641974, "duration": 22494}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c10086104522b2fbc3cd552709a125"}, {"name": "test_set_sim_ringtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "29f8078765071560", "parentUid": "02b906bfcb368697dfb809905922e9cb", "status": "passed", "time": {"start": 1755549656313, "stop": 1755549678793, "duration": 22480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02b906bfcb368697dfb809905922e9cb"}, {"name": "test_set_smart_hub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "16fa33ac9f4fc28a", "parentUid": "1b885e8f75c6cb8827815cfc72755c55", "status": "passed", "time": {"start": 1755549693192, "stop": 1755549715674, "duration": 22482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b885e8f75c6cb8827815cfc72755c55"}, {"name": "test_set_smart_panel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "9401f2aafc72cbb6", "parentUid": "116ddf2803a2d3fa8977f8f2b43e59a3", "status": "passed", "time": {"start": 1755549729979, "stop": 1755549752409, "duration": 22430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "116ddf2803a2d3fa8977f8f2b43e59a3"}, {"name": "test_set_special_function", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "d4033ac84f856cd7", "parentUid": "1cdef3d89962b3a41ead4e6a44623984", "status": "passed", "time": {"start": 1755549766920, "stop": 1755549789346, "duration": 22426}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cdef3d89962b3a41ead4e6a44623984"}, {"name": "test_set_split_screen_apps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "b0523e40835680c9", "parentUid": "df5405aa0bd507fb6dc22f263be7dfef", "status": "passed", "time": {"start": 1755549803696, "stop": 1755549826120, "duration": 22424}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df5405aa0bd507fb6dc22f263be7dfef"}, {"name": "test_set_timer", "children": [{"name": "测试set timer", "uid": "1ee3a2d6d4b65ad", "parentUid": "8fcfb88078ce21426d07ad83897d1dba", "status": "passed", "time": {"start": 1755549840641, "stop": 1755549867778, "duration": 27137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fcfb88078ce21426d07ad83897d1dba"}, {"name": "test_set_timezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "19693adbe0f7f612", "parentUid": "9e7c120412bd7e0f730e61377785fa8a", "status": "passed", "time": {"start": 1755549882185, "stop": 1755549904488, "duration": 22303}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e7c120412bd7e0f730e61377785fa8a"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "dde17385f415640f", "parentUid": "5cae1c2c93d377af57591ba886a396b7", "status": "passed", "time": {"start": 1755549918832, "stop": 1755549941568, "duration": 22736}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cae1c2c93d377af57591ba886a396b7"}, {"name": "test_start_boosting_phone", "children": [{"name": "测试start boosting phone能正常执行", "uid": "685452b2ba4f9a89", "parentUid": "c0500ddbbfde32ada0f1ae6d634b7947", "status": "passed", "time": {"start": 1755549955814, "stop": 1755549977337, "duration": 21523}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c0500ddbbfde32ada0f1ae6d634b7947"}, {"name": "test_start_running", "children": [{"name": "测试start running能正常执行", "uid": "711abadef0237b83", "parentUid": "8fdaf0f924e8731506064256cfd9d49e", "status": "passed", "time": {"start": 1755549991489, "stop": 1755550021349, "duration": 29860}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fdaf0f924e8731506064256cfd9d49e"}, {"name": "test_start_walking", "children": [{"name": "测试start walking能正常执行", "uid": "8b77159b32025374", "parentUid": "4a8c9e4aef8cc53d5214b16790f29ecf", "status": "skipped", "time": {"start": 1755550022696, "stop": 1755550022696, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='start walking命令都会开Health应用，才能执行，无法通用化')", "smoke"]}], "uid": "4a8c9e4aef8cc53d5214b16790f29ecf"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page", "uid": "a8d288b13eea8d3e", "parentUid": "55ce916af0728638845a795948e38c67", "status": "passed", "time": {"start": 1755550035242, "stop": 1755550057418, "duration": 22176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55ce916af0728638845a795948e38c67"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试Summarize what I'm reading", "uid": "b1915364bac38f76", "parentUid": "c866cefd3cd73292a41110064d69746d", "status": "passed", "time": {"start": 1755550071657, "stop": 1755550093619, "duration": 21962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c866cefd3cd73292a41110064d69746d"}, {"name": "test_switch_to_davido_voice", "children": [{"name": "测试Switch to davido voice能正常执行", "uid": "565d3a68eadd14f6", "parentUid": "345255e1a4eaa1b4c4a073d2355fd91c", "status": "passed", "time": {"start": 1755550107536, "stop": 1755550129785, "duration": 22249}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "345255e1a4eaa1b4c4a073d2355fd91c"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "8bb36711edb0df7f", "parentUid": "eaf66c431b1bc4cdb440f63e804d5b7f", "status": "passed", "time": {"start": 1755550143772, "stop": 1755550165334, "duration": 21562}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eaf66c431b1bc4cdb440f63e804d5b7f"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "ff50a601a62b5563", "parentUid": "9c64338b2e6a23f5945b6c45a6e8988f", "status": "passed", "time": {"start": 1755550179343, "stop": 1755550201447, "duration": 22104}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9c64338b2e6a23f5945b6c45a6e8988f"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "d356792bb10c9dcb", "parentUid": "0a89b8197df33175fe755b5815c5229b", "status": "passed", "time": {"start": 1755550215198, "stop": 1755550237680, "duration": 22482}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a89b8197df33175fe755b5815c5229b"}, {"name": "test_switching_charging_speed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "9a3290f96fe7666b", "parentUid": "556ad59feb219cf0e64a09e0c312ef81", "status": "passed", "time": {"start": 1755550251513, "stop": 1755550273578, "duration": 22065}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "556ad59feb219cf0e64a09e0c312ef81"}, {"name": "test_take_notes", "children": [{"name": "测试take notes能正常执行", "uid": "2fb5de1bf6466d20", "parentUid": "7509a983d8243a8effb8a61f465b5c1a", "status": "failed", "time": {"start": 1755550287593, "stop": 1755550313863, "duration": 26270}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7509a983d8243a8effb8a61f465b5c1a"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "1701645e26679093", "parentUid": "8b698828f35d1b5e0ecb9b450dc40242", "status": "passed", "time": {"start": 1755550328224, "stop": 1755550350425, "duration": 22201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8b698828f35d1b5e0ecb9b450dc40242"}, {"name": "test_tell_me_joke", "children": [{"name": "测试tell me joke能正常执行", "uid": "c554cc0509ed2c17", "parentUid": "cb157e6b5cd8cba7c4577e10e0dd7c22", "status": "failed", "time": {"start": 1755550364424, "stop": 1755550389686, "duration": 25262}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb157e6b5cd8cba7c4577e10e0dd7c22"}, {"name": "test_the_mobile_phone_is_very_hot", "children": [{"name": "测试the mobile phone is very hot", "uid": "3be2292986414830", "parentUid": "a4c6d4cdd9b5c797269ad542918d8253", "status": "passed", "time": {"start": 1755550404270, "stop": 1755550427074, "duration": 22804}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a4c6d4cdd9b5c797269ad542918d8253"}, {"name": "test_there_are_many_yellow_sunflowers_on_the_ground", "children": [{"name": "测试there are many yellow sunflowers on the ground", "uid": "70ea41509f71ae68", "parentUid": "c87417891e4cf964c74e3c52135f1012", "status": "passed", "time": {"start": 1755550440995, "stop": 1755550466694, "duration": 25699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c87417891e4cf964c74e3c52135f1012"}, {"name": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "children": [{"name": "测试There are transparent, glowing multicolored soap bubbles around it", "uid": "789a9ec2b9f26c16", "parentUid": "a977b51920eca060a5b122c5aff6cc53", "status": "passed", "time": {"start": 1755550480853, "stop": 1755550507042, "duration": 26189}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a977b51920eca060a5b122c5aff6cc53"}, {"name": "test_there_is_a_colorful_butterfly_beside_it", "children": [{"name": "测试there is a colorful butterfly beside it", "uid": "10ed7fd6efa81c6a", "parentUid": "8c6aaf4045a81b3320a15d452b5d7e2f", "status": "passed", "time": {"start": 1755550520777, "stop": 1755550546193, "duration": 25416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6aaf4045a81b3320a15d452b5d7e2f"}, {"name": "test_three_little_pigs", "children": [{"name": "测试Three Little Pigs", "uid": "f35ab6662635c7df", "parentUid": "e448ef7c7bd02012da4b9d380ad338b5", "status": "passed", "time": {"start": 1755550560264, "stop": 1755550585695, "duration": 25431}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e448ef7c7bd02012da4b9d380ad338b5"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "915a979c1ab3f826", "parentUid": "07d91c6eb72b9bf62af23569018b6ed9", "status": "passed", "time": {"start": 1755550599676, "stop": 1755550621782, "duration": 22106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07d91c6eb72b9bf62af23569018b6ed9"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "94a260d39cc61397", "parentUid": "1da9f8360bfb0009b7bbba3b558b5ad6", "status": "passed", "time": {"start": 1755550635665, "stop": 1755550658012, "duration": 22347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1da9f8360bfb0009b7bbba3b558b5ad6"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "7cc6c01c614625cd", "parentUid": "fd2d2a21ef2aa878399c88b3d0e934ba", "status": "passed", "time": {"start": 1755550672216, "stop": 1755550698097, "duration": 25881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd2d2a21ef2aa878399c88b3d0e934ba"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "c0a498b834afb8f6", "parentUid": "49b44a51671f9fc053854051c80f7c70", "status": "passed", "time": {"start": 1755550712201, "stop": 1755550734473, "duration": 22272}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49b44a51671f9fc053854051c80f7c70"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "1209d5cb1fb232f6", "parentUid": "838b34c8fa65f970357a94fb9eaf0dcc", "status": "passed", "time": {"start": 1755550748640, "stop": 1755550771005, "duration": 22365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "838b34c8fa65f970357a94fb9eaf0dcc"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "674938ed9b42673f", "parentUid": "9d137f0f051ce9331aca868c940ab405", "status": "passed", "time": {"start": 1755550785029, "stop": 1755550815738, "duration": 30709}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d137f0f051ce9331aca868c940ab405"}, {"name": "test_view_in_notebook", "children": [{"name": "测试view in notebook", "uid": "486198e7ca2f5671", "parentUid": "4a568b6f4b1988b1b280aac795977cf4", "status": "passed", "time": {"start": 1755550829705, "stop": 1755550864436, "duration": 34731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a568b6f4b1988b1b280aac795977cf4"}, {"name": "test_voice_setting_page", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "1aefe55150cc415", "parentUid": "c2e243e17e6068d0d67070f2c20e8486", "status": "passed", "time": {"start": 1755550878115, "stop": 1755550909670, "duration": 31555}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c2e243e17e6068d0d67070f2c20e8486"}, {"name": "test_what_date_is_it", "children": [{"name": "测试what date is it能正常执行", "uid": "3c858841add323a3", "parentUid": "91a4dbcca261fa96c4858bdb888ab618", "status": "passed", "time": {"start": 1755550923588, "stop": 1755550945795, "duration": 22207}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "91a4dbcca261fa96c4858bdb888ab618"}, {"name": "test_what_is_the_weather_today", "children": [{"name": "测试what is the weather today能正常执行", "uid": "480a0fe4bb19fcbc", "parentUid": "f9448cabe248d24cefaab6116ebbe403", "status": "passed", "time": {"start": 1755550960004, "stop": 1755550990198, "duration": 30194}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9448cabe248d24cefaab6116ebbe403"}, {"name": "test_what_s_the_date_today", "children": [{"name": "测试what's the date today", "uid": "8b5755924feaa165", "parentUid": "1e7565034ebb8f9de8885edeada8be54", "status": "passed", "time": {"start": 1755551004319, "stop": 1755551026729, "duration": 22410}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e7565034ebb8f9de8885edeada8be54"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "d6444fbbd9fa86d2", "parentUid": "de0c4647cd25ea2211f7b7d6173d4170", "status": "passed", "time": {"start": 1755551040688, "stop": 1755551062769, "duration": 22081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "de0c4647cd25ea2211f7b7d6173d4170"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name", "uid": "b3d6c60bb022e56e", "parentUid": "a1b78446c4fd3cc42f6e7a24189e01e4", "status": "passed", "time": {"start": 1755551076900, "stop": 1755551098907, "duration": 22007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1b78446c4fd3cc42f6e7a24189e01e4"}, {"name": "test_what_time_is_it", "children": [{"name": "测试what time is it能正常执行", "uid": "f88fb1e507dee323", "parentUid": "9b2aff3b0a6e2514bd870f8249da94f2", "status": "passed", "time": {"start": 1755551113074, "stop": 1755551135081, "duration": 22007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b2aff3b0a6e2514bd870f8249da94f2"}, {"name": "test_what_time_is_it_in_china", "children": [{"name": "测试what time is it in china能正常执行", "uid": "12ed095629d33d34", "parentUid": "4708be60f0bd03b2f9d73a1d9a3dbd08", "status": "passed", "time": {"start": 1755551148997, "stop": 1755551171662, "duration": 22665}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4708be60f0bd03b2f9d73a1d9a3dbd08"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "测试what time is it in London能正常执行", "uid": "dd5f2e8b9cbf2bfa", "parentUid": "73e8c462f6a0b1457c865212c98a100e", "status": "passed", "time": {"start": 1755551185946, "stop": 1755551208146, "duration": 22200}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73e8c462f6a0b1457c865212c98a100e"}, {"name": "test_where_is_my_car", "children": [{"name": "测试where is my car能正常执行", "uid": "d69e193e35f210e0", "parentUid": "d24794db3e8c3ed208a348e4c4a1e4a0", "status": "passed", "time": {"start": 1755551222229, "stop": 1755551244233, "duration": 22004}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d24794db3e8c3ed208a348e4c4a1e4a0"}, {"name": "test_where_s_my_car", "children": [{"name": "测试where`s my car能正常执行", "uid": "e34af531cc9853a3", "parentUid": "5cddcd62a6a24cfd23af0bb73d272e2d", "status": "passed", "time": {"start": 1755551258713, "stop": 1755551281024, "duration": 22311}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cddcd62a6a24cfd23af0bb73d272e2d"}, {"name": "test_yandex_eats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "979aaae63733c3e6", "parentUid": "ffa74429a7c2fa0151c7e7ef27b25ecb", "status": "failed", "time": {"start": 1755551295036, "stop": 1755551317273, "duration": 22237}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffa74429a7c2fa0151c7e7ef27b25ecb"}], "uid": "e416b8e9994852e8ed797dec283160f6"}], "uid": "testcases.test_ella"}]}