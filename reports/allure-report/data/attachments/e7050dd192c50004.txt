2025-08-19 04:12:51 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-08-19 04:12:51 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:299 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-19 04:12:51 | INFO | tools.adb_process_monitor:clear_all_running_processes:1825 | 🧹 开始清除手机上所有运行中的应用进程...
2025-08-19 04:12:51 | INFO | tools.adb_process_monitor:clear_all_running_processes:1841 | ⚡ 优先使用命令直接清理...
2025-08-19 04:12:54 | INFO | tools.adb_process_monitor:clear_all_running_processes:1847 | 💪 强制停止顽固应用...
2025-08-19 04:12:58 | INFO | tools.adb_process_monitor:clear_all_running_processes:1857 | 🎉 应用进程清理完成，共清理 30 个应用
2025-08-19 04:13:00 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:301 | ✅ 应用进程清理完成
2025-08-19 04:13:00 | INFO | pages.apps.ella.dialogue_page:start_app:213 | 启动Ella应用
2025-08-19 04:13:00 | INFO | pages.apps.ella.dialogue_page:start_app:221 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-19 04:13:04 | INFO | pages.apps.ella.dialogue_page:_check_app_started:279 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-08-19 04:13:04 | INFO | pages.apps.ella.dialogue_page:start_app:226 | ✅ Ella应用启动成功（指定Activity）
2025-08-19 04:13:04 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:300 | 等待Ella页面加载完成 (超时: 15秒)
2025-08-19 04:13:04 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-08-19 04:13:04 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-19 04:13:04 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-19 04:13:04 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:304 | ✅ 输入框已出现，页面加载完成
2025-08-19 04:13:04 | INFO | testcases.test_ella.base_ella_test:ella_app:333 | ✅ Ella应用启动成功
2025-08-19 04:13:04 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:366 | 初始状态None- 使用命令please show me where i am，状态: 
2025-08-19 04:13:04 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-08-19 04:13:04 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-19 04:13:04 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-19 04:13:04 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-19 04:13:04 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-19 04:13:04 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-08-19 04:13:04 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-19 04:13:05 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-19 04:13:05 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: please show me where i am
2025-08-19 04:13:05 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-08-19 04:13:05 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-08-19 04:13:05 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: please show me where i am
2025-08-19 04:13:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-19 04:13:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-19 04:13:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-19 04:13:05 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-08-19 04:13:05 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-08-19 04:13:05 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-19 04:13:05 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-08-19 04:13:06 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: please show me where i am
2025-08-19 04:13:06 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-08-19 04:13:06 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-08-19 04:13:06 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-08-19 04:13:06 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-08-19 04:13:06 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-08-19 04:13:07 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-08-19 04:13:07 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-08-19 04:13:07 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-08-19 04:13:07 | INFO | testcases.test_ella.base_ella_test:_execute_command:870 | ✅ 成功执行命令: please show me where i am
2025-08-19 04:13:07 | INFO | testcases.test_ella.base_ella_test:_handle_popup_after_command:178 | handle_popup_after_command:处理弹窗
2025-08-19 04:13:07 | INFO | core.popup_tool:detect_and_close_popup_once:735 | 执行单次弹窗检测和关闭
2025-08-19 04:13:12 | INFO | core.popup_tool:detect_and_close_popup_once:739 | 未检测到弹窗，无需处理
2025-08-19 04:13:12 | INFO | testcases.test_ella.base_ella_test:_get_response_timeout:1294 | 💬 检测到简单对话命令，使用短等待时间: 6秒
2025-08-19 04:13:12 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 6秒
2025-08-19 04:13:13 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:60 | ✅ 通过TTS按钮检测到响应
2025-08-19 04:13:17 | INFO | testcases.test_ella.base_ella_test:_get_final_status_with_page_info:440 | 状态检查时当前应用包名: com.transsion.aivoiceassistant
2025-08-19 04:13:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:558 | 状态检查完成，现在获取响应文本
2025-08-19 04:13:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:562 | 第1次尝试确保在Ella页面以获取响应
2025-08-19 04:13:17 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:689 | 确保在对话页面...
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-19 04:13:17 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:700 | ✅ 已在对话页面
2025-08-19 04:13:17 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:566 | ✅ 已确认在Ella对话页面，可以获取响应
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-19 04:13:17 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:843 | 检查是否在Ella页面...
2025-08-19 04:13:17 | INFO | pages.base.system_status_checker:ensure_ella_process:2013 | 检查当前进程是否是Ella...
2025-08-19 04:13:18 | INFO | pages.base.system_status_checker:ensure_ella_process:2020 | 当前应用: com.transsion.aivoiceassistant
2025-08-19 04:13:18 | INFO | pages.base.system_status_checker:ensure_ella_process:2021 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-08-19 04:13:18 | INFO | pages.base.system_status_checker:ensure_ella_process:2030 | ✅ 当前在Ella应用进程
2025-08-19 04:13:18 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:852 | ✅ 当前在Ella页面
2025-08-19 04:13:18 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-08-19 04:13:19 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:330 | asr_txt文本不符合AI响应格式: please show me where i am，已达到最大重试次数
2025-08-19 04:13:21 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | robot_text节点不存在，已达到最大重试次数
2025-08-19 04:13:22 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | function_name节点不存在，已达到最大重试次数
2025-08-19 04:13:23 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | function_control节点不存在，已达到最大重试次数
2025-08-19 04:13:24 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | tv_card_chat_gpt节点不存在，已达到最大重试次数
2025-08-19 04:13:25 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:291 | tv_top节点不存在，已达到最大重试次数
2025-08-19 04:13:25 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:158 | 尝试获取其他有效的响应文本
2025-08-19 04:13:25 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:579 | 从TextView元素获取响应
2025-08-19 04:13:27 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:595 | 查找RecyclerView中的最新消息
2025-08-19 04:13:28 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:427 | 从dump正则提取文本: 04:13 Dialogue Explore 04:13 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Swift Ticket Scam Refunded Micah: Arsenal Need £80m Signing Win in PUBG please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…
2025-08-19 04:13:28 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:170 | ✅ 获取到响应文本: 04:13 Dialogue Explore 04:13 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Swift Ticket Scam Refunded Micah: Arsenal Need £80m Signing Win in PUBG please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…
2025-08-19 04:13:28 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:175 | 未获取到有效的响应文本
2025-08-19 04:13:28 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:596 | 最终获取的AI响应: '['please show me where i am', '', '', '', '', '', "04:13 Dialogue Explore 04:13 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Swift Ticket Scam Refunded Micah: Arsenal Need £80m Signing Win in PUBG please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…"]'
2025-08-19 04:13:28 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaPleaseShowMeWhereIAm\test_completed.png
2025-08-19 04:13:28 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1373 | 🎉 please show me where i am 测试完成
2025-08-19 04:13:28 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1010 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['please show me where i am', '', '', '', '', '', "04:13 Dialogue Explore 04:13 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Swift Ticket Scam Refunded Micah: Arsenal Need £80m Signing Win in PUBG please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…"]
2025-08-19 04:13:28 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1057 | ⚠️ 响应未包含期望内容: 'Your current location:'
2025-08-19 04:13:28 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1064 | ❌ 部分期望内容未找到 (0/1)
2025-08-19 04:13:28 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1065 | 缺失内容: ['Your current location:']
2025-08-19 04:13:28 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1066 | 搜索文本: 'please show me where i am 04:13 Dialogue Explore 04:13 Hi, I'm Ella I can answer your questions, summarize content, and provide creative inspiration. Refresh Swift Ticket Scam Refunded Micah: Arsenal Need £80m Signing Win in PUBG please show me where i am Getting location failed. DeepSeek-R1 Feel free to ask me any questions…'
2025-08-19 04:13:28 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaPleaseShowMeWhereIAm\failure_test_please_show_me_where_i_am_20250819_041328.png
2025-08-19 04:13:28 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaPleaseShowMeWhereIAm\failure_test_please_show_me_where_i_am_20250819_041328.png
2025-08-19 04:13:28 | INFO | pages.apps.ella.dialogue_page:stop_app:320 | 停止Ella应用
2025-08-19 04:13:30 | INFO | pages.apps.ella.dialogue_page:stop_app:331 | ✅ Ella应用已成功停止
